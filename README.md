# 统一AI算法管理平台

## 🎯 项目概述

统一AI算法管理平台是一个下一代AI算法部署与管理解决方案，提供3步部署、零配置启动、统一管理的完整平台。让AI算法部署像安装软件一样简单。

> **详细展示文档**: 查看 [PROJECT_SHOWCASE.md](./docs/PROJECT_SHOWCASE.md) 了解完整的技术架构和核心价值

## 🚀 快速开始

### 3步部署
```bash
# 第1步：构建管理平台
./build-deployment-package.sh

# 第2步：构建算法包
./build-algorithm-packages.sh

# 第3步：启动系统
cd separated-deployment-system/platform-package/algorithm-platform-deploy-package
./run-platform.sh
```

### 访问地址
- **管理平台**: http://localhost (端口80)
- **API服务**: http://localhost:8100
- **API文档**: http://localhost:8100/docs

## 📁 项目结构

```
algorithm_platform/
├── algorithm-platform-manager/     # 管理平台源码
├── algorithms/                     # 算法项目
│   ├── _scripts/                   # 算法管理工具
│   ├── renchefei/                  # 人车非检测算法 (标准参考模板)
│   ├── wenzhou_gaokongpaowu/       # 温州高空抛物检测算法
│   ├── wenzhou_haiguanocr/         # 温州海关OCR识别算法
│   ├── wenzhou_face/               # 温州人脸识别算法
│   └── accident_classify/          # 交通事故分类算法
├── separated-deployment-system/    # 独立部署系统
├── build-deployment-package.sh     # 平台构建脚本
├── build-algorithm-packages.sh     # 算法构建脚本
├── port-registry.json              # 端口分配注册表
└── UNIFIED_AI_PLATFORM_DEVELOPMENT_GUIDE.md # 开发指南
```

## 🚀 算法包列表

### 1. 人车非检测算法 (RenCheFei Detection) - 标准参考模板
- **端口**: 8002
- **功能**: 基于 YOLOv5 的人员、车辆、非机动车检测
- **特点**: 标准化API接口，作为其他算法包的参考模板
- **API**: `GET /api/v1/health`, `POST /api/v1/detect`

### 2. 温州高空抛物检测算法 (Wenzhou High-altitude Throwing)
- **端口**: 8005
- **功能**: 高空抛物行为检测
- **特点**: GPU加速检测，高性能多路并发处理
- **API**: `GET /api/v1/health`, `POST /api/v1/detect`

### 3. 温州海关OCR识别算法 (Wenzhou Customs OCR)
- **端口**: 8006
- **功能**: 海关文档OCR识别和结构化提取
- **特点**: GPU加速OCR，支持多种文档格式
- **API**: `GET /api/v1/health`, `POST /api/v1/ocr`

### 4. 温州人脸识别算法 (Wenzhou Face Recognition)
- **端口**: 8003
- **功能**: 人脸检测、识别和质量评估
- **特点**: 高精度人脸识别，支持质量评估
- **API**: `GET /api/v1/health`, `POST /api/v1/detect`

### 5. 交通事故分类算法 (Accident Classification)
- **端口**: 8004
- **功能**: 交通事故类型自动分类
- **特点**: 多类别分类，支持严重程度判断
- **API**: `GET /api/v1/health`, `POST /api/v1/detect`

## 🎯 平台核心特性

### ⚡ 零配置，自动管理
- **自动端口分配** - 系统自动管理8002-8099端口，避免冲突
- **自动网络配置** - 统一algorithm-network，容器间无缝通信
- **自动环境检测** - GPU环境自动检测和配置
- **自动健康监控** - 实时监控算法状态，异常自动恢复

### 🧠 GPU优化架构
- **GPU加速支持** - 自动检测和使用GPU资源进行算法加速
- **CUDA环境适配** - 支持CUDA环境下的高性能计算
- **内存管理优化** - 智能管理GPU内存使用，避免资源冲突
- **性能监控** - 实时监控GPU使用率和算法性能指标

### 🎛️ 统一管理界面
- **Web管理界面** - http://localhost 一站式管理所有算法
- **实时监控** - 算法性能、资源使用、运行状态一目了然
- **集中日志** - 统一查看所有算法的运行日志
- **批量操作** - 一键启停、批量部署、集中配置

## 🛠️ 算法开发

### 使用算法管理工具
```bash
cd algorithms/_scripts
./manage-algorithms.sh
```

**主要功能**：
1. **添加新算法包** - 自动创建标准结构和模板文件
2. **删除算法包** - 完全清除算法并释放端口
3. **列出现有算法** - 显示所有算法的状态信息
4. **查看端口分配状态** - 详细的端口使用统计
5. **端口管理和清理** - 检测清理孤立端口，更新映射表
6. **构建算法包** - 集成构建功能，支持单个和批量构建

### 算法开发流程
```bash
# 1. 创建算法包
cd algorithms/_scripts
./manage-algorithms.sh  # 选择"1. 添加新算法包"

# 2. 开发算法逻辑
cd algorithms/your-algorithm
# 编辑 src/api_server.py
# 添加依赖到 pyproject.toml

# 3. 本地测试
uv sync
uv run uvicorn src.api_server:app --reload

# 4. 构建算法包
cd algorithms/_scripts
./manage-algorithms.sh  # 选择"6. 构建算法包"

# 5. 部署算法包
cd separated-deployment-system
./deploy.sh
```

## 🔧 系统要求

### 环境要求
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **uv**: Python包管理器
- **NVIDIA GPU + CUDA**: 可选，用于GPU加速算法
- **nvidia-docker2**: GPU环境必需

### 系统资源
- **内存**: 8GB+ (推荐)
- **存储**: 20GB+ (包含所有算法)
- **GPU内存**: 4GB+ (GPU加速模式)
## 📈 核心优势

### 🚀 一键部署
- **3步部署** - 30秒完成整个AI算法平台部署
- **零配置** - 自动端口分配、网络配置、环境检测
- **统一管理** - Web界面统一管理所有算法

### 🧠 GPU优化架构
- **GPU加速支持** - GPU环境下的高性能计算
- **自动环境适配** - 自动检测和配置GPU环境
- **标准化接口** - 统一API规范，简化集成开发

### 📊 企业级特性
- **容器化部署** - Docker容器隔离，易于扩展
- **实时监控** - 算法状态、性能指标、资源使用监控
- **集中日志** - 统一日志管理，便于问题排查

## 📚 文档资源

- **[PROJECT_SHOWCASE.md](./docs/PROJECT_SHOWCASE.md)** - 完整的项目展示文档
- **[AI_ALGORITHM_PRODUCT_CATALOG.md](./docs/AI_ALGORITHM_PRODUCT_CATALOG.md)** - AI算法包商品目录
- **[UNIFIED_AI_PLATFORM_DEVELOPMENT_GUIDE.md](./UNIFIED_AI_PLATFORM_DEVELOPMENT_GUIDE.md)** - 详细开发指南
- **算法包文档** - 查看各算法包目录下的README.md

## 🎉 立即体验

```bash
# 克隆项目
git clone <repository-url>
cd algorithm_platform

# 3步部署
./build-deployment-package.sh
./build-algorithm-packages.sh
cd separated-deployment-system/platform-package/algorithm-platform-deploy-package
./run-platform.sh

# 访问管理界面
open http://localhost
```

**让AI算法部署变得简单，让统一管理成为现实！** 🚀
