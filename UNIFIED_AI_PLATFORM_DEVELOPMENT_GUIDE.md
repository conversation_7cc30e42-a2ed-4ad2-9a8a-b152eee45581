# 统一AI算法管理平台开发指南

## 🚀 快速开始

### 环境要求
- Docker 20.10+
- Docker Compose 2.0+
- uv (Python包管理器)
- NVIDIA GPU + CUDA (可选，用于GPU加速算法)
- nvidia-docker2 (GPU环境必需)

### 完整部署流程
```bash
# 1. 构建管理平台
./build-deployment-package.sh

# 2. 构建算法包
./build-algorithm-packages.sh

# 3. 部署系统
cd separated-deployment-system/platform-package/algorithm-platform-deploy-package
./run-platform.sh
```

### 📋 部署前验证
```bash
# 验证构建脚本存在
ls -la build-deployment-package.sh build-algorithm-packages.sh

# 验证算法管理工具
ls -la algorithms/_scripts/manage-algorithms.sh

# 验证部署目录结构
ls -la separated-deployment-system/platform-package/algorithm-platform-deploy-package/run-platform.sh
```

### 访问地址
- **管理平台前端**: http://localhost (端口80)
- **管理平台后端API**: http://localhost:8100
- **API文档**: http://localhost:8100/docs

## 🛠️ 算法开发

### 使用算法管理工具
```bash
cd algorithms/_scripts
./manage-algorithms.sh
```

**主要功能**：
1. **添加新算法包** - 自动创建标准结构和模板文件
2. **删除算法包** - 完全清除算法并释放端口
3. **列出现有算法** - 显示所有算法的状态信息
4. **查看端口分配状态** - 详细的端口使用统计
5. **端口管理和清理** - 检测清理孤立端口，更新映射表
6. **构建算法包** - 集成构建功能，支持单个和批量构建

### 算法开发流程
```bash
# 1. 创建算法包
cd algorithms/_scripts
./manage-algorithms.sh  # 选择"1. 添加新算法包"

# 2. 开发算法逻辑
cd algorithms/your-algorithm
# 编辑 src/api_server.py
# 添加依赖到 pyproject.toml

# 3. 本地测试
uv sync
uv run uvicorn src.api_server:app --reload

# 4. 构建算法包
cd algorithms/_scripts
./manage-algorithms.sh  # 选择"6. 构建算法包"

# 5. 部署算法包
cd separated-deployment-system
./deploy.sh
```

### 算法包标准结构
```
your-algorithm/
├── Dockerfile                      # 容器构建文件
├── pyproject.toml                  # 项目配置文件
├── src/
│   ├── __init__.py
│   └── api_server.py              # FastAPI应用
└── README.md                      # 算法说明文档
```

### 智能算法包结构 (推荐)
```
smart-algorithm/
├── Dockerfile                      # 智能多阶段构建
├── pyproject.toml                  # 项目配置
├── src/
│   ├── api_server.py              # 统一API服务器
│   ├── environment_detector.py    # 环境检测器
│   └── engine_manager.py          # 智能引擎管理器
├── engines/
│   ├── python_engine.py           # CPU兼容引擎
│   ├── deepstream_engine.py       # GPU加速引擎
│   └── core/                      # 核心算法模块
├── scripts/
│   └── smart_start.sh             # 智能启动脚本
└── README.md                      # 完整文档
```

## 📋 算法包本地化标准流程

> ⚠️ **重要**: 本标准流程受到最高级别保护，详见文档末尾的保护声明。

### 标准参考模板：renchefei算法包

当执行"把XX算法包本地化"指令时，必须严格按照以下标准流程执行，以`renchefei`算法包作为标准参考模板。

### 流程概述

算法包本地化是将原始算法代码规范化为平台标准格式的过程，确保所有算法包具有统一的结构、接口和部署方式。

### 1. 标准参考模板分析

#### renchefei标准目录结构
```
algorithms/renchefei/
├── Dockerfile                 # 生产级Docker配置
├── README.md                  # 算法包说明文档
├── pyproject.toml            # Python项目配置（使用uv管理）
├── config.ini                # 算法配置文件
├── uv.lock                   # 依赖锁定文件
├── src/                      # 源代码目录
│   ├── api_server.py         # FastAPI服务器（统一v2.0格式）
│   ├── inference_engine.py   # 推理引擎
│   ├── logger_config.py      # 日志配置
│   ├── unified_models.py     # 统一响应模型
│   ├── models/               # 模型相关代码
│   └── utils/                # 工具函数
├── models/                   # 模型文件目录
│   ├── README.md
│   └── model_weights.pt
└── tests/                    # 测试文件（可选）
```

#### 标准API接口
- `GET /api/v1/health` - 健康检查（v2.0统一格式）
- `POST /api/v1/detect` - 主要检测接口
- `GET /api/v1/info` - 算法信息
- `POST /api/v1/detect/batch` - 批量检测（可选）

#### 标准配置格式
```ini
[MODEL]
input_size = 640
num_classes = 6
device = auto

[DETECTION]
confidence_threshold = 0.25
iou_threshold = 0.45
max_detections = 1000
enable_gpu = true

[OUTPUT]
save_detection_images = true
save_detection_videos = true
output_format = json
```

### 2. 核心算法完全保留原则

---
#### 🚫 绝对禁止的操作 - 严格执行

**⚠️ 以下操作在任何情况下都严格禁止，违反将导致算法包不合格**

- ❌ **修改任何原始算法的核心逻辑** - 包括检测、分类、跟踪等核心算法
- ❌ **改变算法参数的默认值或计算方式** - 保持所有原始参数和计算逻辑
- ❌ **修改模型推理流程** - 保持模型加载、推理、后处理的完整流程
- ❌ **改变算法的精度或性能特性** - 不得降低或改变算法性能
- ❌ **删除或简化原有的处理步骤** - 保持所有原始处理步骤完整
- ❌ **修改核心数据结构** - 保持原始数据格式和结构
- ❌ **改变核心依赖库版本** - 保持关键依赖库的版本一致性

**🔒 核心算法保护级别：最高级别 - 零容忍修改**
---

#### 必须保留的内容
- ✅ 所有原始算法的处理逻辑
- ✅ 原有的模型加载和推理代码
- ✅ 原始的数据预处理和后处理流程
- ✅ 所有算法特有的参数和配置
- ✅ 原有的精度和性能特性

#### 允许的操作
- ✅ 在外层添加API封装
- ✅ 添加统一的响应格式转换
- ✅ 添加日志记录和错误处理
- ✅ 添加配置文件管理
- ✅ 添加Docker化部署支持

### 3. 标准化改造要求

#### 3.1 Docker化部署
```dockerfile
# 标准Dockerfile模板（支持GPU自适应）
# 基础镜像选择策略：
# - 生产环境：python:3.11-slim（自动检测GPU环境）
# - GPU开发环境：nvidia/cuda:11.8-devel-ubuntu20.04
FROM python:3.11-slim

ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    curl \
    # GPU支持库（可选安装）
    && if command -v nvidia-smi >/dev/null 2>&1; then \
        echo "检测到GPU环境，安装GPU支持库"; \
    fi \
    && rm -rf /var/lib/apt/lists/*

# 安装uv包管理器
RUN pip install --no-cache-dir uv

# 复制项目配置文件
COPY pyproject.toml uv.lock ./

# 安装Python依赖
RUN uv sync --frozen --no-dev

# 创建必要的目录
RUN mkdir -p /app/data/input /app/data/output /app/logs

# 复制源代码和配置
COPY src/ ./src/
COPY models/ ./models/
COPY config.ini ./

# 创建非root用户
RUN useradd -m -u 1000 appuser && \
    chown -R appuser:appuser /app

USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=15s --retries=3 \
    CMD curl -f http://localhost:8002/api/v1/health || exit 1

EXPOSE 8002

CMD ["uv", "run", "uvicorn", "src.api_server:app", "--host", "0.0.0.0", "--port", "8002"]
```

#### 3.2 API标准化
必须实现以下标准接口：

```python
# 健康检查接口（v2.0格式）
@app.get("/api/v1/health", response_model=UnifiedResponse)
async def health_check():
    """健康检查接口 - v2.0格式"""
    # 实现标准健康检查逻辑

# 主要检测接口
@app.post("/api/v1/detect", response_model=UnifiedResponse)
async def detect(file: UploadFile = File(...)):
    """主要检测接口"""
    # 实现检测逻辑，保持原始算法不变

# 算法信息接口
@app.get("/api/v1/info", response_model=UnifiedResponse)
async def get_algorithm_info():
    """获取算法信息"""
    # 返回算法基本信息
```

#### 3.3 统一响应格式
必须使用v2.0统一响应格式：

```python
class UnifiedResponse(BaseModel):
    """统一响应格式 - v2.0规范"""
    success: bool = Field(..., description="是否成功执行")
    error: Optional[ErrorInfo] = Field(None, description="错误信息，成功时为null")
    data: Optional[Union[DetectionData, Dict[str, Any]]] = Field(None, description="核心业务数据，失败时为null")
    metadata: Metadata = Field(..., description="处理元数据和诊断信息")
```

### 4. 技术规范要求

#### 4.1 框架和工具
- **Web框架**：FastAPI
- **包管理**：uv（统一使用）
- **配置管理**：configparser + INI格式
- **日志系统**：Python logging + 自定义配置
- **容器化**：Docker + 非root用户

#### 4.2 环境支持
- **GPU支持**：自动检测GPU环境
- **网络集成**：algorithm-network
- **端口管理**：使用端口注册表自动分配
- **健康检查**：标准HTTP健康检查

#### 4.3 错误处理和日志
```python
# 标准日志配置
from logger_config import get_logger
logger = get_logger()

# 统一错误处理
try:
    # 算法处理逻辑
    result = original_algorithm_function(input_data)
    return create_success_response(result, metadata)
except Exception as e:
    logger.error(f"处理失败: {e}")
    return create_error_response("PROCESSING_ERROR", str(e))
```

### 5. 质量保证流程

#### 5.1 构建系统兼容性
- ✅ 确保算法包能被`build-algorithm-packages.sh`正确识别
- ✅ 验证Dockerfile能成功构建
- ✅ 确认端口分配正确

#### 5.2 API功能完整性
- ✅ 所有标准接口正常响应
- ✅ 响应格式符合v2.0规范
- ✅ 错误处理完整

#### 5.3 Docker运行验证
- ✅ 容器能成功启动
- ✅ 健康检查通过
- ✅ 网络连接正常

### 6. 本地化执行检查清单

#### 准备阶段
- [ ] 分析原始算法的目录结构和核心文件
- [ ] 识别核心算法逻辑和关键函数
- [ ] 确定算法的输入输出格式
- [ ] 了解算法的配置参数和依赖

#### 结构创建阶段
- [ ] 按照renchefei模板创建标准目录结构
- [ ] 创建pyproject.toml（使用uv管理依赖）
- [ ] 创建config.ini配置文件
- [ ] 创建标准Dockerfile

#### 代码迁移阶段
- [ ] 将原始算法代码封装为inference_engine.py
- [ ] 创建api_server.py（基于renchefei模板）
- [ ] 实现unified_models.py（v2.0响应格式）
- [ ] 配置logger_config.py

#### 功能验证阶段
- [ ] 验证原始算法逻辑完全保留
- [ ] 测试所有API接口功能
- [ ] 验证Docker构建和运行
- [ ] 确认网络连接和端口分配

#### 集成测试阶段
- [ ] 使用构建脚本验证集成
- [ ] 测试与管理平台的连接
- [ ] 验证健康检查和监控
- [ ] 确认日志记录正常

### 7. 常见问题和解决方案

#### 问题1：原始算法依赖复杂
**解决方案**：
- 仔细分析依赖关系，在pyproject.toml中准确声明
- 使用uv lock锁定依赖版本
- 在Dockerfile中安装必要的系统依赖

#### 问题2：算法接口不统一
**解决方案**：
- 创建适配层，将原始接口转换为标准格式
- 保持原始函数不变，在外层进行格式转换
- 使用unified_models.py统一响应格式

#### 问题3：性能要求高
**解决方案**：
- 保持原始算法的所有优化
- 添加GPU自动检测和使用
- 实现批量处理接口（可选）

### 8. 本地化完成标准

一个算法包本地化完成的标准：

#### 功能标准
- ✅ 所有原始算法功能完全保留
- ✅ API接口符合平台规范
- ✅ 响应格式统一（v2.0）
- ✅ 配置管理标准化

#### 技术标准
- ✅ Docker镜像成功构建和运行
- ✅ 健康检查正常
- ✅ 日志记录完整
- ✅ 错误处理健全

#### 集成标准
- ✅ 构建系统正确识别
- ✅ 端口自动分配
- ✅ 网络连接正常
- ✅ 与管理平台兼容

### 9. 示例：完整本地化流程

当收到"把wenzhou_traffic算法包本地化"指令时：

#### 1. 分析原始算法
```bash
# 检查原始算法结构
ls -la row/wenzhou_traffic/
# 分析核心算法文件
cat row/wenzhou_traffic/main.py
```

#### 2. 创建标准结构
```bash
mkdir -p algorithms/wenzhou_traffic/{src/{models,utils},models,tests}
cp algorithms/renchefei/pyproject.toml algorithms/wenzhou_traffic/
cp algorithms/renchefei/config.ini algorithms/wenzhou_traffic/
cp algorithms/renchefei/Dockerfile algorithms/wenzhou_traffic/
```

#### 3. 迁移核心算法
```python
# 将原始算法封装为inference_engine.py
class WenzhouTrafficEngine:
    def __init__(self):
        # 保持原始初始化逻辑
        self.model = load_original_model()

    def detect(self, image):
        # 保持原始检测逻辑
        return original_detect_function(image)
```

#### 4. 创建API服务
```python
# 基于renchefei模板创建api_server.py
from inference_engine import WenzhouTrafficEngine
from unified_models import UnifiedResponse, create_success_response

@app.post("/api/v1/detect", response_model=UnifiedResponse)
async def detect(file: UploadFile = File(...)):
    # 实现标准API接口，调用原始算法
    result = engine.detect(image)
    return create_success_response(result, metadata)
```

#### 5. 验证和测试
```bash
# 构建Docker镜像
cd algorithms/wenzhou_traffic
docker build -t wenzhou_traffic:1.0.0 .

# 运行测试
docker run --network algorithm-network -p 8007:8007 wenzhou_traffic:1.0.0

# 测试API接口
curl http://localhost:8007/api/v1/health
```

### 10. 本地化流程总结

通过严格遵循此标准流程，确保所有算法包具有：

#### 统一性
- 相同的目录结构和文件组织
- 统一的API接口设计
- 一致的响应格式和错误处理
- 标准化的配置管理

#### 兼容性
- 与构建系统完全兼容
- 与管理平台无缝集成
- 与Docker网络正确连接
- 与端口管理系统协调

#### 可靠性
- 原始算法功能完全保留
- 完整的错误处理和日志记录
- 标准的健康检查和监控
- 稳定的Docker化部署

**重要提醒**：执行"把XX算法包本地化"时，必须严格按照此流程操作，以renchefei为标准参考模板，确保算法包的质量和一致性。

---
## 🔒 文档保护声明

### ⚠️ 严禁修改本标准流程

**本算法包本地化标准流程为平台核心规范，任何AI助手或开发人员都严禁修改以下内容：**

#### 🚫 绝对禁止修改的内容
- ❌ **标准参考模板** - renchefei算法包作为标准模板不可更改
- ❌ **核心算法保留原则** - 核心算法完全保留的原则不可修改
- ❌ **标准化改造要求** - Docker化、API标准化等技术要求不可修改
- ❌ **技术规范要求** - 框架选择、工具使用、环境支持等规范不可修改
- ❌ **质量保证流程** - 构建兼容性、API完整性等质量标准不可修改
- ❌ **执行检查清单** - 本地化执行的检查清单不可修改
- ❌ **完成标准** - 本地化完成的判定标准不可修改

#### ⚠️ 修改后果警告
- 破坏平台统一性和兼容性
- 导致算法包部署失败
- 影响系统稳定性和可靠性
- 造成维护困难和技术债务

#### 🔐 保护级别
**最高级别保护 - 零容忍修改政策**

任何对本标准流程的修改都将被视为违反平台核心规范，必须立即恢复到原始状态。

---

**📋 本标准流程最后更新时间：2025-01-01**
**🔒 保护状态：已锁定，禁止修改**
**📝 维护责任：平台架构团队**

### 必需API接口
```python
from fastapi import FastAPI, File, UploadFile, HTTPException
from src.unified_models import UnifiedResponse, create_success_response, create_error_response
import time

app = FastAPI()

@app.get("/api/v1/health", response_model=UnifiedResponse)
async def health_check():
    """健康检查接口 - v2.0统一格式"""
    try:
        health_data = {
            "status": "healthy",
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%S"),
            "version": "1.0.0",
            "uptime_seconds": 3600.5
        }

        metadata = {
            "request_id": f"health_{int(time.time() * 1000)}",
            "processing_time_ms": 1.0,
            "algorithm_name": "your-algorithm",
            "api_version": "v1"
        }

        return create_success_response(health_data, metadata)

    except Exception as e:
        return create_error_response("HEALTH_CHECK_ERROR", str(e))

@app.post("/api/v1/detect", response_model=UnifiedResponse)
async def detect(file: UploadFile = File(...)):
    """主要检测接口 - v2.0统一格式"""
    try:
        # 检测算法逻辑（保持原始算法不变）
        detection_result = {
            "version": "1.0",
            "events": {
                "objects": [],
                "timestamp": int(time.time() * 1000)
            }
        }

        metadata = {
            "request_id": f"detect_{int(time.time() * 1000)}",
            "processing_time_ms": 45.2,
            "algorithm_name": "your-algorithm",
            "api_version": "v1",
            "input_info": {
                "filename": file.filename,
                "content_type": file.content_type
            }
        }

        return create_success_response(detection_result, metadata)

    except Exception as e:
        return create_error_response("DETECTION_ERROR", str(e))
```

## 🏗️ 系统架构

### 项目结构
```
algorithm_platform/
├── algorithm-platform-manager/     # 管理平台源码
├── algorithms/                     # 算法项目
│   ├── _scripts/                   # 算法管理工具
│   │   ├── manage-algorithms.sh    # 主管理脚本
│   │   └── cleanup-orphaned-ports.py # 端口清理工具
│   ├── _standards/                 # 开发规范文档
│   ├── renchefei/                  # 人车非检测算法 (标准参考模板)
│   ├── wenzhou_gaokongpaowu/       # 温州高空抛物检测算法 (智能版本)
│   ├── wenzhou_haiguanocr/         # 温州海关OCR识别算法
│   ├── wenzhou_trash_detect/       # 温州垃圾检测算法
│   └── [other-algorithms]/         # 其他算法包目录
├── separated-deployment-system/    # 独立部署系统
│   └── platform-package/          # 平台部署包
│       └── algorithm-platform-deploy-package/
├── build-deployment-package.sh     # 平台构建脚本
├── build-algorithm-packages.sh     # 算法构建脚本
├── port-registry.json              # 端口分配注册表
└── UNIFIED_AI_PLATFORM_DEVELOPMENT_GUIDE.md # 开发指南
```

### 智能算法包架构
```
智能算法包 (wenzhou_gaokongpaowu)
├── 🧠 环境检测器
│   ├── GPU检测 (nvidia-smi)
│   ├── CUDA检测 (nvcc, 库文件)
│   ├── DeepStream检测 (SDK安装)
│   └── Docker GPU支持检测
├── ⚡ 双引擎架构
│   ├── DeepStream引擎 (GPU高性能: 60+ FPS, 15路并发)
│   └── Python引擎 (CPU备用: 30 FPS, 1路处理)
├── 🚀 智能引擎管理器
│   ├── 自动模式选择
│   ├── 故障自动恢复
│   └── 运行时模式切换
└── 📡 统一API接口
    ├── GET /api/v1/health (增强版)
    ├── POST /api/v1/detect (统一接口)
    ├── GET /api/v1/mode (模式信息)
    └── POST /api/v1/mode/switch (模式切换)

OCR算法包 (wenzhou_haiguanocr)
├── 🧠 环境检测器
│   ├── GPU检测 (nvidia-smi)
│   ├── CUDA检测 (nvcc)
│   ├── Triton检测 (推理服务器)
│   └── PaddleOCR检测
├── ⚡ 双引擎架构
│   ├── Triton引擎 (GPU高性能: 100ms/页, 50+并发)
│   └── PaddleOCR引擎 (CPU备用: 500ms/页, 10并发)
├── 🚀 智能引擎管理器
│   ├── 自动模式选择
│   ├── 故障自动恢复
│   └── 运行时模式切换
└── 📡 统一API接口
    ├── GET /api/v1/health (增强版)
    ├── POST /api/v1/ocr (OCR识别)
    ├── GET /api/v1/mode (模式信息)
    └── POST /api/v1/mode/switch (模式切换)
```

### 网络配置
- 统一网络: `algorithm-network`
- 统一项目: `algorithm-platform`
- 管理平台: 端口80 (前端), 8100 (后端API)
- 算法容器: 端口8002-8099 (动态分配)
- Redis数据库: 端口6379 (内部)

## 🔌 端口管理系统

### 端口分配规范
- **端口范围**: 8002-8099 (98个可用端口)
- **管理平台端口**: 80 (前端), 8100 (后端API)
- **预留端口**: 8100 (管理平台后端)
- **分配策略**: 自动分配下一个可用端口

### 当前端口分配状态
**实时查看端口分配**：
```bash
cd algorithms/_scripts
./manage-algorithms.sh
# 选择 "4. 查看端口分配状态"
```

**端口分配示例** (实际分配请查看port-registry.json)：
- **8002**: renchefei (人车非检测算法 - 标准参考模板)
- **8003**: wenzhou_face (温州人脸识别算法)
- **8004**: accident_classify (交通事故分类算法)
- **8005**: wenzhou_gaokongpaowu (温州高空抛物检测算法 - 智能版本)
- **8006**: wenzhou_haiguanocr (温州海关OCR识别算法)

**直接查看端口注册表**：
```bash
cat port-registry.json
```

**📋 端口注册表格式说明**:
```json
{
  "version": "1.0",
  "port_range": {"start": 8002, "end": 8100, "reserved_ports": [8100]},
  "allocated_ports": {
    "8002": {
      "algorithm": "renchefei",
      "display_name": "人车非检测算法",
      "allocated_at": "2025-01-01T00:00:00Z",
      "status": "active"
    }
  },
  "next_available_port": 8007,
  "metadata": {"total_allocated": 5}
}
```

### 智能算法包端口特性

#### 温州高空抛物检测算法 (端口8005)
- **自动环境检测**: 启动时检测GPU/CUDA/DeepStream环境
- **智能模式选择**: 根据环境自动选择DeepStream或Python模式
- **双引擎架构**: 一个端口支持两种执行模式
- **运行时切换**: 支持通过API手动切换模式
- **故障恢复**: DeepStream失败时自动降级到Python模式

#### 温州海关OCR识别算法 (端口8006)
- **自动环境检测**: 启动时检测GPU/CUDA/Triton/PaddleOCR环境
- **智能模式选择**: 根据环境自动选择Triton或PaddleOCR模式
- **双引擎架构**: 一个端口支持两种OCR引擎
- **运行时切换**: 支持通过API手动切换OCR引擎
- **故障恢复**: Triton失败时自动降级到PaddleOCR模式

### 端口管理操作
```bash
cd algorithms/_scripts
./manage-algorithms.sh
# 选择"5. 端口管理和清理"
```

### 端口注册表文件
- **位置**: `port-registry.json` (项目根目录)
- **格式**: JSON格式，包含端口分配、元数据和统计信息
- **更新**: 算法添加/删除时自动更新
- **查看**: 使用管理脚本或直接读取文件

## 📦 构建和部署

### 构建流程
```bash
# 1. 构建管理平台
./build-deployment-package.sh

# 2. 构建算法包
./build-algorithm-packages.sh

# 3. 部署系统
cd separated-deployment-system/platform-package/algorithm-platform-deploy-package
./run-platform.sh
```

### 部署包结构
```
separated-deployment-system/
├── algorithm-packages/              # 算法包目录
│   ├── {name}-algorithm-package.tar.gz
│   └── {name}-manifest.json
├── platform-package/               # 平台包目录
│   └── algorithm-platform-deploy-package/
└── deploy.sh                       # 部署脚本
```

## 📦 清单文件格式 (v2.0)

### 标准清单文件结构
```json
{
  "manifest_version": "2.0",
  "generated_at": "2025-01-01T00:00:00Z",
  "algorithm": {
    "name": "algorithm-name",
    "display_name": "算法显示名称",
    "version": "1.0.0",
    "port": 8002,
    "type": "docker-compose"
  },
  "package": {
    "file": "algorithm-name-algorithm-package.tar.gz",
    "format": "tar.gz",
    "size": "705M",
    "size_bytes": 727923313,
    "created_at": "2025-01-01T00:00:00Z"
  },
  "deployment": {
    "network": "algorithm-network",
    "health_check": "/api/v1/health",
    "startup_timeout": 60,
    "container_name": "algorithm-name-1.0.0",
    "project_name": "algorithm-platform"
  },
  "requirements": {
    "docker_version": "20.10+",
    "memory_mb": 4096,
    "disk_mb": 2048
  },
  "metadata": {
    "build_system": "algorithm-platform-builder",
    "isolation": "independent"
  }
}
```

## 📡 API参考

### 管理平台API
- `GET /api/health` - 平台健康检查
- `GET /api/v1/containers` - 容器列表
- `GET /api/v1/benchmark/{container_id}/result` - 获取基准测试结果
- `POST /api/v1/benchmark/{container_id}/with-frames` - 运行性能基准测试
- `POST /api/v1/benchmark/fps/{container_id}/apply` - 应用FPS设置
- `GET /api/v1/benchmark/fps/{container_id}` - 获取FPS配置
- `GET /api/v1/benchmark/fps/configs/all` - 获取所有FPS配置
- `ws://localhost:8100/ws` - WebSocket通信

### 算法API标准
- `GET /api/v1/health` - 健康检查 (必需)
- `POST /api/v1/detect` - 检测接口 (视觉算法)
- `POST /api/v1/ocr` - OCR识别接口 (OCR算法)
- `GET /api/v1/info` - 算法信息接口
- `GET /api/v1/mode` - 模式查询接口 (智能算法)
- `POST /api/v1/mode/switch` - 模式切换接口 (智能算法)

### 智能算法包API

#### 温州高空抛物检测算法 (wenzhou_gaokongpaowu)

##### 增强健康检查
```http
GET /api/v1/health
```
**响应示例**：
```json
{
  "status": "healthy",
  "timestamp": "2025-01-01T12:00:00",
  "version": "1.0.0",
  "uptime_seconds": 3600.5,
  "current_mode": "deepstream",
  "mode_info": {
    "name": "DeepStream高性能模式",
    "performance": "高性能",
    "concurrent_streams": 15,
    "gpu_acceleration": true,
    "processing_fps": "60+"
  },
  "environment": {
    "has_nvidia_gpu": true,
    "has_cuda": true,
    "has_deepstream": true,
    "gpu_memory_mb": 8192,
    "recommended_mode": "deepstream"
  }
}
```

##### 统一检测接口
```http
POST /api/v1/detect
Content-Type: multipart/form-data

file: <image_file>
camera_id: "camera_001"
task_id: "142"
sensitivity: 0.8
```
**响应示例**：
```json
{
  "version": "1.0",
  "task": {
    "model_name": "High-altitude-throwing",
    "task_id": "142"
  },
  "events": {
    "camera_id": "camera_001",
    "timestamp": 1704110400000,
    "objects": [
      {
        "object_label": "High-altitude-throwing",
        "track_id": "1",
        "object_box": {
          "x": 0.653,
          "y": 0.175,
          "width": 0.025,
          "height": 0.028
        }
      }
    ]
  },
  "processing_time_ms": 45.2,
  "engine_info": {
    "engine": "deepstream",
    "mode": "高性能模式",
    "gpu_accelerated": true
  }
}
```

##### 模式管理接口
```http
GET /api/v1/mode
```
**响应**：当前模式信息和可用模式列表

```http
POST /api/v1/mode/switch
Content-Type: application/json

{
  "target_mode": "python"
}
```
**功能**：手动切换执行模式

#### 温州海关OCR识别算法 (wenzhou_haiguanocr)

##### OCR识别接口
```http
POST /api/v1/ocr
Content-Type: multipart/form-data

file: <document_file>
```
**支持格式**: JPG, PNG, BMP, TIFF, PDF
**响应示例**：
```json
{
  "success": true,
  "engine": "triton",
  "processing_time_ms": 150.5,
  "result": {
    "text_regions": [...],
    "recognized_text": "识别的文本内容",
    "confidence": 0.95,
    "structured_data": {
      "extracted_fields": {...},
      "table_data": [...],
      "confidence": 0.92
    }
  },
  "image_info": {
    "width": 1024,
    "height": 768,
    "channels": 3
  }
}
```

## 🎯 FPS性能优化系统

### FPS自动调节架构

统一AI算法管理平台实现了智能FPS性能优化系统，确保算法包在最佳性能和系统稳定性之间取得平衡。

#### 核心原理
- **最大安全FPS**: 基于最长处理时间 + 30%安全边际计算
- **建议FPS**: 最大安全FPS的85%，确保稳定运行
- **保守FPS**: 最大安全FPS的70%，提供最高安全保障
- **逻辑关系**: 最大安全FPS ≥ 建议FPS ≥ 保守FPS

#### FPS计算公式
```python
# 最大安全FPS计算
safe_interval = max_processing_time * 1.3
max_safe_fps = 1000 / safe_interval

# 建议FPS计算（基于最大安全FPS）
recommended_fps = max_safe_fps * 0.85

# 保守FPS计算（更加保守）
conservative_fps = max_safe_fps * 0.7
```

#### 自动FPS应用流程
1. **性能基准测试**: 使用真实视频帧测试算法处理能力
2. **FPS计算**: 基于处理时间统计计算安全FPS值
3. **安全验证**: 确保所有FPS值符合逻辑关系
4. **自动应用**: 系统自动应用保守FPS设置
5. **持久化存储**: FPS配置保存到平台数据库

#### 使用方法
```bash
# 通过Web界面运行性能测试
1. 访问 http://localhost:80
2. 进入"算法测试"页面
3. 选择目标算法包
4. 点击"开始性能测试"
5. 系统自动计算并应用最佳FPS设置

# 手动应用FPS设置
curl -X POST http://localhost:8100/api/v1/benchmark/fps/{container_id}/apply \
  -H "Content-Type: application/json" \
  -d '{"target_fps": 2.4}'

# 查看FPS配置
curl http://localhost:8100/api/v1/benchmark/fps/{container_id}
```

## 🔍 故障排除

### 常见问题
```bash
# 检查容器状态
docker ps -a

# 查看容器日志
docker logs [container-name]

# 检查网络连接
docker network inspect algorithm-network

# 重启管理平台服务
cd algorithm-platform-manager
docker-compose down && docker-compose up -d
```

### 智能算法包故障排除

#### GPU环境问题
```bash
# 检查GPU状态
nvidia-smi

# 检查CUDA环境
nvcc --version

# 检查DeepStream安装
ls -la /opt/nvidia/deepstream/deepstream/

# 检查Docker GPU支持
docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu20.04 nvidia-smi
```

#### 高空抛物检测算法问题
```bash
# 查看当前模式
curl http://localhost:8005/api/v1/mode

# 查看详细健康状态
curl http://localhost:8005/api/v1/health

# 手动切换到Python模式
curl -X POST http://localhost:8005/api/v1/mode/switch \
  -H "Content-Type: application/json" \
  -d '{"target_mode": "python"}'
```

#### OCR识别算法问题
```bash
# 查看OCR算法状态
curl http://localhost:8006/api/v1/health

# 查看当前OCR模式
curl http://localhost:8006/api/v1/mode

# 手动切换到PaddleOCR模式
curl -X POST http://localhost:8006/api/v1/mode/switch \
  -H "Content-Type: application/json" \
  -d '{"target_mode": "paddleocr"}'
```

#### 性能问题诊断
- **DeepStream模式慢**: 检查GPU内存使用，确保有足够的GPU内存
- **Python模式慢**: 检查CPU使用率，考虑优化图像处理参数
- **Triton模式慢**: 检查Triton服务器状态和模型加载情况
- **PaddleOCR模式慢**: 检查CPU/GPU使用率，优化批处理参数
- **频繁模式切换**: 检查环境稳定性，可能需要固定模式

### FPS性能问题排除

#### FPS计算异常
```bash
# 检查基准测试结果
curl http://localhost:8100/api/v1/benchmark/{container_id}/result

# 重新运行性能测试
curl -X POST http://localhost:8100/api/v1/benchmark/{container_id}/with-frames \
  -H "Content-Type: application/json" \
  -d '{"video_frames": [...], "parameters": {}}'
```

#### FPS设置问题
- **建议FPS > 最大安全FPS**: 检查FPS计算逻辑，确保使用修复后的算法
- **请求堆积**: 降低FPS设置，使用保守FPS值
- **处理延迟**: 检查算法包性能，考虑优化或增加资源

#### 自动FPS应用失败
```bash
# 检查FPS配置存储
curl http://localhost:8100/api/v1/benchmark/fps/configs/all

# 手动应用FPS设置
curl -X POST http://localhost:8100/api/v1/benchmark/fps/{container_id}/apply \
  -H "Content-Type: application/json" \
  -d '{"target_fps": 2.4}'
```

### 端口冲突处理
```bash
cd algorithms/_scripts
./manage-algorithms.sh
# 选择"5. 端口管理和清理"
```

### 网络配置问题
```bash
# 检查网络配置
docker network ls | grep algorithm-network

# 重新创建网络（如果需要）
docker network rm algorithm-network
docker network create algorithm-network

# 修复docker-compose网络配置
# 在docker-compose.yml中设置 external: true
```

### 清理临时文件
```bash
# 自动清理（推荐）
./build-algorithm-packages.sh  # 启动时会提示清理

# 手动清理
rm -rf temp-build-*
rm -rf separated-deployment-system/temp-deployment-*
```

## 📚 开发规范

### 算法包开发标准
- 查阅: `algorithms/_standards/development-standards.md`
- 兼容性验证: `algorithms/_standards/compatibility-verification.md`
- 端口管理: `algorithms/_standards/port-mapping.md`

### 智能算法包开发指南

#### 环境检测器实现
```python
from src.environment_detector import EnvironmentDetector

detector = EnvironmentDetector()
env_info = detector.detect_environment()

# 检查推荐模式
if env_info.recommended_mode == "deepstream":
    # 使用GPU加速模式
    pass
else:
    # 使用CPU兼容模式
    pass
```

#### 双引擎架构设计
- **抽象引擎接口**: 统一的检测引擎基类
- **DeepStream引擎**: GPU硬件加速实现
- **Python引擎**: CPU兼容实现
- **智能管理器**: 自动选择和切换引擎

#### 智能启动脚本
- 启动时环境检测和日志输出
- 自动设置环境变量和路径
- 彩色日志显示和进度提示
- 错误处理和降级机制

### 代码质量要求
- 使用uv进行依赖管理
- 遵循FastAPI最佳实践
- 实现必需的健康检查接口
- 提供完整的README文档
- 支持环境自适应和智能切换
- 实现故障恢复和性能监控

### 部署最佳实践

#### GPU环境部署
```bash
# 构建支持GPU的镜像
docker build -t algorithm:gpu .

# 运行时启用GPU支持
docker run --gpus all -p 8005:8005 algorithm:gpu
```

#### CPU环境部署
```bash
# 同样的镜像，自动适配CPU环境
docker run -p 8005:8005 algorithm:gpu
```

#### 开发环境设置
```bash
# 使用uv管理虚拟环境
cd algorithms/your-algorithm
uv sync
uv run uvicorn src.api_server:app --reload --port 8005
```

---

**版本**: 5.0
**更新时间**: 2025-08-05
**维护**: 统一AI算法管理平台团队
**重大更新**:
- 新增FPS性能优化系统和自动调节机制
- 修复FPS计算逻辑，确保正确的安全关系
- 完善性能基准测试和FPS管理API
- 优化前端FPS显示和应用逻辑
- 增强网络配置和故障排除指南
- 新增算法包本地化标准流程 (以renchefei为标准参考模板)
- 完善智能算法包架构和双引擎系统


