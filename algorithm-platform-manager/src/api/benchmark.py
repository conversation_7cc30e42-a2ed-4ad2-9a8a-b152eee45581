"""
算法性能基准测试模块
用于测试算法处理速度，为实时检测提供最优帧率建议
"""

import asyncio
import time
import statistics
import json
import logging
import base64
import os
from typing import List, Dict, Any
from io import BytesIO

import aiohttp
from PIL import Image
from fastapi import APIRouter, HTTPException

from src.core.docker_manager import DockerManager

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

# Docker管理器实例
docker_manager = DockerManager()


def build_algorithm_api_url(container_name: str, port: str, endpoint: str = "") -> str:
    """
    构建算法API URL，自动检测运行环境

    Args:
        container_name: 容器名称
        port: 端口号
        endpoint: API端点

    Returns:
        完整的API URL
    """
    # 检查是否在Docker容器中运行
    if os.path.exists('/.dockerenv') or os.environ.get('DOCKER_CONTAINER'):
        # 在容器中运行，使用容器名和内部端口
        return f"http://{container_name}:8000{endpoint}"
    else:
        # 在宿主机运行，使用localhost和映射端口
        return f"http://localhost:{port}{endpoint}"

class AlgorithmBenchmark:
    """算法性能基准测试类"""
    
    def __init__(self):
        self.test_images = []
        self.results = {}
        self.fps_configs = {}  # 存储FPS配置
    
    def generate_test_images(self, count: int = 10) -> List[bytes]:
        """
        生成测试图片
        
        Args:
            count: 生成图片数量
            
        Returns:
            图片字节数据列表
        """
        test_images = []
        
        # 生成不同尺寸的测试图片
        sizes = [(640, 480), (800, 600), (1024, 768), (1280, 720)]
        colors = ['red', 'green', 'blue', 'yellow', 'purple']
        
        for i in range(count):
            # 循环使用不同尺寸和颜色
            size = sizes[i % len(sizes)]
            color = colors[i % len(colors)]
            
            # 创建测试图片
            img = Image.new('RGB', size, color)
            
            # 转换为字节数据
            img_buffer = BytesIO()
            img.save(img_buffer, format='JPEG', quality=85)
            img_data = img_buffer.getvalue()
            
            test_images.append(img_data)
            
        return test_images
    
    async def test_algorithm_performance_with_frames(
        self,
        container_id: str,
        video_frames: List[bytes],
        parameters: Dict[str, Any] = None,
        auto_apply_fps: bool = True
    ) -> Dict[str, Any]:
        """
        使用真实视频帧测试算法性能

        Args:
            container_id: 容器ID
            video_frames: 真实视频帧列表
            parameters: 检测参数

        Returns:
            性能测试结果
        """
        if parameters is None:
            parameters = {
                "extract_features": False,
                "assess_quality": False,
                "confidence_threshold": 0.7,
                "max_num_faces": 0,
                "nms_threshold": 0.4
            }

        test_count = len(video_frames)
        
        try:
            # 获取容器信息
            containers = await docker_manager.list_algorithm_containers()
            container = None
            for c in containers:
                if c['name'] == container_id:
                    container = c
                    break
            
            if not container:
                raise HTTPException(status_code=404, detail=f"容器 {container_id} 未找到")
            
            if container['status'] != 'running':
                raise HTTPException(status_code=400, detail=f"容器 {container_id} 未运行")
            
            # 获取容器端口
            ports = container.get('ports', {})
            port = None
            for container_port, host_port in ports.items():
                if container_port.startswith('8000'):
                    port = host_port
                    break
            
            if not port:
                raise HTTPException(status_code=400, detail=f"容器 {container_id} 端口未配置")
            
            # 构建API URL
            api_url = build_algorithm_api_url(container_id, port, "/api/v1/detect")

            # 执行性能测试
            logger.info(f"开始使用 {test_count} 帧真实视频测试算法 {container_id} 的性能...")
            processing_times = []
            successful_tests = 0
            failed_tests = 0

            async with aiohttp.ClientSession() as session:
                for i, img_data in enumerate(video_frames):
                    try:
                        # 记录开始时间
                        start_time = time.time()
                        
                        # 准备表单数据
                        data = aiohttp.FormData()
                        data.add_field('file', img_data, filename=f'test_{i}.jpg', content_type='image/jpeg')
                        data.add_field('parameters', json.dumps(parameters))
                        
                        # 发送请求
                        async with session.post(
                            api_url, 
                            data=data, 
                            timeout=aiohttp.ClientTimeout(total=10)
                        ) as response:
                            
                            # 记录结束时间
                            end_time = time.time()
                            processing_time = (end_time - start_time) * 1000  # 转换为毫秒
                            
                            if response.status == 200:
                                result = await response.json()
                                processing_times.append(processing_time)
                                successful_tests += 1
                                logger.info(f"测试 {i+1}/{test_count}: {processing_time:.2f}ms - 成功")
                            else:
                                failed_tests += 1
                                logger.warning(f"测试 {i+1}/{test_count}: HTTP {response.status} - 失败")
                                
                    except asyncio.TimeoutError:
                        failed_tests += 1
                        logger.warning(f"测试 {i+1}/{test_count}: 超时 - 失败")
                    except Exception as e:
                        failed_tests += 1
                        logger.error(f"测试 {i+1}/{test_count}: {str(e)} - 失败")
            
            # 计算统计数据
            if processing_times:
                avg_time = statistics.mean(processing_times)
                min_time = min(processing_times)
                max_time = max(processing_times)
                median_time = statistics.median(processing_times)
                
                # 计算最大安全FPS（基于最长处理时间 + 30%安全边际）
                safe_interval = max_time * 1.3
                max_safe_fps = 1000 / safe_interval if safe_interval > 0 else 1

                # 计算建议FPS（基于最大安全FPS的85%，确保不超过安全阈值）
                recommended_fps = max_safe_fps * 0.85

                # 计算保守FPS（基于最大安全FPS的70%，更加保守）
                conservative_fps = max_safe_fps * 0.7

                # 确保FPS值的逻辑关系正确：max_safe_fps >= recommended_fps >= conservative_fps
                if recommended_fps > max_safe_fps:
                    logger.warning(f"建议FPS({recommended_fps:.1f})超过最大安全FPS({max_safe_fps:.1f})，已自动调整")
                    recommended_fps = max_safe_fps * 0.85

                if conservative_fps > recommended_fps:
                    logger.warning(f"保守FPS({conservative_fps:.1f})超过建议FPS({recommended_fps:.1f})，已自动调整")
                    conservative_fps = recommended_fps * 0.8

                # 最终验证：确保所有FPS值都在合理范围内且符合逻辑关系
                if not (max_safe_fps >= recommended_fps >= conservative_fps):
                    logger.error(f"FPS逻辑关系错误，重新计算")
                    recommended_fps = max_safe_fps * 0.85
                    conservative_fps = max_safe_fps * 0.7
                
                result = {
                    "container_id": container_id,
                    "test_count": test_count,
                    "successful_tests": successful_tests,
                    "failed_tests": failed_tests,
                    "success_rate": successful_tests / test_count * 100,
                    "processing_times": {
                        "average_ms": round(avg_time, 2),
                        "min_ms": round(min_time, 2),
                        "max_ms": round(max_time, 2),
                        "median_ms": round(median_time, 2),
                        "all_times": [round(t, 2) for t in processing_times]
                    },
                    "fps_recommendations": {
                        "max_safe_fps": round(max_safe_fps, 1),
                        "recommended_fps": round(recommended_fps, 1),
                        "conservative_fps": round(conservative_fps, 1)
                    },
                    "performance_level": self._classify_performance(avg_time),
                    "timestamp": time.time()
                }
                
                # 缓存结果
                self.results[container_id] = result
                
                logger.info(f"算法 {container_id} 性能测试完成:")
                logger.info(f"  平均处理时间: {avg_time:.2f}ms")
                logger.info(f"  最大处理时间: {max_time:.2f}ms")
                logger.info(f"  最大安全FPS: {max_safe_fps:.1f}")
                logger.info(f"  建议FPS: {recommended_fps:.1f}")
                logger.info(f"  保守FPS: {conservative_fps:.1f}")

                # 自动应用保守FPS设置（如果启用）
                if auto_apply_fps:
                    try:
                        await self._apply_fps_setting(container_id, conservative_fps)
                        logger.info(f"已自动应用保守FPS设置: {conservative_fps:.1f}")
                    except Exception as e:
                        logger.warning(f"自动应用FPS设置失败: {e}")

                return result
            else:
                raise HTTPException(status_code=500, detail="所有测试都失败了")
                
        except Exception as e:
            logger.error(f"算法性能测试失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"性能测试失败: {str(e)}")
    
    def _classify_performance(self, avg_time_ms: float) -> str:
        """
        根据平均处理时间分类性能等级
        
        Args:
            avg_time_ms: 平均处理时间（毫秒）
            
        Returns:
            性能等级
        """
        if avg_time_ms < 50:
            return "极快"
        elif avg_time_ms < 100:
            return "很快"
        elif avg_time_ms < 200:
            return "快速"
        elif avg_time_ms < 500:
            return "中等"
        elif avg_time_ms < 1000:
            return "较慢"
        else:
            return "慢"
    
    def get_cached_result(self, container_id: str) -> Dict[str, Any]:
        """获取缓存的测试结果"""
        return self.results.get(container_id)

    async def _apply_fps_setting(self, container_id: str, target_fps: float):
        """
        自动应用FPS设置到算法包

        Args:
            container_id: 容器ID
            target_fps: 目标FPS值
        """
        try:
            # 获取容器信息
            container = self.docker_client.containers.get(container_id)

            # 获取容器的端口映射
            port_mapping = container.attrs['NetworkSettings']['Ports']
            container_port = None
            host_port = None

            for port, mappings in port_mapping.items():
                if mappings and port.endswith('/tcp'):
                    container_port = port.split('/')[0]
                    host_port = mappings[0]['HostPort']
                    break

            if not host_port:
                raise ValueError("无法获取容器端口映射")

            # 构建算法包的配置更新API URL
            algorithm_url = f"http://localhost:{host_port}"

            # 尝试更新FPS配置（如果算法包支持）
            config_data = {
                "performance": {
                    "target_fps": target_fps,
                    "max_safe_fps": target_fps * 1.1,
                    "enable_fps_limit": True
                }
            }

            # 发送配置更新请求（如果算法包支持配置更新接口）
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        f"{algorithm_url}/api/v1/config/update",
                        json=config_data,
                        timeout=aiohttp.ClientTimeout(total=5)
                    ) as response:
                        if response.status == 200:
                            logger.info(f"成功更新算法包 {container_id} 的FPS配置")
                        else:
                            logger.warning(f"算法包 {container_id} 不支持配置更新接口")
            except Exception as e:
                logger.debug(f"算法包 {container_id} 配置更新失败: {e}")
                # 这是正常的，因为不是所有算法包都支持动态配置更新

            # 将FPS设置保存到平台配置中
            fps_config = {
                "container_id": container_id,
                "target_fps": target_fps,
                "applied_at": time.time(),
                "auto_applied": True
            }

            # 保存到Redis或本地配置存储
            self.fps_configs[container_id] = fps_config

            logger.info(f"FPS配置已保存到平台: {container_id} -> {target_fps:.1f} FPS")

        except Exception as e:
            logger.error(f"应用FPS设置失败: {e}")
            raise

# 全局基准测试实例
benchmark = AlgorithmBenchmark()

@router.post("/benchmark/{container_id}/with-frames")
async def run_algorithm_benchmark_with_frames(
    container_id: str,
    request: Dict[str, Any],
    auto_apply_fps: bool = True
):
    """
    使用真实视频帧运行算法性能基准测试

    Args:
        container_id: 容器ID
        request: 包含video_frames和parameters的请求体
        auto_apply_fps: 是否自动应用推荐的FPS设置

    Returns:
        性能测试结果和FPS建议
    """
    video_frames_base64 = request.get('video_frames', [])
    parameters = request.get('parameters', {})

    # 解码base64视频帧
    video_frames = []
    for frame_base64 in video_frames_base64:
        try:
            # 移除data:image/jpeg;base64,前缀
            if frame_base64.startswith('data:image/jpeg;base64,'):
                frame_base64 = frame_base64[23:]

            frame_data = base64.b64decode(frame_base64)
            video_frames.append(frame_data)
        except Exception as e:
            logger.error(f"解码视频帧失败: {e}")
            continue

    if not video_frames:
        raise HTTPException(status_code=400, detail="没有有效的视频帧数据")

    return await benchmark.test_algorithm_performance_with_frames(container_id, video_frames, parameters, auto_apply_fps)

@router.post("/benchmark/{container_id}")
async def run_algorithm_benchmark(
    container_id: str,
    test_count: int = 10,
    parameters: Dict[str, Any] = None
):
    """
    运行算法性能基准测试（使用生成的测试图片）

    Args:
        container_id: 容器ID
        test_count: 测试次数（默认10次）
        parameters: 检测参数

    Returns:
        性能测试结果和FPS建议
    """
    # 保留原有的生成图片测试方法作为备用
    test_images = benchmark.generate_test_images(test_count)
    return await benchmark.test_algorithm_performance_with_frames(container_id, test_images, parameters)

@router.get("/benchmark/{container_id}/result")
async def get_benchmark_result(container_id: str):
    """
    获取算法的基准测试结果
    
    Args:
        container_id: 容器ID
        
    Returns:
        缓存的测试结果
    """
    result = benchmark.get_cached_result(container_id)
    if not result:
        raise HTTPException(status_code=404, detail=f"容器 {container_id} 没有基准测试结果")
    return result

@router.get("/benchmark/results/all")
async def get_all_benchmark_results():
    """
    获取所有算法的基准测试结果

    Returns:
        所有缓存的测试结果
    """
    return benchmark.results

@router.post("/fps/{container_id}/apply")
async def apply_fps_setting(
    container_id: str,
    fps_data: Dict[str, Any]
):
    """
    手动应用FPS设置到指定算法包

    Args:
        container_id: 容器ID
        fps_data: FPS配置数据，包含target_fps等

    Returns:
        应用结果
    """
    target_fps = fps_data.get('target_fps')
    if not target_fps or target_fps <= 0:
        raise HTTPException(status_code=400, detail="无效的FPS值")

    try:
        await benchmark._apply_fps_setting(container_id, target_fps)
        return {
            "success": True,
            "message": f"成功应用FPS设置: {target_fps:.1f}",
            "container_id": container_id,
            "applied_fps": target_fps
        }
    except Exception as e:
        logger.error(f"应用FPS设置失败: {e}")
        raise HTTPException(status_code=500, detail=f"应用FPS设置失败: {str(e)}")

@router.get("/fps/{container_id}")
async def get_fps_config(container_id: str):
    """
    获取指定算法包的FPS配置

    Args:
        container_id: 容器ID

    Returns:
        FPS配置信息
    """
    fps_config = benchmark.fps_configs.get(container_id)
    if not fps_config:
        raise HTTPException(status_code=404, detail=f"容器 {container_id} 没有FPS配置")

    return fps_config

@router.get("/fps/configs/all")
async def get_all_fps_configs():
    """
    获取所有算法包的FPS配置

    Returns:
        所有FPS配置
    """
    return benchmark.fps_configs
