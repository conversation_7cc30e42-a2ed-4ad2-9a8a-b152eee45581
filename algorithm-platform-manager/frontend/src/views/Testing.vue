<template>
  <div class="page-container">
    <!-- 测试配置 -->
    <div class="config-section">
        <el-card>
          <template #header>
            <span>测试配置</span>
          </template>
          
          <el-form :model="testForm" label-width="100px">
            <el-form-item label="选择容器">
              <el-select 
                v-model="testForm.containerId" 
                placeholder="请选择要测试的容器"
                style="width: 100%"
                @change="onContainerChange"
              >
                <el-option
                  v-for="container in testingContainers"
                  :key="container.id"
                  :label="`${container.name} (${container.algorithm_name})`"
                  :value="container.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="API端点">
              <el-select
                v-model="testForm.endpoint"
                placeholder="请选择API端点"
                style="width: 100%"
                :loading="loadingEndpoints"
                @change="onEndpointChange"
                :disabled="!testForm.containerId"
              >
                <el-option
                  v-for="endpoint in availableEndpoints"
                  :key="endpoint.path"
                  :label="`${endpoint.method} ${endpoint.path} - ${endpoint.description}`"
                  :value="endpoint.path"
                >
                  <div style="display: flex; justify-content: space-between;">
                    <span>{{ endpoint.path }}</span>
                    <el-tag size="small" :type="getMethodTagType(endpoint.method)">
                      {{ endpoint.method }}
                    </el-tag>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item v-if="requiresFileUpload" label="上传文件">
              <div class="upload-section">
                <!-- 算法性能基准测试 -->
                <div v-if="isDetectionEndpoint && selectedContainer" class="benchmark-section">
                  <div class="benchmark-controls">
                    <el-button-group class="benchmark-buttons">
                      <el-button
                        type="info"
                        @click="runBenchmarkTest"
                        :disabled="!cameraActive || benchmarkTesting || realTimeDetecting"
                        :icon="Timer"
                        :loading="benchmarkTesting"
                      >
                        {{ benchmarkTesting ? '正在测试真实视频帧...' : '算法性能测试' }}
                      </el-button>
                      <el-button
                        v-if="benchmarkResult"
                        type="success"
                        @click="showBenchmarkResult"
                        :icon="DataAnalysis"
                      >
                        查看测试结果
                      </el-button>
                    </el-button-group>

                    <!-- 性能测试结果简要显示 -->
                    <div v-if="benchmarkResult" class="benchmark-summary">
                      <span class="performance-level" :class="getPerformanceLevelClass(benchmarkResult.performance_level)">
                        {{ benchmarkResult.performance_level }}
                      </span>
                      <span class="avg-time">
                        平均: {{ benchmarkResult.processing_times.average_ms }}ms
                      </span>
                      <span class="applied-fps">
                        已应用FPS: {{ detectionFPS }}
                      </span>
                      <span class="test-status">
                        ✅ 已完成测试并自动应用最佳FPS
                      </span>
                    </div>

                    <!-- 测试流程提示 -->
                    <div v-if="!cameraActive" class="benchmark-tip">
                      💡 请先开启摄像头，然后进行性能测试以获得准确的FPS建议
                    </div>
                    <div v-else-if="!benchmarkCompleted" class="benchmark-tip">
                      🎯 请先进行性能测试，系统将使用真实视频帧测试算法处理速度
                    </div>
                  </div>
                </div>

                <!-- 实时摄像头检测 -->
                <div v-if="!requiresMultipleFiles" class="camera-section">
                  <div class="camera-controls">
                    <el-button-group class="camera-buttons">
                      <el-button
                        type="success"
                        @click="startCamera"
                        :disabled="cameraActive || !isDetectionEndpoint"
                        :icon="VideoCamera"
                      >
                        开启摄像头
                      </el-button>
                      <el-button
                        type="warning"
                        @click="stopCamera"
                        :disabled="!cameraActive"
                        :icon="VideoPause"
                      >
                        停止摄像头
                      </el-button>
                      <el-button
                        type="primary"
                        @click="startRealTimeDetection"
                        :disabled="!cameraActive || realTimeDetecting || benchmarkTesting || !benchmarkCompleted"
                        :icon="Monitor"
                      >
                        开始实时检测
                      </el-button>
                      <el-button
                        type="danger"
                        @click="stopRealTimeDetection"
                        :disabled="!realTimeDetecting"
                        :icon="Close"
                      >
                        停止检测
                      </el-button>
                    </el-button-group>

                    <!-- 检测方案选择 -->
                    <div class="detection-mode-settings">
                      <span class="mode-label">检测方案:</span>
                      <el-radio-group v-model="detectionMode" size="small" :disabled="realTimeDetecting">
                        <el-radio-button label="http">HTTP API (兼容)</el-radio-button>
                        <el-radio-button label="websocket">WebSocket (极速)</el-radio-button>
                      </el-radio-group>
                    </div>

                    <!-- FPS显示（只读） -->
                    <div class="fps-display">
                      <span class="fps-label">检测频率:</span>
                      <el-tag
                        :type="getFPSTagType(detectionFPS)"
                        size="large"
                        effect="dark"
                        class="fps-tag"
                      >
                        {{ detectionFPS }}FPS {{ getFPSDescription(detectionFPS) }}
                      </el-tag>
                      <span v-if="!benchmarkCompleted" class="fps-hint">
                        💡 完成性能测试后将自动设置最佳FPS
                      </span>
                    </div>
                  </div>

                  <!-- 摄像头视频流 -->
                  <div v-if="cameraActive" class="camera-container">
                    <video
                      ref="videoElement"
                      autoplay
                      muted
                      playsinline
                      class="camera-video"
                    ></video>
                    <canvas
                      ref="detectionCanvas"
                      class="detection-overlay"
                      v-show="realTimeDetecting"
                    ></canvas>
                    <div class="camera-info">
                      <span class="fps-counter">FPS: {{ currentFPS }}</span>
                      <span class="detection-mode">
                        {{ detectionMode === 'websocket' ? '⚡ WebSocket' : '🌐 HTTP API' }}
                      </span>
                      <span v-if="detectionMode === 'websocket'" class="latency-info">
                        延迟: {{ wsLatency }}ms
                      </span>
                      <span class="detection-status" :class="{ active: realTimeDetecting }">
                        {{ realTimeDetecting ? '🔴 实时检测中' : '⚪ 检测已停止' }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- 单文件上传 -->
                <div v-if="!requiresMultipleFiles" class="upload-area">
                  <el-upload
                    ref="uploadRef"
                    :auto-upload="false"
                    :show-file-list="false"
                    :limit="1"
                    accept="image/*,video/*,.txt,.json"
                    @change="handleFileChange"
                    @exceed="handleFileExceed"
                    :before-upload="beforeFileUpload"
                  >
                    <el-button type="primary">
                      <el-icon><Upload /></el-icon>
                      选择文件
                    </el-button>
                    <template #tip>
                      <div class="el-upload__tip">
                        支持图片、视频、文本等格式，或使用上方摄像头实时检测
                        <span v-if="testForm.file" class="file-uploaded">
                          ✅ 文件已上传，可使用快速测试
                        </span>
                      </div>
                    </template>
                  </el-upload>
                </div>

                <!-- 多文件上传 -->
                <div v-else class="multi-upload-area">
                  <div v-for="field in fileFields" :key="field.name" class="file-upload-item">
                    <div class="upload-label">
                      {{ field.label }}
                      <span v-if="field.required" class="required-mark">*</span>
                    </div>

                    <!-- 集成的上传和预览区域 -->
                    <div class="integrated-upload-preview">
                      <!-- 如果没有文件，显示上传区域 -->
                      <div v-if="!uploadedFiles.get(field.name)" class="upload-zone">
                        <el-upload
                          :ref="`uploadRef_${field.name}`"
                          :auto-upload="false"
                          :show-file-list="false"
                          :limit="1"
                          accept="image/*,video/*,.txt,.json"
                          @change="(file) => handleMultiFileChange(file, field.name)"
                          @exceed="(files) => handleMultiFileExceed(files, field.name)"
                          :before-upload="() => beforeMultiFileUpload(field.name)"
                          class="integrated-upload"
                        >
                          <div class="upload-placeholder">
                            <el-icon size="48" class="upload-icon"><Upload /></el-icon>
                            <div class="upload-text">
                              <div class="upload-title">点击选择{{ field.label }}</div>
                              <div class="upload-hint">支持图片、视频、文本等格式</div>
                            </div>
                          </div>
                        </el-upload>
                      </div>

                      <!-- 如果有文件，显示预览和操作区域 -->
                      <div v-else class="preview-zone">
                        <div class="file-preview-content">
                          <!-- 图片预览 -->
                          <div v-if="uploadedFiles.get(field.name).type === 'image'" class="image-preview-container">
                            <img
                              :src="uploadedFiles.get(field.name).url"
                              :alt="uploadedFiles.get(field.name).name"
                              class="preview-image"
                            />
                          </div>
                          <!-- 非图片文件图标 -->
                          <div v-else class="file-icon-container">
                            <el-icon size="48" class="file-icon"><Document /></el-icon>
                            <span class="file-type">{{ uploadedFiles.get(field.name).type }}</span>
                          </div>

                          <!-- 文件信息和操作 -->
                          <div class="file-info-actions">
                            <div class="file-info">
                              <div class="file-name">{{ uploadedFiles.get(field.name).name }}</div>
                              <div class="file-size">({{ formatFileSize(uploadedFiles.get(field.name).size) }})</div>
                            </div>
                            <div class="file-actions">
                              <el-button size="small" type="primary" @click="replaceFile(field.name)">
                                <el-icon><Upload /></el-icon>
                                重新选择
                              </el-button>
                              <el-button size="small" type="danger" @click="clearMultiFile(field.name)">
                                <el-icon><Delete /></el-icon>
                                删除
                              </el-button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="multi-upload-tip">
                    <el-text size="small" type="info">
                      <span v-if="allRequiredFilesUploaded" class="file-uploaded">
                        ✅ 所有文件已上传，可使用快速测试
                      </span>
                      <span v-else>
                        请上传所有必需的文件
                      </span>
                    </el-text>
                  </div>
                </div>


                <!-- 文件预览区域 - 合并图片渲染功能 -->
                <div v-if="filePreview" class="file-preview">
                  <div class="preview-header">
                    <span class="preview-title">
                      {{ testResult && filePreview.type === 'image' ? '检测结果' : '文件预览' }}
                    </span>
                    <div class="header-actions">
                      <!-- 图片渲染控制按钮 -->
                      <div v-if="testResult && filePreview.type === 'image'" class="render-controls">
                        <el-button
                          size="small"
                          @click="toggleDetectionBoxes"
                          :type="showDetectionBoxes ? 'primary' : 'default'"
                        >
                          {{ showDetectionBoxes ? '隐藏' : '显示' }}检测框
                        </el-button>
                        <el-button
                          size="small"
                          @click="toggleLabels"
                          :type="showLabels ? 'primary' : 'default'"
                        >
                          {{ showLabels ? '隐藏' : '显示' }}标签
                        </el-button>
                        <el-button
                          size="small"
                          @click="downloadRenderedImage"
                          :icon="Download"
                        >
                          下载
                        </el-button>
                      </div>
                      <el-button
                        size="small"
                        type="danger"
                        @click="clearFile"
                        :icon="Delete"
                      >
                        删除
                      </el-button>
                    </div>
                  </div>
                  <div class="preview-content">
                    <!-- 图片预览/渲染 -->
                    <div v-if="filePreview.type === 'image'" class="image-container">
                      <canvas
                        v-if="testResult && shouldShowImageRender"
                        ref="renderCanvas"
                        class="render-canvas"
                        @click="handleCanvasClick"
                      ></canvas>
                      <img
                        v-else
                        :src="filePreview.url"
                        :alt="filePreview.name"
                        class="preview-image"
                      />
                    </div>
                    <!-- 视频预览 -->
                    <div v-else-if="filePreview.type === 'video'" class="video-preview">
                      <video
                        :src="filePreview.url"
                        controls
                        class="preview-video"
                      />
                    </div>
                    <!-- 其他文件 -->
                    <div v-else class="file-info">
                      <el-icon class="file-icon"><Document /></el-icon>
                      <div class="file-details">
                        <div class="file-name">{{ filePreview.name }}</div>
                        <div class="file-size">{{ formatFileSize(filePreview.size) }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-form-item>

            <el-form-item label="请求参数">
              <div class="params-container">
                <!-- 左侧：参数输入 -->
                <div class="params-input-section">
                  <el-input
                    v-model="testForm.parameters"
                    type="textarea"
                    :rows="8"
                    :placeholder="getParameterPlaceholder()"
                    class="params-textarea"
                  />
                  <div class="params-actions">
                    <el-button
                      size="small"
                      @click="fillDefaultParameters"
                      :disabled="!currentEndpoint"
                    >
                      <el-icon><Lightning /></el-icon>
                      填充默认参数
                    </el-button>
                    <el-button
                      size="small"
                      @click="clearParameters"
                    >
                      <el-icon><Delete /></el-icon>
                      清空参数
                    </el-button>
                  </div>
                </div>

                <!-- 右侧：参数说明 -->
                <div v-if="currentEndpoint && currentEndpoint.parameters && currentEndpoint.parameters.length > 0"
                     class="params-help-section">
                  <el-card class="parameter-hints-card" shadow="never">
                    <template #header>
                      <div class="card-header">
                        <span>参数说明</span>
                      </div>
                    </template>
                    <div class="parameter-grid">
                      <div v-for="param in currentEndpoint.parameters" :key="param.name" class="parameter-item">
                        <span class="param-name">{{ param.name }}</span>
                        <span class="param-type">({{ param.type }})</span>
                        <span v-if="param.default !== undefined" class="param-default">默认: {{ param.default }}</span>
                        <span v-if="param.required" class="param-required">必填</span>
                      </div>
                    </div>
                  </el-card>
                </div>
              </div>
            </el-form-item>

            <el-form-item>
              <div class="test-buttons">
                <el-button
                  type="primary"
                  @click="executeTest"
                  :loading="testing"
                  :disabled="!testForm.containerId || !testForm.endpoint"
                  style="flex: 1"
                >
                  <el-icon><Connection /></el-icon>
                  执行测试
                </el-button>
                <el-tooltip
                  content="快速测试：自动使用默认参数，适合快速验证算法效果"
                  placement="top"
                >
                  <el-button
                    type="success"
                    @click="quickTest"
                    :loading="testing"
                    :disabled="!canQuickTest"
                    style="margin-left: 10px"
                  >
                    <el-icon><Lightning /></el-icon>
                    快速测试
                  </el-button>
                </el-tooltip>
              </div>
              <div class="button-tips">
                <el-text size="small" type="info">
                  💡 <strong>执行测试</strong>：使用当前配置的参数进行测试
                  <br>
                  ⚡ <strong>快速测试</strong>：自动填充默认参数并执行（需要上传文件）
                </el-text>
              </div>
            </el-form-item>
          </el-form>
        </el-card>
    </div>

    <!-- 测试结果 -->
    <div class="result-section">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>测试结果</span>
              <el-button 
                v-if="testResult" 
                @click="clearResult" 
                size="small"
              >
                清空结果
              </el-button>
            </div>
          </template>

          <div v-if="!testResult && !testing" class="empty-result">
            <el-empty description="请选择容器并执行测试" />
          </div>

          <div v-else-if="testing" class="testing-status">
            <el-skeleton :rows="5" animated />
            <div class="testing-text">正在执行测试...</div>
          </div>

          <div v-else class="result-content">
            <!-- 请求信息 -->
            <el-descriptions title="请求信息" :column="2" border>
              <el-descriptions-item label="容器">
                {{ selectedContainer?.name }}
              </el-descriptions-item>
              <el-descriptions-item label="算法">
                {{ selectedContainer?.algorithm_name }}
              </el-descriptions-item>
              <el-descriptions-item label="端点">
                {{ testForm.endpoint }}
              </el-descriptions-item>
              <el-descriptions-item label="方法">
                <el-tag>{{ testForm.method }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="状态码">
                <el-tag :type="testResult.status >= 400 ? 'danger' : 'success'">
                  {{ testResult.status }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="响应时间">
                {{ testResult.response_time }}ms
              </el-descriptions-item>
            </el-descriptions>

            <!-- 响应数据 -->
            <div class="response-section">
              <h4>响应数据</h4>
              <el-tabs v-model="activeTab">
                <el-tab-pane label="🎨 智能格式化" name="formatted">
                  <div class="formatted-content">
                    <pre class="json-content">{{ formatJSON(testResult.data) }}</pre>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="📄 原始JSON" name="raw">
                  <div class="raw-content">
                    <pre class="json-content raw-json">{{ getRawJSON(testResult.data) }}</pre>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="📊 数据统计" name="stats" v-if="getDataStats(testResult.data)">
                  <div class="stats-content">
                    <el-descriptions :column="2" border>
                      <el-descriptions-item
                        v-for="(value, key) in getDataStats(testResult.data)"
                        :key="key"
                        :label="key"
                      >
                        {{ value }}
                      </el-descriptions-item>
                    </el-descriptions>
                  </div>
                </el-tab-pane>

              </el-tabs>
            </div>

            <!-- 错误信息 -->
            <div v-if="testResult.error" class="error-section">
              <h4>错误信息</h4>
              <el-alert
                :title="testResult.error"
                type="error"
                show-icon
                :closable="false"
              />
            </div>
          </div>
        </el-card>
    </div>
  </div>

  <!-- 基准测试结果对话框 -->
  <el-dialog
    v-model="benchmarkDialogVisible"
    title="算法性能基准测试结果"
    width="800px"
    :close-on-click-modal="false"
  >
    <div v-if="benchmarkResult" class="benchmark-result">
      <!-- 基本信息 -->
      <el-row :gutter="20" class="result-section">
        <el-col :span="12">
          <el-statistic title="测试容器" :value="benchmarkResult.container_id" />
        </el-col>
        <el-col :span="12">
          <el-statistic title="测试次数" :value="benchmarkResult.test_count" suffix="次" />
        </el-col>
      </el-row>

      <!-- 成功率 -->
      <el-row :gutter="20" class="result-section">
        <el-col :span="8">
          <el-statistic title="成功测试" :value="benchmarkResult.successful_tests" suffix="次" />
        </el-col>
        <el-col :span="8">
          <el-statistic title="失败测试" :value="benchmarkResult.failed_tests" suffix="次" />
        </el-col>
        <el-col :span="8">
          <el-statistic
            title="成功率"
            :value="benchmarkResult.success_rate"
            suffix="%"
            :precision="1"
            :value-style="{ color: benchmarkResult.success_rate >= 90 ? '#67C23A' : benchmarkResult.success_rate >= 70 ? '#E6A23C' : '#F56C6C' }"
          />
        </el-col>
      </el-row>

      <!-- 处理时间统计 -->
      <div class="result-section">
        <h4>处理时间统计</h4>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="平均时间" :value="benchmarkResult.processing_times.average_ms" suffix="ms" :precision="2" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="最短时间" :value="benchmarkResult.processing_times.min_ms" suffix="ms" :precision="2" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="最长时间" :value="benchmarkResult.processing_times.max_ms" suffix="ms" :precision="2" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="中位数" :value="benchmarkResult.processing_times.median_ms" suffix="ms" :precision="2" />
          </el-col>
        </el-row>
      </div>

      <!-- FPS建议 -->
      <div class="result-section">
        <h4>FPS建议</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-statistic
              title="建议FPS"
              :value="benchmarkResult.fps_recommendations.recommended_fps"
              suffix="FPS"
              :precision="1"
              :value-style="{ color: '#409EFF' }"
            />
          </el-col>
          <el-col :span="8">
            <el-statistic
              title="最大安全FPS"
              :value="benchmarkResult.fps_recommendations.max_safe_fps"
              suffix="FPS"
              :precision="1"
              :value-style="{ color: '#67C23A' }"
            />
          </el-col>
          <el-col :span="8">
            <el-statistic
              title="保守FPS"
              :value="benchmarkResult.fps_recommendations.conservative_fps"
              suffix="FPS"
              :precision="1"
              :value-style="{ color: '#E6A23C' }"
            />
          </el-col>
        </el-row>
      </div>

      <!-- 性能等级 -->
      <div class="result-section">
        <h4>性能评级</h4>
        <el-tag
          :type="getPerformanceTagType(benchmarkResult.performance_level)"
          size="large"
          effect="dark"
        >
          {{ benchmarkResult.performance_level }}
        </el-tag>
      </div>

      <!-- 详细时间数据 -->
      <div class="result-section">
        <h4>详细时间数据</h4>
        <div class="time-data">
          <el-tag
            v-for="(time, index) in benchmarkResult.processing_times.all_times"
            :key="index"
            class="time-tag"
            :type="getTimeTagType(time, benchmarkResult.processing_times.average_ms)"
          >
            测试{{ index + 1 }}: {{ time }}ms
          </el-tag>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button type="primary" @click="benchmarkDialogVisible = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch, getCurrentInstance, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload, Connection, Lightning, Delete, Document, Download, VideoCamera, VideoPause, Monitor, Close, Timer, DataAnalysis } from '@element-plus/icons-vue'
import { testingAPI } from '@/api'

// 响应式数据
const testingContainers = ref([])
const availableEndpoints = ref([])
const loadingEndpoints = ref(false)
const testForm = ref({
  containerId: '',
  endpoint: '',
  method: 'POST',
  file: null,
  parameters: ''
})
const testResult = ref(null)
const testing = ref(false)
const activeTab = ref('formatted')
const uploadRef = ref()

// 文件预览相关
const filePreview = ref(null)
const renderCanvas = ref(null)
const showDetectionBoxes = ref(true)
const showLabels = ref(true)

// 多文件上传相关
const uploadedFiles = ref(new Map()) // 存储多个文件，key为参数名，value为文件信息

// 算法性能基准测试相关
const benchmarkTesting = ref(false)
const benchmarkResult = ref(null)
const benchmarkDialogVisible = ref(false)
const benchmarkCompleted = ref(false) // 是否完成基准测试

// 摄像头实时检测相关
const videoElement = ref(null)
const detectionCanvas = ref(null)
const cameraActive = ref(false)
const realTimeDetecting = ref(false)
const mediaStream = ref(null)
const detectionInterval = ref(null)
const animationFrameId = ref(null)
const lastDetectionTime = ref(0)
const currentFPS = ref(0)
const frameCount = ref(0)
const lastFPSTime = ref(0)
const isDetecting = ref(false) // 防止并发检测
const detectionQueue = ref(0) // 检测队列计数
const detectionFPS = ref(15) // 默认15FPS
const detectionMode = ref('http') // 检测方案：http 或 websocket

// WebSocket相关
const websocket = ref(null)
const wsConnected = ref(false)
const wsLatency = ref(0) // WebSocket延迟
const lastPingTime = ref(0)

// 页面状态记忆系统
const endpointStates = ref(new Map())
const previousEndpoint = ref('')

// 计算属性
const selectedContainer = computed(() => {
  return testingContainers.value.find(c => c.id === testForm.value.containerId)
})

const currentEndpoint = computed(() => {
  return availableEndpoints.value.find(e => e.path === testForm.value.endpoint)
})

const canQuickTest = computed(() => {
  const endpoint = currentEndpoint.value
  if (endpoint && endpoint.requires_file) {
    if (requiresMultipleFiles.value) {
      // 多文件上传：检查是否所有必需文件都已上传
      return testForm.value.containerId && testForm.value.endpoint && allRequiredFilesUploaded.value
    } else {
      // 单文件上传：检查是否有文件
      return testForm.value.containerId && testForm.value.endpoint && testForm.value.file
    }
  }
  return testForm.value.containerId && testForm.value.endpoint
})

const requiresFileUpload = computed(() => {
  const endpoint = currentEndpoint.value
  return endpoint ? endpoint.requires_file : false
})

// 分析接口参数中的文件字段
const fileFields = computed(() => {
  try {
    const params = JSON.parse(testForm.value.parameters || '{}')
    const fields = []

    // 查找所有可能的文件字段
    for (const [key, value] of Object.entries(params)) {
      // 检查字段名是否包含 file 或者值为空字符串（通常表示文件字段）
      if (key.toLowerCase().includes('file') ||
          (typeof value === 'string' && value === '')) {
        fields.push({
          name: key,
          label: key,
          required: true // 可以根据接口文档进一步判断
        })
      }
    }

    return fields
  } catch (error) {
    return []
  }
})

// 是否需要多文件上传
const requiresMultipleFiles = computed(() => {
  return fileFields.value.length > 1
})

// 检查是否所有必需的文件都已上传
const allRequiredFilesUploaded = computed(() => {
  if (!requiresMultipleFiles.value) return false

  return fileFields.value.every(field => {
    if (field.required) {
      return uploadedFiles.value.has(field.name)
    }
    return true
  })
})

const shouldShowImageRender = computed(() => {
  return testResult.value && filePreview.value && filePreview.value.type === 'image'
})

// 判断是否为检测端点（支持实时检测）
const isDetectionEndpoint = computed(() => {
  return testForm.value.endpoint && (
    testForm.value.endpoint.includes('/detect') ||
    testForm.value.endpoint.includes('/recognition') ||
    testForm.value.endpoint.includes('/classify')
  )
})

// 方法
const loadTestingContainers = async () => {
  try {
    const response = await testingAPI.getTestingContainers()
    if (response.success) {
      testingContainers.value = response.data.containers
    }
  } catch (error) {
    ElMessage.error('获取可测试容器失败')
  }
}

const onContainerChange = async () => {
  // 清空状态记忆（切换容器时）
  endpointStates.value.clear()
  previousEndpoint.value = ''

  // 清空当前状态
  clearCurrentState()

  // 重置表单
  testForm.value.endpoint = ''
  testForm.value.method = 'POST'
  testForm.value.parameters = ''
  availableEndpoints.value = []

  const container = selectedContainer.value
  if (!container) return

  // 加载容器的API端点
  await loadContainerEndpoints(container.name)

  // 设置第一个端点为 previousEndpoint，这样后续切换时能正确保存状态
  if (availableEndpoints.value.length > 0) {
    previousEndpoint.value = availableEndpoints.value[0].path
  }
}

const loadContainerEndpoints = async (containerName) => {
  if (!containerName) return

  loadingEndpoints.value = true
  try {
    const response = await testingAPI.getContainerEndpoints(containerName)
    if (response.success) {
      availableEndpoints.value = response.data.endpoints
      // 自动选择第一个端点
      if (availableEndpoints.value.length > 0) {
        const firstEndpoint = availableEndpoints.value[0]
        testForm.value.endpoint = firstEndpoint.path
        testForm.value.method = firstEndpoint.method
        onEndpointChange()
      }
    }
  } catch (error) {
    ElMessage.error('获取API端点失败')
  } finally {
    loadingEndpoints.value = false
  }
}

// 状态管理函数 - 只保存请求参数
const saveEndpointState = (endpointPath) => {
  if (!endpointPath || !selectedContainer.value) return

  const stateKey = `${selectedContainer.value.name}_${endpointPath}`

  // 只保存请求参数
  const currentState = {
    parameters: testForm.value.parameters
  }

  endpointStates.value.set(stateKey, currentState)
  console.log('保存状态:', stateKey, currentState)
}

const restoreEndpointState = (endpointPath) => {
  if (!selectedContainer.value) return

  const stateKey = `${selectedContainer.value.name}_${endpointPath}`
  const savedState = endpointStates.value.get(stateKey)

  console.log('尝试恢复状态:', stateKey, savedState)

  if (savedState) {
    // 清空当前状态（图片和测试结果）
    clearCurrentState()

    // 只恢复请求参数
    testForm.value.parameters = savedState.parameters || ''

    console.log('状态已恢复:', {
      parameters: testForm.value.parameters
    })

    ElMessage.success(`已恢复 ${endpointPath} 的请求参数`)
  } else {
    // 新的端点，初始化为默认状态
    clearCurrentState()
    const endpoint = currentEndpoint.value
    if (endpoint) {
      fillDefaultParameters()
    }
    ElMessage.info(`切换到新端点：${endpointPath}`)
  }
}

const clearCurrentState = () => {
  // 清理文件资源
  if (filePreview.value && filePreview.value.url) {
    URL.revokeObjectURL(filePreview.value.url)
  }

  // 清理多文件资源
  clearAllMultiFiles()

  // 重置所有状态
  testForm.value.file = null
  testForm.value.parameters = ''
  filePreview.value = null
  testResult.value = null
  activeTab.value = 'formatted'
  showDetectionBoxes.value = true
  showLabels.value = true

  // 清理上传组件
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

const onEndpointChange = () => {
  const newEndpoint = testForm.value.endpoint
  const oldEndpoint = previousEndpoint.value

  // 首先保存旧端点的状态（如果存在且不同）
  if (oldEndpoint && oldEndpoint !== newEndpoint) {
    saveEndpointState(oldEndpoint)
  }

  const endpoint = currentEndpoint.value
  if (endpoint) {
    // 设置新端点的方法
    testForm.value.method = endpoint.method

    // 恢复或初始化新端点的状态
    restoreEndpointState(newEndpoint)
  }

  // 更新前一个端点记录
  previousEndpoint.value = newEndpoint
}

const getMethodTagType = (method) => {
  const types = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger'
  }
  return types[method] || 'info'
}

const getParameterPlaceholder = () => {
  const endpoint = currentEndpoint.value
  if (!endpoint || !endpoint.parameters) {
    return '{"param1": "value1", "param2": "value2"}'
  }

  const example = {}
  Object.keys(endpoint.parameters).forEach(param => {
    if (param === 'file') return // 文件参数不在JSON中
    if (param.includes('threshold')) {
      example[param] = 0.5
    } else {
      example[param] = `value_${param}`
    }
  })

  return Object.keys(example).length > 0 ? JSON.stringify(example, null, 2) : '{}'
}

const fillDefaultParameters = () => {
  const endpoint = currentEndpoint.value
  if (!endpoint) {
    testForm.value.parameters = '{}'
    return
  }

  const defaultParams = {}

  // 处理新的API端点数据结构
  if (endpoint.default_params && Object.keys(endpoint.default_params).length > 0) {
    // 使用从OpenAPI文档解析的默认参数
    Object.assign(defaultParams, endpoint.default_params)
  } else if (endpoint.parameters && Array.isArray(endpoint.parameters)) {
    // 处理参数数组格式
    endpoint.parameters.forEach(param => {
      if (param.name === 'file') return // 文件参数不在JSON中
      if (param.in === 'formData' && param.name !== 'file') {
        if (param.default !== undefined) {
          defaultParams[param.name] = param.default
        } else if (param.type === 'boolean') {
          defaultParams[param.name] = false
        } else if (param.type === 'number' || param.type === 'integer') {
          if (param.name.includes('threshold')) {
            defaultParams[param.name] = 0.5
          } else {
            defaultParams[param.name] = 0
          }
        } else {
          defaultParams[param.name] = ''
        }
      }
    })
  } else if (endpoint.parameters && typeof endpoint.parameters === 'object') {
    // 处理旧的参数对象格式（向后兼容）
    Object.keys(endpoint.parameters).forEach(param => {
      if (param === 'file') return // 文件参数不在JSON中
      if (param === 'conf_threshold') {
        defaultParams[param] = 0.5
      } else if (param === 'iou_threshold') {
        defaultParams[param] = 0.4
      } else if (param.includes('threshold')) {
        defaultParams[param] = 0.5
      } else {
        defaultParams[param] = ''
      }
    })
  }

  testForm.value.parameters = JSON.stringify(defaultParams, null, 2)
}

const clearParameters = () => {
  testForm.value.parameters = '{}'
}

const quickTest = async () => {
  if (!canQuickTest.value) {
    ElMessage.warning('请选择容器、API端点和上传文件')
    return
  }

  // 快速测试：自动填充默认参数并执行
  const originalParams = testForm.value.parameters

  // 如果参数为空或只有空对象，自动填充默认参数
  if (!testForm.value.parameters || testForm.value.parameters.trim() === '' || testForm.value.parameters.trim() === '{}') {
    fillDefaultParameters()
  }

  // 执行测试
  await executeTest()

  ElMessage.success('快速测试完成！已自动使用默认参数')
}

const getRawJSON = (data) => {
  try {
    return JSON.stringify(data, null, 2)
  } catch (error) {
    return String(data)
  }
}

const getDataStats = (data) => {
  try {
    if (!data || typeof data !== 'object') return null

    const stats = {}

    // 检测结果统计
    if (data.results || data.detections || data.predictions) {
      const results = data.results || data.detections || data.predictions
      if (Array.isArray(results)) {
        stats['🎯 检测数量'] = results.length

        // 统计置信度
        const confidences = results.map(r => r.confidence || r.score || 0).filter(c => c > 0)
        if (confidences.length > 0) {
          stats['📈 平均置信度'] = (confidences.reduce((a, b) => a + b, 0) / confidences.length).toFixed(3)
          stats['🔝 最高置信度'] = Math.max(...confidences).toFixed(3)
          stats['📉 最低置信度'] = Math.min(...confidences).toFixed(3)
        }

        // 统计类别
        const classes = results.map(r => r.class || r.label || r.category).filter(c => c)
        if (classes.length > 0) {
          const uniqueClasses = [...new Set(classes)]
          stats['🏷️ 检测类别'] = uniqueClasses.join(', ')
          stats['📊 类别数量'] = uniqueClasses.length
        }
      }
    }

    // 性能统计
    if (data.processing_time || data.inference_time) {
      stats['⏱️ 处理时间'] = `${data.processing_time || data.inference_time}ms`
    }

    // 图像信息
    if (data.image_width || data.width) {
      stats['🖼️ 图像尺寸'] = `${data.image_width || data.width} × ${data.image_height || data.height}`
    }

    // 模型信息
    if (data.model_name || data.algorithm_name) {
      stats['🤖 模型名称'] = data.model_name || data.algorithm_name
    }

    if (data.model_version || data.version) {
      stats['📦 模型版本'] = data.model_version || data.version
    }

    return Object.keys(stats).length > 0 ? stats : null
  } catch (error) {
    return null
  }
}

const handleFileChange = (file) => {
  // 清理之前的文件资源
  if (filePreview.value && filePreview.value.url) {
    URL.revokeObjectURL(filePreview.value.url)
  }

  // 清除之前的测试结果（因为文件已更换）
  testResult.value = null

  // 设置新文件
  testForm.value.file = file.raw

  // 创建新文件预览
  createFilePreview(file.raw)

  ElMessage.success(`文件已更新：${file.name}`)
}

const handleFileExceed = (files) => {
  // 当超过文件数量限制时，清除现有文件并添加新文件
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }

  // 手动触发文件更改
  const newFile = files[0]
  if (newFile) {
    // 模拟文件对象结构
    const fileObj = {
      name: newFile.name,
      raw: newFile
    }
    handleFileChange(fileObj)
  }
}

const beforeFileUpload = (file) => {
  // 在上传前检查，如果已有文件则先清除
  if (testForm.value.file) {
    clearFile()
  }
  return false // 阻止自动上传，我们手动处理
}

// 多文件上传处理
const handleMultiFileChange = (file, fieldName) => {
  if (!file || !file.raw) return

  const rawFile = file.raw

  // 存储文件信息
  const fileInfo = {
    name: rawFile.name,
    size: rawFile.size,
    type: getFileType(rawFile.type),
    url: URL.createObjectURL(rawFile),
    file: rawFile
  }

  uploadedFiles.value.set(fieldName, fileInfo)

  // 更新参数中的文件字段
  updateFileParameters()

  // 清除之前的测试结果
  testResult.value = null

  ElMessage.success(`${fieldName} 文件已更新：${rawFile.name}`)
}

const handleMultiFileExceed = (files, fieldName) => {
  // 当超过文件数量限制时，清除现有文件并添加新文件
  const uploadRefs = getCurrentInstance().refs
  const uploadRef = uploadRefs[`uploadRef_${fieldName}`]
  if (uploadRef && uploadRef[0]) {
    uploadRef[0].clearFiles()
  }

  // 手动触发文件更改
  const newFile = files[0]
  if (newFile) {
    const fileObj = {
      name: newFile.name,
      raw: newFile,
      status: 'ready',
      uid: Date.now()
    }
    handleMultiFileChange(fileObj, fieldName)
  }
}

const beforeMultiFileUpload = (fieldName) => {
  // 在上传前检查，如果已有文件则先清除
  if (uploadedFiles.value.has(fieldName)) {
    clearMultiFile(fieldName)
  }
  return false // 阻止自动上传，我们手动处理
}

// 更新参数中的文件字段
const updateFileParameters = () => {
  try {
    const params = JSON.parse(testForm.value.parameters || '{}')

    // 对于多文件上传，保持文件字段为空字符串
    // 文件将通过 FormData 直接传递给后端，不需要在参数中包含文件名
    for (const [fieldName, fileInfo] of uploadedFiles.value) {
      if (params.hasOwnProperty(fieldName)) {
        // 保持文件字段为空字符串，因为文件通过 FormData 传递
        params[fieldName] = ''
      }
    }

    testForm.value.parameters = JSON.stringify(params, null, 2)
  } catch (error) {
    console.error('更新文件参数失败:', error)
  }
}

// 清理单个多文件
const clearMultiFile = (fieldName) => {
  const fileInfo = uploadedFiles.value.get(fieldName)
  if (fileInfo && fileInfo.url) {
    URL.revokeObjectURL(fileInfo.url)
  }
  uploadedFiles.value.delete(fieldName)

  // 恢复参数中的文件字段为空字符串，而不是删除
  try {
    const params = JSON.parse(testForm.value.parameters || '{}')
    params[fieldName] = '' // 恢复为空字符串
    testForm.value.parameters = JSON.stringify(params, null, 2)
  } catch (error) {
    console.error('恢复文件参数失败:', error)
  }

  ElMessage.info(`已清除 ${fieldName} 文件`)
}

// 重新选择文件
const replaceFile = (fieldName) => {
  // 先清除现有文件
  clearMultiFile(fieldName)

  // 触发文件选择
  nextTick(() => {
    const uploadRefs = getCurrentInstance().refs
    const uploadRef = uploadRefs[`uploadRef_${fieldName}`]
    if (uploadRef && uploadRef[0] && uploadRef[0].$refs.input) {
      uploadRef[0].$refs.input.click()
    }
  })
}

// 清理所有多文件
const clearAllMultiFiles = () => {
  for (const [fieldName, fileInfo] of uploadedFiles.value) {
    if (fileInfo.url) {
      URL.revokeObjectURL(fileInfo.url)
    }
  }
  uploadedFiles.value.clear()

  // 重置参数，恢复文件字段为空字符串
  try {
    const params = JSON.parse(testForm.value.parameters || '{}')
    for (const field of fileFields.value) {
      params[field.name] = ''
    }
    testForm.value.parameters = JSON.stringify(params, null, 2)
  } catch (error) {
    console.error('重置文件参数失败:', error)
  }
}

const createFilePreview = (file) => {
  if (!file) {
    filePreview.value = null
    return
  }

  const url = URL.createObjectURL(file)
  const fileType = getFileType(file.type)

  filePreview.value = {
    name: file.name,
    size: file.size,
    type: fileType,
    url: url,
    file: file
  }
}

const getFileType = (mimeType) => {
  if (mimeType.startsWith('image/')) return 'image'
  if (mimeType.startsWith('video/')) return 'video'
  return 'other'
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const clearFile = () => {
  // 清理文件资源
  testForm.value.file = null
  if (filePreview.value && filePreview.value.url) {
    URL.revokeObjectURL(filePreview.value.url)
    filePreview.value = null
  }
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }

  // 清除测试结果
  testResult.value = null

  ElMessage.success('文件已清除')
}

const executeTest = async () => {
  if (!testForm.value.containerId) {
    ElMessage.warning('请选择要测试的容器')
    return
  }

  testing.value = true
  testResult.value = null

  try {
    const testData = {
      endpoint: testForm.value.endpoint,
      method: testForm.value.method,
      parameters: testForm.value.parameters || '{}'
    }

    // 处理文件上传
    if (requiresMultipleFiles.value) {
      // 多文件上传：将所有文件添加到 testData
      const files = {}
      for (const [fieldName, fileInfo] of uploadedFiles.value) {
        files[fieldName] = fileInfo.file
      }
      testData.files = files
    } else if (testForm.value.file) {
      // 单文件上传
      testData.file = testForm.value.file
    }

    const startTime = Date.now()
    const response = await testingAPI.testContainer(testForm.value.containerId, testData)
    const endTime = Date.now()

    testResult.value = {
      status: response.status || 200,
      data: response.data || response,
      response_time: endTime - startTime,
      error: null
    }

    ElMessage.success('测试执行成功')

    // 如果有图片和检测结果，渲染图片
    if (shouldShowImageRender.value) {
      await nextTick()
      renderImageWithDetections()
    }
  } catch (error) {
    const endTime = Date.now()
    testResult.value = {
      status: error.response?.status || 500,
      data: error.response?.data || null,
      response_time: endTime - Date.now(),
      error: error.message || '测试执行失败'
    }
    ElMessage.error('测试执行失败')
  } finally {
    testing.value = false
  }
}

const clearResult = () => {
  testResult.value = null
  activeTab.value = 'formatted'
}

const formatJSON = (data) => {
  try {
    if (typeof data === 'string') {
      // 尝试解析字符串为JSON
      try {
        const parsed = JSON.parse(data)
        return formatJSON(parsed) // 递归处理解析后的数据
      } catch {
        return data
      }
    }

    // 如果是对象，进行美化格式化
    if (typeof data === 'object' && data !== null) {

      // 处理测试响应的外层结构
      if (data.response && typeof data.response === 'object') {
        // 这是测试API返回的结构，提取response部分进行格式化
        const response = data.response

        if (response.success !== undefined && response.data) {
          const formatted = {
            '✅ 响应状态': response.success ? '成功' : '失败'
          }

          // 处理不同类型的data内容
          if (response.data.results || response.data.detections || response.data.predictions) {
            // 检测结果
            const results = response.data.results || response.data.detections || response.data.predictions
            formatted['📊 检测结果'] = results
            formatted['🎯 检测数量'] = Array.isArray(results) ? results.length : '未知'
          } else if (response.data.name || response.data.version) {
            // 算法信息
            formatted['🤖 算法名称'] = response.data.name || '未知'
            formatted['📦 版本'] = response.data.version || '未知'
            formatted['📄 描述'] = response.data.description || '无描述'
            if (response.data.capabilities) {
              formatted['🛠️ 功能'] = response.data.capabilities
            }
            if (response.data.classes) {
              formatted['🏷️ 支持类别'] = response.data.classes
            }
            if (response.data.supported_formats) {
              formatted['📁 支持格式'] = response.data.supported_formats
            }
          } else if (response.data.service) {
            // 健康检查
            formatted['🏥 服务名称'] = response.data.service
            formatted['💚 健康状态'] = response.data.status || '未知'
            if (response.data.engine_loaded !== undefined) {
              formatted['🔧 引擎状态'] = response.data.engine_loaded ? '已加载' : '未加载'
            }
          } else {
            // 其他数据类型
            formatted['📋 响应数据'] = response.data
          }

          // 添加时间信息
          if (response.timestamp) {
            formatted['🕐 响应时间'] = response.timestamp
          }
          if (response.processing_time) {
            formatted['⏱️ 处理时间'] = `${response.processing_time}ms`
          }

          // 添加错误信息
          if (response.error) {
            formatted['❌ 错误信息'] = response.error
          }

          // 添加请求信息
          if (data.container) {
            formatted['🐳 容器ID'] = data.container
          }
          if (data.endpoint) {
            formatted['🔗 API端点'] = data.endpoint
          }
          if (data.method) {
            formatted['📡 请求方法'] = data.method
          }
          if (data.status_code) {
            formatted['📊 状态码'] = data.status_code
          }

          return JSON.stringify(formatted, null, 2)
        }
      }

      // 处理标准API响应格式
      if (data.success !== undefined && data.data) {
        const formatted = {
          '✅ 响应状态': data.success ? '成功' : '失败'
        }

        // 处理不同类型的data内容
        if (data.data.results || data.data.detections || data.data.predictions) {
          // 检测结果
          const results = data.data.results || data.data.detections || data.data.predictions
          formatted['📊 检测结果'] = results
          formatted['🎯 检测数量'] = Array.isArray(results) ? results.length : '未知'

          // v2.0格式的额外信息
          if (data.data.summary) {
            formatted['📈 结果摘要'] = data.data.summary
          }
        } else if (data.data.detections !== undefined) {
          // v2.0统一格式的检测结果
          formatted['📊 检测结果'] = data.data.detections
          formatted['🎯 检测数量'] = Array.isArray(data.data.detections) ? data.data.detections.length : '未知'
          if (data.data.summary) {
            formatted['📈 结果摘要'] = data.data.summary
          }
        } else if (data.data.name || data.data.version) {
          // 算法信息
          formatted['🤖 算法名称'] = data.data.name || '未知'
          formatted['📦 版本'] = data.data.version || '未知'
          formatted['📄 描述'] = data.data.description || '无描述'
          if (data.data.capabilities) {
            formatted['🛠️ 功能'] = data.data.capabilities
          }
          if (data.data.classes) {
            formatted['🏷️ 支持类别'] = data.data.classes
          }
          if (data.data.supported_formats) {
            formatted['📁 支持格式'] = data.data.supported_formats
          }
        } else if (data.data.service) {
          // 健康检查
          formatted['🏥 服务名称'] = data.data.service
          formatted['💚 健康状态'] = data.data.status || '未知'
          if (data.data.engine_loaded !== undefined) {
            formatted['🔧 引擎状态'] = data.data.engine_loaded ? '已加载' : '未加载'
          }
        } else {
          // 其他数据类型
          formatted['📋 响应数据'] = data.data
        }

        // 添加时间信息
        if (data.timestamp) {
          formatted['🕐 响应时间'] = data.timestamp
        }
        if (data.processing_time) {
          formatted['⏱️ 处理时间'] = `${data.processing_time}ms`
        }

        // v2.0格式的元数据
        if (data.metadata) {
          if (data.metadata.processing_time_ms) {
            formatted['⏱️ 处理时间'] = `${data.metadata.processing_time_ms.toFixed(2)}ms`
          }
          if (data.metadata.image_shape) {
            formatted['🖼️ 图像尺寸'] = `${data.metadata.image_shape[1]}×${data.metadata.image_shape[0]}`
          }
          if (data.metadata.model_info) {
            formatted['🤖 模型信息'] = data.metadata.model_info
          }
        }

        // 添加错误信息
        if (data.error) {
          formatted['❌ 错误信息'] = data.error
        }

        return JSON.stringify(formatted, null, 2)
      }

      // 直接的检测结果数据
      if (data.results || data.detections || data.predictions) {
        const formatted = {
          '📊 检测结果': data.results || data.detections || data.predictions,
          '⏱️ 处理时间': data.processing_time || data.inference_time || '未设置',
          '🎯 置信度阈值': data.conf_threshold || data.confidence_threshold || '未设置',
          '📏 IoU阈值': data.iou_threshold || '未设置',
          '🖼️ 图像信息': {
            '宽度': data.image_width || data.width || '未知',
            '高度': data.image_height || data.height || '未知',
            '格式': data.image_format || data.format || '未知'
          }
        }

        // 移除空值
        Object.keys(formatted).forEach(key => {
          if (formatted[key] === '未知' || formatted[key] === '未设置') {
            delete formatted[key]
          }
          if (typeof formatted[key] === 'object' && formatted[key] !== null) {
            Object.keys(formatted[key]).forEach(subKey => {
              if (formatted[key][subKey] === '未知' || formatted[key][subKey] === '未设置') {
                delete formatted[key][subKey]
              }
            })
            if (Object.keys(formatted[key]).length === 0) {
              delete formatted[key]
            }
          }
        })

        return JSON.stringify(formatted, null, 2)
      }

      // 默认格式化
      return JSON.stringify(data, null, 2)
    }

    return String(data)
  } catch (error) {
    return String(data)
  }
}

// 图片渲染相关方法
const renderImageWithDetections = async () => {
  if (!renderCanvas.value || !filePreview.value || !testResult.value) {
    return
  }

  const canvas = renderCanvas.value
  const ctx = canvas.getContext('2d')

  // 创建图片对象
  const img = new Image()
  img.onload = () => {
    // 设置canvas尺寸
    const maxWidth = 800
    const maxHeight = 600
    let { width, height } = img

    // 计算缩放比例
    const scale = Math.min(maxWidth / width, maxHeight / height, 1)
    width *= scale
    height *= scale

    canvas.width = width
    canvas.height = height

    // 绘制原图
    ctx.drawImage(img, 0, 0, width, height)

    // 绘制检测结果
    if (showDetectionBoxes.value) {
      drawDetections(ctx, width, height, img.width, img.height)
    }
  }

  img.onerror = (error) => {
    console.error('图片加载失败:', error)
  }
  img.src = filePreview.value.url
}

const drawDetections = (ctx, canvasWidth, canvasHeight, originalWidth, originalHeight) => {
  const detections = extractDetections(testResult.value.data)
  if (!detections || detections.length === 0) return

  const scaleX = canvasWidth / originalWidth
  const scaleY = canvasHeight / originalHeight

  detections.forEach((detection, index) => {
    const { bbox, class: className, class_name, label, confidence, score } = detection
    if (!bbox || bbox.length < 4) return

    // 获取类别名称，优先使用v2.0格式的label，然后是class_name，然后是class，最后是unknown
    const displayClassName = label || class_name || className || 'unknown'

    // 转换坐标
    const [x1, y1, x2, y2] = bbox
    const x = x1 * scaleX
    const y = y1 * scaleY
    const width = (x2 - x1) * scaleX
    const height = (y2 - y1) * scaleY

    // 绘制边界框
    ctx.strokeStyle = getColorForClass(displayClassName, index)
    ctx.lineWidth = 2
    ctx.strokeRect(x, y, width, height)

    // 绘制标签
    if (showLabels.value) {
      const label = `${displayClassName} ${((confidence || score || 0) * 100).toFixed(1)}%`
      const labelHeight = 20

      // 绘制标签背景
      ctx.fillStyle = getColorForClass(displayClassName, index)
      ctx.fillRect(x, y - labelHeight, ctx.measureText(label).width + 10, labelHeight)

      // 绘制标签文字
      ctx.fillStyle = 'white'
      ctx.font = '12px Arial'
      ctx.fillText(label, x + 5, y - 5)
    }
  })
}

const extractDetections = (responseData) => {
  try {
    // v2.0统一响应格式：检查 data.detections
    if (responseData.data && responseData.data.detections) {
      return responseData.data.detections
    }

    // 尝试从不同的响应格式中提取检测结果
    if (responseData.response && responseData.response.data) {
      const data = responseData.response.data
      return data.results || data.detections || data.predictions || []
    }

    if (responseData.results) return responseData.results
    if (responseData.detections) return responseData.detections
    if (responseData.predictions) return responseData.predictions

    return []
  } catch (error) {
    console.error('提取检测结果失败:', error)
    return []
  }
}

const getColorForClass = (className, index) => {
  const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
  ]

  if (className) {
    // 基于类名生成一致的颜色
    let hash = 0
    for (let i = 0; i < className.length; i++) {
      hash = className.charCodeAt(i) + ((hash << 5) - hash)
    }
    return colors[Math.abs(hash) % colors.length]
  }

  return colors[index % colors.length]
}

const toggleDetectionBoxes = () => {
  showDetectionBoxes.value = !showDetectionBoxes.value
  renderImageWithDetections()
}

const toggleLabels = () => {
  showLabels.value = !showLabels.value
  renderImageWithDetections()
}

const downloadRenderedImage = () => {
  if (!renderCanvas.value) return

  const link = document.createElement('a')
  link.download = `rendered_${filePreview.value?.name || 'image'}.png`
  link.href = renderCanvas.value.toDataURL()
  link.click()
}

const handleCanvasClick = (event) => {
  // 可以添加点击检测框的交互功能
  console.log('Canvas clicked at:', event.offsetX, event.offsetY)
}



// WebSocket连接管理
const connectWebSocket = () => {
  if (!selectedContainer.value) {
    ElMessage.error('请先选择算法容器')
    return
  }

  const wsUrl = `ws://localhost:8100/api/v1/ws/realtime-detect/${selectedContainer.value.name}`

  try {
    websocket.value = new WebSocket(wsUrl)

    websocket.value.onopen = () => {
      wsConnected.value = true
      ElMessage.success('WebSocket连接已建立')
      console.log('WebSocket连接成功')

      // 发送心跳
      startHeartbeat()
    }

    websocket.value.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        handleWebSocketMessage(data)
      } catch (error) {
        console.error('解析WebSocket消息失败:', error)
      }
    }

    websocket.value.onclose = () => {
      wsConnected.value = false
      console.log('WebSocket连接已关闭')

      // 如果正在检测，尝试重连
      if (realTimeDetecting.value) {
        setTimeout(() => {
          console.log('尝试重连WebSocket...')
          connectWebSocket()
        }, 1000)
      }
    }

    websocket.value.onerror = (error) => {
      console.error('WebSocket连接错误:', error)
      ElMessage.error('WebSocket连接失败')
      wsConnected.value = false
    }

  } catch (error) {
    console.error('创建WebSocket连接失败:', error)
    ElMessage.error('无法创建WebSocket连接')
  }
}

const disconnectWebSocket = () => {
  if (websocket.value) {
    websocket.value.close()
    websocket.value = null
  }
  wsConnected.value = false
}

const handleWebSocketMessage = (data) => {
  switch (data.type) {
    case 'connection':
      console.log('WebSocket连接确认:', data.message)
      break

    case 'detection_result':
      // 处理检测结果
      if (data.result.success) {
        updateFPS()
        renderRealTimeDetections(data.result.data, 1/0.5) // 坐标缩放
      } else {
        console.error('检测失败:', data.result.error)
      }
      break

    case 'pong':
      // 计算延迟
      wsLatency.value = Date.now() - lastPingTime.value
      break

    case 'error':
      console.error('WebSocket错误:', data.error)
      break

    default:
      console.log('未知WebSocket消息类型:', data.type)
  }
}

const startHeartbeat = () => {
  const heartbeatInterval = setInterval(() => {
    if (wsConnected.value && websocket.value) {
      lastPingTime.value = Date.now()
      websocket.value.send(JSON.stringify({
        type: 'ping',
        timestamp: lastPingTime.value
      }))
    } else {
      clearInterval(heartbeatInterval)
    }
  }, 5000) // 每5秒发送一次心跳
}

const sendFrameViaWebSocket = (frameData) => {
  if (wsConnected.value && websocket.value) {
    const message = {
      type: 'frame',
      frame: frameData, // base64格式的图片数据
      parameters: JSON.parse(testForm.value.parameters || '{}')
    }

    websocket.value.send(JSON.stringify(message))
  }
}

// 算法性能基准测试方法
const runBenchmarkTest = async () => {
  if (!selectedContainer.value) {
    ElMessage.warning('请先选择算法容器')
    return
  }

  if (!cameraActive.value || !videoElement.value) {
    ElMessage.warning('请先开启摄像头')
    return
  }

  benchmarkTesting.value = true
  benchmarkCompleted.value = false

  try {
    ElMessage.info('开始捕获真实视频帧进行性能测试...')

    // 捕获10帧真实视频帧
    const videoFrames = []
    const frameCount = 10
    const captureInterval = 200 // 每200ms捕获一帧

    for (let i = 0; i < frameCount; i++) {
      // 创建临时canvas来捕获视频帧
      const tempCanvas = document.createElement('canvas')
      const tempCtx = tempCanvas.getContext('2d')

      // 设置canvas尺寸与视频一致
      tempCanvas.width = videoElement.value.videoWidth
      tempCanvas.height = videoElement.value.videoHeight

      // 绘制当前视频帧到canvas
      tempCtx.drawImage(videoElement.value, 0, 0, tempCanvas.width, tempCanvas.height)

      // 转换为base64格式
      const frameData = tempCanvas.toDataURL('image/jpeg', 0.8)
      videoFrames.push(frameData)

      // 等待一段时间再捕获下一帧
      if (i < frameCount - 1) {
        await new Promise(resolve => setTimeout(resolve, captureInterval))
      }
    }

    ElMessage.info(`已捕获${frameCount}帧真实视频，开始性能测试...`)

    // 获取当前检测参数
    const parameters = JSON.parse(testForm.value.parameters || '{}')

    // 调用基准测试API
    const response = await fetch(`/api/v1/benchmark/benchmark/${selectedContainer.value.name}/with-frames`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        video_frames: videoFrames,
        parameters: parameters
      })
    })

    if (response.ok) {
      const result = await response.json()
      benchmarkResult.value = result
      benchmarkCompleted.value = true

      // 自动应用建议的FPS设置
      applyRecommendedFPS(result.fps_recommendations.recommended_fps)

      ElMessage.success(`性能测试完成！已自动应用最佳FPS: ${detectionFPS.value}FPS (平均处理时间: ${result.processing_times.average_ms}ms)`)

    } else {
      const error = await response.json()
      throw new Error(error.detail || '基准测试失败')
    }

  } catch (error) {
    console.error('基准测试失败:', error)
    ElMessage.error(`基准测试失败: ${error.message}`)
    benchmarkCompleted.value = false
  } finally {
    benchmarkTesting.value = false
  }
}

const showBenchmarkResult = () => {
  benchmarkDialogVisible.value = true
}

const getPerformanceLevelClass = (level) => {
  const levelMap = {
    '极快': 'excellent',
    '很快': 'very-good',
    '快速': 'good',
    '中等': 'medium',
    '较慢': 'slow',
    '慢': 'very-slow'
  }
  return levelMap[level] || 'medium'
}

const applyRecommendedFPS = (recommendedFPS) => {
  // 使用算法性能测试的实际建议FPS，不强制最小值
  // 保留一位小数以提高精度
  let targetFPS = Math.round(recommendedFPS * 10) / 10

  // 只设置合理的上限，不设置下限（算法可能确实需要很低的FPS）
  if (targetFPS > 60) targetFPS = 60

  // 对于WebSocket模式，可以支持更高的FPS
  if (detectionMode.value !== 'websocket' && targetFPS > 30) {
    targetFPS = 30
  }

  // 确保最小值为0.1，避免除零错误
  if (targetFPS < 0.1) targetFPS = 0.1

  detectionFPS.value = targetFPS
  console.log(`已自动应用最佳FPS: ${targetFPS}FPS（基于算法性能测试）`)
}

const getPerformanceTagType = (level) => {
  const typeMap = {
    '极快': 'success',
    '很快': 'success',
    '快速': 'primary',
    '中等': 'warning',
    '较慢': 'danger',
    '慢': 'danger'
  }
  return typeMap[level] || 'info'
}

const getFPSTagType = (fps) => {
  if (fps >= 25) return 'success'
  if (fps >= 15) return 'primary'
  if (fps >= 5) return 'warning'
  return 'danger'
}

const getFPSDescription = (fps) => {
  if (fps >= 30) return '(超极速)'
  if (fps >= 20) return '(极速)'
  if (fps >= 15) return '(高速)'
  if (fps >= 10) return '(流畅)'
  if (fps >= 5) return '(较慢)'
  if (fps >= 2) return '(保守)'
  return '(极保守)'
}

const getTimeTagType = (time, avgTime) => {
  if (time <= avgTime * 0.8) return 'success'
  if (time <= avgTime * 1.2) return 'primary'
  if (time <= avgTime * 1.5) return 'warning'
  return 'danger'
}

// 摄像头实时检测方法
const startCamera = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({
      video: {
        width: { ideal: 1280 },
        height: { ideal: 720 },
        frameRate: { ideal: 30 }
      },
      audio: false
    })

    mediaStream.value = stream
    cameraActive.value = true

    // 等待下一个 tick，确保 video 元素已经渲染
    await nextTick()

    if (videoElement.value) {
      videoElement.value.srcObject = stream
      ElMessage.success('摄像头已启动')
    } else {
      console.error('Video element not found')
      ElMessage.error('视频元素未找到')
    }
  } catch (error) {
    console.error('启动摄像头失败:', error)
    cameraActive.value = false
    ElMessage.error('无法访问摄像头，请检查权限设置')
  }
}

const stopCamera = () => {
  if (mediaStream.value) {
    mediaStream.value.getTracks().forEach(track => track.stop())
    mediaStream.value = null
  }

  if (videoElement.value) {
    videoElement.value.srcObject = null
  }

  // 停止实时检测
  stopRealTimeDetection()

  cameraActive.value = false
  ElMessage.info('摄像头已关闭')
}

const startRealTimeDetection = () => {
  if (!cameraActive.value || !videoElement.value || !selectedContainer.value) {
    ElMessage.warning('请先启动摄像头并选择算法容器')
    return
  }

  realTimeDetecting.value = true
  frameCount.value = 0
  lastFPSTime.value = Date.now()
  lastDetectionTime.value = 0

  // 根据选择的方案初始化
  if (detectionMode.value === 'websocket') {
    // WebSocket方案
    if (!wsConnected.value) {
      connectWebSocket()
      // 等待连接建立后再开始检测
      const checkConnection = setInterval(() => {
        if (wsConnected.value) {
          clearInterval(checkConnection)
          startWebSocketDetection()
        }
      }, 100)
    } else {
      startWebSocketDetection()
    }
  } else {
    // HTTP API方案
    startHttpDetection()
  }
}

const startHttpDetection = () => {
  // 使用requestAnimationFrame实现高性能检测
  const detectLoop = (timestamp) => {
    if (!realTimeDetecting.value) return

    const targetInterval = 1000 / detectionFPS.value

    // 检查是否到了下一次检测的时间
    if (timestamp - lastDetectionTime.value >= targetInterval) {
      captureAndDetect()
      lastDetectionTime.value = timestamp
    }

    // 继续下一帧
    animationFrameId.value = requestAnimationFrame(detectLoop)
  }

  // 开始检测循环
  animationFrameId.value = requestAnimationFrame(detectLoop)

  ElMessage.success(`HTTP实时检测已开始 (${detectionFPS.value}FPS)`)
}

const startWebSocketDetection = () => {
  // WebSocket检测循环
  const detectLoop = (timestamp) => {
    if (!realTimeDetecting.value) return

    const targetInterval = 1000 / detectionFPS.value

    // 检查是否到了下一次检测的时间
    if (timestamp - lastDetectionTime.value >= targetInterval) {
      captureAndSendFrame()
      lastDetectionTime.value = timestamp
    }

    // 继续下一帧
    animationFrameId.value = requestAnimationFrame(detectLoop)
  }

  // 开始检测循环
  animationFrameId.value = requestAnimationFrame(detectLoop)

  ElMessage.success(`WebSocket实时检测已开始 (${detectionFPS.value}FPS, 延迟: ${wsLatency.value}ms)`)
}

const stopRealTimeDetection = () => {
  // 清理定时器和动画帧
  if (detectionInterval.value) {
    clearInterval(detectionInterval.value)
    detectionInterval.value = null
  }

  if (animationFrameId.value) {
    cancelAnimationFrame(animationFrameId.value)
    animationFrameId.value = null
  }

  realTimeDetecting.value = false
  currentFPS.value = 0
  isDetecting.value = false
  detectionQueue.value = 0

  // 如果使用WebSocket方案，断开连接
  if (detectionMode.value === 'websocket') {
    disconnectWebSocket()
  }

  // 清除检测画布
  if (detectionCanvas.value) {
    const ctx = detectionCanvas.value.getContext('2d')
    ctx.clearRect(0, 0, detectionCanvas.value.width, detectionCanvas.value.height)
  }

  ElMessage.info('实时检测已停止')
}

const captureAndSendFrame = () => {
  if (!videoElement.value || !realTimeDetecting.value || !wsConnected.value) {
    return
  }

  try {
    // 创建临时canvas来捕获视频帧
    const tempCanvas = document.createElement('canvas')
    const tempCtx = tempCanvas.getContext('2d')

    const video = videoElement.value
    // 降低分辨率以提高检测速度
    const scale = 0.5
    tempCanvas.width = video.videoWidth * scale
    tempCanvas.height = video.videoHeight * scale

    // 绘制当前视频帧（缩放）
    tempCtx.drawImage(video, 0, 0, tempCanvas.width, tempCanvas.height)

    // 转换为base64格式
    const frameData = tempCanvas.toDataURL('image/jpeg', 0.6)

    // 通过WebSocket发送帧数据
    sendFrameViaWebSocket(frameData)

  } catch (error) {
    console.error('捕获和发送视频帧失败:', error)
  }
}

const captureAndDetect = async () => {
  if (!videoElement.value || !detectionCanvas.value || !realTimeDetecting.value) {
    return
  }

  // 如果正在检测且队列太长，跳过这一帧
  if (isDetecting.value && detectionQueue.value > 2) {
    console.log('跳过帧，检测队列过长')
    return
  }

  detectionQueue.value++

  try {
    // 创建临时canvas来捕获视频帧
    const tempCanvas = document.createElement('canvas')
    const tempCtx = tempCanvas.getContext('2d')

    const video = videoElement.value
    // 降低分辨率以提高检测速度
    const scale = 0.5 // 缩放到50%
    tempCanvas.width = video.videoWidth * scale
    tempCanvas.height = video.videoHeight * scale

    // 绘制当前视频帧（缩放）
    tempCtx.drawImage(video, 0, 0, tempCanvas.width, tempCanvas.height)

    // 转换为blob，降低质量以提高传输速度
    tempCanvas.toBlob(async (blob) => {
      if (!blob || !realTimeDetecting.value) {
        detectionQueue.value--
        return
      }

      isDetecting.value = true

      try {
        // 调用检测API
        const testData = {
          endpoint: testForm.value.endpoint,
          method: testForm.value.method,
          parameters: testForm.value.parameters || '{}'
        }
        testData.file = blob

        const response = await testingAPI.testContainer(testForm.value.containerId, testData)

        // 更新FPS计数
        updateFPS()

        // 渲染检测结果（需要按比例放大坐标）
        if (response.data && response.data.response) {
          renderRealTimeDetections(response.data.response, 1/scale)
        }

      } catch (error) {
        console.error('实时检测失败:', error)
        // 不显示错误消息，避免频繁弹窗
      } finally {
        isDetecting.value = false
        detectionQueue.value--
      }
    }, 'image/jpeg', 0.6) // 降低图片质量到60%

  } catch (error) {
    console.error('捕获视频帧失败:', error)
    detectionQueue.value--
  }
}

const updateFPS = () => {
  frameCount.value++
  const now = Date.now()

  if (now - lastFPSTime.value >= 1000) {
    currentFPS.value = Math.round(frameCount.value * 1000 / (now - lastFPSTime.value))
    frameCount.value = 0
    lastFPSTime.value = now
  }
}

const renderRealTimeDetections = (apiResponse, coordinateScale = 1) => {
  if (!detectionCanvas.value || !videoElement.value) return

  const canvas = detectionCanvas.value
  const ctx = canvas.getContext('2d')
  const video = videoElement.value

  // 设置canvas尺寸与视频一致
  canvas.width = video.clientWidth
  canvas.height = video.clientHeight

  // 清除之前的绘制
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  // 提取检测结果
  const detections = extractDetections(apiResponse)
  if (!detections || detections.length === 0) return

  // 计算缩放比例
  const scaleX = canvas.width / video.videoWidth
  const scaleY = canvas.height / video.videoHeight

  // 绘制检测框
  detections.forEach((detection, index) => {
    const { bbox, label, confidence } = detection
    if (!bbox || bbox.length < 4) return

    // 应用坐标缩放（用于补偿检测时的图片缩放）
    const [x1, y1, x2, y2] = bbox.map(coord => coord * coordinateScale)
    const x = x1 * scaleX
    const y = y1 * scaleY
    const width = (x2 - x1) * scaleX
    const height = (y2 - y1) * scaleY

    // 绘制边界框
    ctx.strokeStyle = getColorForClass(label || 'unknown', index)
    ctx.lineWidth = 3 // 稍微加粗线条，更容易看清
    ctx.strokeRect(x, y, width, height)

    // 绘制标签背景
    const labelText = `${label || 'unknown'} ${(confidence * 100).toFixed(1)}%`
    ctx.font = 'bold 16px Arial' // 加粗字体，更容易看清
    const textMetrics = ctx.measureText(labelText)
    const textWidth = textMetrics.width
    const textHeight = 18

    ctx.fillStyle = ctx.strokeStyle
    ctx.fillRect(x, y - textHeight - 4, textWidth + 8, textHeight + 4)

    // 绘制标签文字
    ctx.fillStyle = 'white'
    ctx.fillText(labelText, x + 4, y - 4)
  })
}

// 状态管理：只保存和恢复请求参数，图片和测试结果在端点切换时清空

// 生命周期
onMounted(async () => {
  await loadTestingContainers()

  // 监听全局刷新事件
  window.addEventListener('refresh-data', loadTestingContainers)
})

onUnmounted(() => {
  // 清理摄像头资源
  stopCamera()

  // 移除事件监听器
  window.removeEventListener('refresh-data', loadTestingContainers)
})
</script>

<style scoped>
/* 基准测试样式 */
.benchmark-section {
  margin-bottom: 20px;
  padding: 15px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.benchmark-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.benchmark-buttons {
  display: flex;
  gap: 10px;
}

.benchmark-summary {
  display: flex;
  gap: 15px;
  align-items: center;
  font-size: 13px;
  color: #606266;
}

.performance-level {
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: bold;
  font-size: 12px;
}

.performance-level.excellent {
  background: #f0f9ff;
  color: #0369a1;
  border: 1px solid #0ea5e9;
}

.performance-level.very-good {
  background: #f0fdf4;
  color: #166534;
  border: 1px solid #22c55e;
}

.performance-level.good {
  background: #fefce8;
  color: #a16207;
  border: 1px solid #eab308;
}

.performance-level.medium {
  background: #fff7ed;
  color: #c2410c;
  border: 1px solid #f97316;
}

.performance-level.slow,
.performance-level.very-slow {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #ef4444;
}

.avg-time {
  color: #409eff;
  font-weight: 500;
}

.applied-fps {
  color: #67c23a;
  font-weight: 500;
}

.test-status {
  color: #67c23a;
  font-weight: 500;
  font-size: 12px;
}

.benchmark-tip {
  margin-top: 8px;
  padding: 8px 12px;
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 4px;
  font-size: 12px;
  color: #0369a1;
}

/* FPS显示样式 */
.fps-display {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.fps-label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.fps-tag {
  font-size: 14px;
  font-weight: 600;
}

.fps-hint {
  font-size: 12px;
  color: #909399;
  font-style: italic;
}

/* 基准测试结果对话框样式 */
.benchmark-result {
  padding: 20px 0;
}

.result-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.result-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.result-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.time-data {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.time-tag {
  margin: 0;
  font-size: 12px;
}

.empty-result {
  text-align: center;
  padding: 40px 0;
}

.testing-status {
  text-align: center;
  padding: 20px 0;
}

.testing-text {
  margin-top: 16px;
  color: #409EFF;
  font-size: 14px;
}

.result-content {
  padding: 0;
}

.response-section {
  margin-top: 20px;
}

.response-section h4 {
  margin-bottom: 12px;
  color: #303133;
}

.error-section {
  margin-top: 20px;
}

.error-section h4 {
  margin-bottom: 12px;
  color: #F56C6C;
}

.json-content {
  background-color: #f5f5f5;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 12px;
  line-height: 1.4;
  max-height: 400px;
  overflow: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.parameter-hints {
  margin-bottom: 10px;
}

.parameter-hints ul {
  margin: 0;
  padding-left: 20px;
}

.parameter-hints li {
  margin-bottom: 5px;
}

:deep(.el-select-dropdown__item) {
  height: auto;
  padding: 8px 20px;
  line-height: 1.4;
}

:deep(.el-upload__tip) {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.test-buttons {
  display: flex;
  align-items: center;
  width: 100%;
}

.button-tips {
  margin-top: 10px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #e1f5fe;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
}

.file-uploaded {
  color: #67c23a;
  font-weight: 500;
  margin-left: 10px;
}

/* 多文件上传样式 */
.multi-upload-area {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.file-upload-item {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fafafa;
}

.upload-label {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.required-mark {
  color: #f56c6c;
  margin-left: 4px;
}

.file-placeholder {
  color: #909399;
  font-size: 12px;
}

.multi-upload-tip {
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
}

/* 集成上传预览样式 */
.integrated-upload-preview {
  min-height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background-color: #ffffff;
  transition: all 0.3s ease;
}

.integrated-upload-preview:hover {
  border-color: #409eff;
}

.upload-zone {
  height: 100%;
  min-height: 120px;
}

.integrated-upload {
  width: 100%;
  height: 100%;
}

.integrated-upload .el-upload {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 8px;
}

.integrated-upload .el-upload:hover {
  border-color: transparent;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 120px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-placeholder:hover {
  background-color: #f5f7fa;
}

.upload-icon {
  color: #c0c4cc;
  margin-bottom: 8px;
}

.upload-text {
  text-align: center;
}

.upload-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.upload-hint {
  font-size: 12px;
  color: #909399;
}

.preview-zone {
  padding: 12px;
  border: 2px solid #67c23a;
  border-radius: 8px;
  background-color: #f0f9ff;
}

.file-preview-content {
  display: flex;
  gap: 12px;
  align-items: center;
}

.image-preview-container {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  border-radius: 6px;
  overflow: hidden;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.file-icon-container {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  border-radius: 6px;
}

.file-icon {
  color: #909399;
  margin-bottom: 4px;
}

.file-type {
  font-size: 10px;
  color: #909399;
  text-transform: uppercase;
}

.file-info-actions {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-info {
  flex: 1;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
  word-break: break-all;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.file-actions {
  display: flex;
  gap: 8px;
}



.formatted-content {
  position: relative;
}

.raw-content {
  position: relative;
}

.raw-json {
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  color: #666;
}

.stats-content {
  padding: 10px 0;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
  color: #303133;
}

:deep(.el-descriptions__content) {
  color: #606266;
}

/* 参数容器样式 */
.params-container {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.params-input-section {
  flex: 1;
  min-width: 0;
}

.params-help-section {
  flex: 0 0 400px;
  max-width: 400px;
}

.params-textarea {
  width: 100%;
}

.params-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.parameter-hints-card {
  height: 100%;
}

.parameter-hints-card .el-card__body {
  padding: 12px;
}

.card-header {
  font-weight: 500;
  color: #303133;
}

/* 参数说明样式 */
.parameter-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
}

.parameter-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-size: 12px;
  border: 1px solid #e9ecef;
}

.param-name {
  font-weight: 600;
  color: #2c3e50;
}

.param-type {
  color: #7f8c8d;
  font-style: italic;
}

.param-default {
  color: #27ae60;
  font-size: 12px;
}

.param-required {
  color: #e74c3c;
  font-size: 12px;
  font-weight: 500;
}

/* 文件上传和预览样式 */
.upload-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 摄像头相关样式 */
.camera-section {
  margin-bottom: 20px;
}

.camera-controls {
  margin-bottom: 16px;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.camera-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.detection-mode-settings {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: #e8f4fd;
  border-radius: 6px;
  border: 1px solid #b3d8ff;
  margin-bottom: 8px;
}

.mode-label {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  white-space: nowrap;
}

.fps-settings {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.fps-label {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  white-space: nowrap;
}

.detection-mode {
  font-size: 11px;
  font-weight: bold;
  color: #10b981;
}

.latency-info {
  font-size: 11px;
  color: #6366f1;
  font-weight: 500;
}

.camera-container {
  position: relative;
  display: inline-block;
  border: 2px solid #dcdfe6;
  border-radius: 8px;
  overflow: hidden;
  background: #000;
  max-width: 100%;
}

.camera-video {
  width: 100%;
  max-width: 640px;
  height: auto;
  display: block;
}

.detection-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.camera-info {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  z-index: 20;
}

.fps-counter {
  font-weight: bold;
}

.detection-status {
  font-size: 11px;
}

.detection-status.active {
  color: #ff4757;
}

.upload-area {
  width: 100%;
}

.file-preview {
  width: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  overflow: hidden;
  margin-top: 10px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
}

.preview-title {
  font-weight: 500;
  color: #303133;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.render-controls {
  display: flex;
  gap: 8px;
}

.image-container {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-content {
  padding: 15px;
}

.preview-image {
  max-width: 100%;
  max-height: 200px;
  border-radius: 4px;
  object-fit: contain;
}

.preview-video {
  max-width: 100%;
  max-height: 200px;
  border-radius: 4px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  font-size: 32px;
  color: #909399;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

/* 图片渲染样式 */
.render-content {
  padding: 20px 0;
}

.no-image-tip {
  text-align: center;
  padding: 40px 0;
}

.image-render-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.render-controls {
  display: flex;
  justify-content: center;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 6px;
}

.canvas-container {
  display: flex;
  justify-content: center;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.render-canvas {
  max-width: 100%;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: crosshair;
}

/* 响应式布局优化 */
@media (max-width: 1024px) {
  .params-container {
    flex-direction: column;
  }

  .params-help-section {
    flex: none;
    max-width: none;
  }
}

@media (max-width: 768px) {
  .parameter-item {
    font-size: 11px;
    padding: 4px 8px;
  }
}

/* 页面容器样式 */
.page-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 配置区域样式 */
.config-section {
  width: 100%;
}

/* 结果区域样式 */
.result-section {
  width: 100%;
  flex: 1;
}

@media (max-width: 768px) {
  .page-container {
    padding: 10px;
    gap: 15px;
  }
}
</style>
