# 统一AI算法管理平台
## 🚀 下一代AI算法部署与管理解决方案

[![Docker](https://img.shields.io/badge/Docker-20.10+-blue.svg)](https://www.docker.com/)
[![Python](https://img.shields.io/badge/Python-3.11+-green.svg)](https://www.python.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-Latest-red.svg)](https://fastapi.tiangolo.com/)
[![License](https://img.shields.io/badge/License-Enterprise-yellow.svg)](#)

> **3步部署，零配置启动，智能管理** - 让AI算法部署像安装软件一样简单

---

## 🎯 核心价值

### 🚀 一键部署，3步到位
```bash
./build-deployment-package.sh    # 1. 构建管理平台
./build-algorithm-packages.sh    # 2. 构建算法包  
./run-platform.sh               # 3. 启动系统
```
**30秒完成整个AI算法平台部署** - 无需复杂配置，无需专业运维知识

### ⚡ 零配置，智能管理
- **自动端口分配** - 系统自动管理8002-8099端口，避免冲突
- **自动网络配置** - 统一algorithm-network，容器间无缝通信  
- **自动环境检测** - GPU/CPU环境自适应，故障自动降级
- **自动健康监控** - 实时监控算法状态，异常自动恢复

### 🎛️ 统一管理，可视化运维
- **Web管理界面** - http://localhost 一站式管理所有算法
- **标准化API** - 统一接口规范，简化集成开发
- **实时监控** - 算法性能、资源使用、运行状态一目了然
- **批量操作** - 一键启停、批量部署、集中配置

---

## 🏗️ 技术架构

### 整体系统架构
```mermaid
graph TB
    subgraph "管理层"
        WEB[Web管理界面<br/>:80]
        API[管理API<br/>:8100]
    end
    
    subgraph "算法层"
        ALG1[人车非检测<br/>:8002]
        ALG2[人脸识别<br/>:8003]
        ALG3[事故分类<br/>:8004]
        ALG4[高空抛物检测<br/>:8005]
        ALG5[海关OCR<br/>:8006]
        ALG6[其他算法<br/>:8007-8099]
    end
    
    subgraph "基础设施层"
        DOCKER[Docker容器化]
        NETWORK[algorithm-network<br/>统一网络]
        REGISTRY[端口注册表<br/>自动分配]
    end
    
    WEB --> API
    API --> ALG1
    API --> ALG2
    API --> ALG3
    API --> ALG4
    API --> ALG5
    API --> ALG6

    ALG1 --> DOCKER
    ALG2 --> DOCKER
    ALG3 --> DOCKER
    ALG4 --> DOCKER
    ALG5 --> DOCKER
    ALG6 --> DOCKER
    
    DOCKER --> NETWORK
    NETWORK --> REGISTRY
    
    style ALG4 fill:#e1f5fe
    style ALG5 fill:#e1f5fe
    style WEB fill:#f3e5f5
    style API fill:#f3e5f5
```

### 算法包标准化结构
```mermaid
graph LR
    subgraph "标准算法包结构"
        ROOT[algorithm-name/]
        ROOT --> DOCKER[Dockerfile<br/>🐳 容器化配置]
        ROOT --> CONFIG[pyproject.toml<br/>📦 依赖管理]
        ROOT --> SRC[src/<br/>💻 源代码]
        ROOT --> MODELS[models/<br/>🤖 模型文件]
        ROOT --> README[README.md<br/>📚 文档]
        
        SRC --> API[api_server.py<br/>🌐 FastAPI服务]
        SRC --> ENGINE[inference_engine.py<br/>⚡ 推理引擎]
        SRC --> UNIFIED[unified_models.py<br/>📋 统一响应格式]
        SRC --> LOGGER[logger_config.py<br/>📝 日志配置]
    end
    
    style DOCKER fill:#e3f2fd
    style API fill:#e8f5e8
    style ENGINE fill:#fff3e0
```

### 部署流程图
```mermaid
flowchart TD
    START([开始部署]) --> BUILD_PLATFORM[构建管理平台<br/>./build-deployment-package.sh]
    BUILD_PLATFORM --> BUILD_ALGORITHMS[构建算法包<br/>./build-algorithm-packages.sh]
    BUILD_ALGORITHMS --> DEPLOY[启动系统<br/>./run-platform.sh]
    
    DEPLOY --> CHECK_WEB{Web界面可访问?<br/>http://localhost}
    CHECK_WEB -->|是| CHECK_API{API服务正常?<br/>http://localhost:8100}
    CHECK_API -->|是| CHECK_ALGORITHMS{算法包运行正常?}
    CHECK_ALGORITHMS -->|是| SUCCESS([✅ 部署成功])
    
    CHECK_WEB -->|否| DEBUG_WEB[检查容器状态<br/>docker ps]
    CHECK_API -->|否| DEBUG_API[检查API日志<br/>docker logs]
    CHECK_ALGORITHMS -->|否| DEBUG_ALG[检查算法日志<br/>管理界面查看]
    
    DEBUG_WEB --> RETRY[重新部署]
    DEBUG_API --> RETRY
    DEBUG_ALG --> RETRY
    RETRY --> DEPLOY
    
    style START fill:#e8f5e8
    style SUCCESS fill:#c8e6c9
    style BUILD_PLATFORM fill:#e3f2fd
    style BUILD_ALGORITHMS fill:#e3f2fd
    style DEPLOY fill:#e3f2fd
```

---

## ⚡ 快速开始

### 系统要求
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **uv**: Python包管理器
- **NVIDIA GPU + CUDA**: 可选，用于GPU加速算法

### 3步部署
```bash
# 第1步：构建管理平台 (约10秒)
./build-deployment-package.sh

# 第2步：构建算法包 (约15秒)  
./build-algorithm-packages.sh

# 第3步：启动系统 (约5秒)
cd separated-deployment-system/platform-package/algorithm-platform-deploy-package
./run-platform.sh
```

### 验证部署
```bash
# 访问管理界面
open http://localhost

# 检查API服务
curl http://localhost:8100/api/health

# 测试算法接口
curl http://localhost:8002/api/v1/health  # 人车非检测
curl http://localhost:8003/api/v1/health  # 人脸识别
curl http://localhost:8005/api/v1/health  # 高空抛物检测
curl http://localhost:8006/api/v1/health  # 海关OCR
```

### 管理界面功能
- **算法状态监控** - 实时查看所有算法运行状态
- **性能指标** - CPU、内存、GPU使用率监控
- **日志查看** - 集中查看算法运行日志
- **一键操作** - 启动、停止、重启算法服务

---

## 🌟 技术特色

### 🎯 算法包本地化标准流程
以 **renchefei** 为标准参考模板，确保所有算法包具有：
- **统一的目录结构** - 标准化开发和部署
- **统一的API接口** - 简化集成和维护
- **统一的响应格式** - v2.0规范，便于解析
- **统一的配置管理** - INI格式，易于修改

### 🧠 GPU环境优化
- **GPU加速支持** - 自动检测和使用GPU资源进行算法加速
- **CUDA环境适配** - 支持CUDA环境下的高性能计算
- **内存管理优化** - 智能管理GPU内存使用，避免资源冲突
- **性能监控** - 实时监控GPU使用率和算法性能指标

### 📡 统一API接口
```python
# 标准健康检查 (所有算法包)
GET /api/v1/health

# 统一检测接口 (视觉算法)
POST /api/v1/detect

# OCR识别接口 (OCR算法)
POST /api/v1/ocr

# 算法信息接口
GET /api/v1/info
```

### 🐳 Docker容器化
- **多阶段构建** - 优化镜像大小和构建速度
- **非root用户** - 增强安全性
- **健康检查** - 自动监控容器状态
- **网络隔离** - 统一algorithm-network网络

---

## 👥 目标受众价值

### 🎯 技术决策者
**架构优势**
- **微服务架构** - 算法包独立部署，故障隔离
- **云原生设计** - 容器化部署，易于扩展
- **自动化运维** - 自动化程度高，降低运维成本
- **标准化规范** - 统一开发流程，提高开发效率

**商业价值**
- **快速交付** - 3步部署，大幅缩短项目交付周期
- **降低成本** - 零配置管理，减少专业运维人员需求
- **提高可靠性** - 自动故障恢复，提升系统稳定性
- **便于扩展** - 标准化架构，易于添加新算法

### 🛠️ 运维人员
**部署简便性**
- **一键部署** - 3个脚本完成整个系统部署
- **自动配置** - 端口、网络、环境自动配置
- **可视化管理** - Web界面管理所有算法
- **集中监控** - 统一监控所有算法状态

**维护便利性**
- **标准化日志** - 统一日志格式，便于问题排查
- **健康检查** - 自动监控算法健康状态
- **故障恢复** - 自动故障检测和恢复
- **批量操作** - 一键启停、批量更新

### 💻 开发者
**开发规范**
- **标准模板** - renchefei标准参考模板
- **统一接口** - FastAPI + 统一响应格式
- **开发工具** - 算法管理工具，自动创建标准结构
- **质量保证** - 完整的检查清单和验证流程

**扩展能力**
- **插件化架构** - 新算法包即插即用
- **GPU加速支持** - GPU环境下的高性能计算架构
- **API标准化** - 统一接口规范，便于集成
- **文档完善** - 详细的开发指南和API文档

---

## 📊 性能指标

### 算法包性能指标
| 算法包 | 处理性能 | 并发能力 | GPU加速 |
|--------|----------|----------|---------|
| 人车非检测 | 30+ FPS | 单路处理 | ✅ |
| 人脸识别 | 25+ FPS | 单路处理 | ✅ |
| 事故分类 | 20+ FPS | 单路处理 | ✅ |
| 高空抛物检测 | 60+ FPS | 多路并发 | ✅ |
| 海关OCR | 100ms/页 | 高并发 | ✅ |

### 系统资源占用
- **管理平台**: ~200MB内存
- **算法包**: ~500MB-1GB内存 (根据算法复杂度)
- **GPU内存**: ~2-4GB (GPU加速模式)
- **网络开销**: 最小化，容器间直连

---

## 🚀 立即体验

```bash
# 克隆项目
git clone https://github.com/your-org/algorithm-platform.git
cd algorithm-platform

# 3步部署
./build-deployment-package.sh
./build-algorithm-packages.sh  
cd separated-deployment-system/platform-package/algorithm-platform-deploy-package
./run-platform.sh

# 访问管理界面
open http://localhost
```

**让AI算法部署变得简单，让统一管理成为现实！** 🎉
