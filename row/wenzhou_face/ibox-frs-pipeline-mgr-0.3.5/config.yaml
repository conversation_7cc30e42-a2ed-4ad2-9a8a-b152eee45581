action:  # ACTION
  - FaceDetect
  - FaceRecog
ports: # 端口配置
  TRITON_PORT: 7010
  BACKEND_PORT: 8003
models: # 模型参数配置
  FaceDetect:
    model_name: FaceDetect
    max_batch_size: 32
    input:
      - name: input.1
        dtype: FP32
        dims:
          - 3
          - 640
          - 640
    output:
      - name: score_8
        dtype: FP32
        dims:
          - 12800
          - 1
      - name: score_16
        dtype: FP32
        dims:
          - 3200
          - 1
      - name: score_32
        dtype: FP32
        dims:
          - 800
          - 1
      - name: bbox_8
        dtype: FP32
        dims:
          - 12800
          - 4
      - name: bbox_16
        dtype: FP32
        dims:
          - 3200
          - 4
      - name: bbox_32
        dtype: FP32
        dims:
          - 800
          - 4
      - name: kps_8
        dtype: FP32
        dims:
          - 12800
          - 10
      - name: kps_16
        dtype: FP32
        dims:
          - 3200
          - 10
      - name: kps_32
        dtype: FP32
        dims:
          - 800
          - 10

    input_shape:
      - input_w: 640
        input_h: 640
    ssl: False
    verbose: False
    http_headers:
    request_compression_algorithm:
    response_compression_algorithm:
  FaceRecog:
    model_name: FaceRecog
    max_batch_size: 16
    input:
      - name: input
        dtype: FP32
        dims:
          - 3
          - 112
          - 112
    output:
      - name: output
        dtype: FP32
        dims:
          - 512
    input_shape:
      - input_w: 112
        input_h: 112
    ssl: False
    verbose: False
    http_headers:
    request_compression_algorithm:
    response_compression_algorithm:

process: # 运行配置参数
  FACE_THRESH: 0.7
  NEED_FACE_FEATURE: False # 是否返回人脸特征
  NEED_FACE_QUALITY: False # 是否返回人脸质量
  NEED_FACE_ANGLE: False # 是否返回人脸角度

FaceCropAlignProcess:
  MEAN: "127.5, 127.5 , 127.5"
  STD: "127.5, 127.5 , 127.5"
  PADDING: "0.0"
  MAX_BATCH_SIZE: "32"
  MIN_BOX_SIZE: "20,20"
  TRANSFORM_MAT: "0.34191607,0.46157411,0.65653393,0.45983393,0.500225,0.64050536,0.37097589, 0.82469196,0.63151696,0.82325089"

params: # 系统参数
  FILE_FORMAT:
    - "image/jpeg"
    - "image/png"
    - "image/bmp"
  IMAGE_SIZE: 7340032 # 7 * 1024 * 1024, 7M
  MIN_LEN: 32
  MAX_LEN: 5000
  SAVE_RULE: all # all / requests / none
  SAVE_NUM: 1
