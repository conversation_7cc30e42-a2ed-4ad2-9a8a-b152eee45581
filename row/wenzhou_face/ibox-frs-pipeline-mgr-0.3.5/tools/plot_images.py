# -*- coding:utf-8 -*-
import cv2
import sys
import random
import os
import glob
from icecream import ic
import numpy as np

sys.path.append("..")  # 父路径寻找 app.client

# 可视化画图工具


def plot_one_box(box, landmark, img, color=None, label_name=None, label_score=None, line_thickness=None):
    """
    description: Plots one bounding box on image img,
                 this function comes from YoLov5 project.
    param:
        box:    a box likes [x1,y1,w,h]
        img:    a opencv image object
        color:  color to draw rectangle, such as (0,255,0)
        label:  str
        line_thickness: int
    return:
        no return

    """
    tl = line_thickness or round(
        0.001 * (img.shape[0] + img.shape[1]) / 2) + 1  # line/font thickness
    color = color or [random.randint(0, 255) for _ in range(3)]

    x1, y1, w, h = [int(v) for v in box]
    c1, c2 = (x1, y1), (x1+w, y1+h)
    cv2.rectangle(img, c1, c2, color, thickness=tl,
                  lineType=cv2.LINE_AA)  # 检测框

    cv2.circle(img, (int(landmark[0]), int(landmark[1])), 1, (0, 0, 255), 4)
    cv2.circle(img, (int(landmark[2]), int(landmark[3])), 1, (0, 255, 255), 4)
    cv2.circle(img, (int(landmark[4]), int(landmark[5])), 1, (255, 0, 255), 4)
    cv2.circle(img, (int(landmark[6]), int(landmark[7])), 1, (0, 255, 0), 4)
    cv2.circle(img, (int(landmark[8]), int(landmark[9])), 1, (255, 0, 0), 4)

    if label_name:  # 分类名
        tf = max(tl - 1, 1)  # font thickness
        t_size = cv2.getTextSize(
            label_name, 0, fontScale=tl / 3, thickness=tf)[0]
        c2 = c1[0] + t_size[0], c1[1] + t_size[1] + 3

        sub_img = img[c1[1]:c2[1], c1[0]:c2[0]]
        white_rect = np.ones(sub_img.shape, dtype=np.uint8) * 255
        res = cv2.addWeighted(sub_img, 0.4, white_rect, 0.4, 1.0)
        img[c1[1]:c2[1], c1[0]:c2[0]] = res

        cv2.putText(
            img,
            label_name,
            (c1[0], c2[1] - 2),
            0,
            tl / 3,
            [0, 0, 255],
            thickness=tf,
            lineType=cv2.LINE_AA,
        )


if __name__ == "__main__":

    image_paths = glob.glob("../test/*.jpg")
    image_paths.sort()

    os.makedirs("post_results", exist_ok=True)

    from app.client.face_detect import FaceDetectClient, process_configs
    client = FaceDetectClient(process_configs)
    print(process_configs)

    for idx, image_path in enumerate(image_paths):
        ic(idx, image_path)

        with open(image_path, 'rb') as file:
            img_arr = np.frombuffer(file.read(), np.uint8)
        img_mat = cv2.cvtColor(cv2.imdecode(
            img_arr, cv2.IMREAD_COLOR), cv2.COLOR_BGR2RGB)

        results, _ = client.run(img_arr, img_mat, False, False, False, 0.7)
        ic(results)

        image = cv2.imread(image_path)
        boxes = results.get("FaceRectangles")
        fix_boxes = [boxes[i:i+4] for i in range(0, len(boxes), 4)]
        scores = results.get("FaceProbabilityList")
        landmarks = results.get("Landmarks")
        fix_landmarks = [landmarks[i:i+10]
                         for i in range(0, len(landmarks), 10)]

        cv2.imwrite(os.path.join("post_results",
                                 os.path.basename(image_path)), image)

        for box, score, landmark in zip(fix_boxes, scores, fix_landmarks):
            plot_one_box(
                box,
                landmark,
                image,
                color=(0, 0, 255),
                label_name=str(score)[:4]
                # line_thickness=1
            )

        cv2.imwrite(os.path.join("post_results",
                                 os.path.basename(image_path)), image)
