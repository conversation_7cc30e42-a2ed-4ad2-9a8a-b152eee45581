# -*- coding:utf-8 -*-
import time
import cv2
import numpy as np
from multiprocessing import Pool, Process, Queue, Value, cpu_count
import sys
sys.path.append("..")  # 父路径寻找 app.client

# triton 并发测试工具

is_exit = Value('b', False)


def timing(minutes):
    start = time.time()
    while True:
        if start + minutes * 60 < time.time():
            # 超过指定时间ls
            is_exit.value = True
            print("process exit")
            break
        else:
            time.sleep(0.01)


def infer(img_arr, img_mat, batch_size, q):
    from app.client.face_detect import FaceDetectClient, process_configs
    client = FaceDetectClient(process_configs)  # 车辆类型
    total_num = 0

    t1 = time.time()
    while not is_exit.value:
        result, _ = client.run(img_arr, img_mat)
        # print(result)
        total_num += 1
    t2 = time.time()
    q.put([(t2 - t1) * 1000, total_num])


if __name__ == "__main__":

    batch_size = 1
    minutes = 1
    nums = [2, 4, 8, 16, 32, 64, 100, 128]  # 并发数
    nums = [16, 32, 64, 100, 128]  # 并发数
    image_path = "../test/face3.jpg"  # 人脸

    with open(image_path, 'rb') as file:
        img_arr = np.frombuffer(file.read(), np.uint8)
    img_mat = cv2.cvtColor(cv2.imdecode(
        img_arr, cv2.IMREAD_COLOR), cv2.COLOR_BGR2RGB)

    for num in nums:
        total_count = 0
        total_time = 0
        q = Queue()
        processes = []
        for i in range(num):
            processes.append(
                Process(target=infer, args=(img_arr, img_mat, batch_size, q)))

        processes.append(Process(target=timing, args=(minutes, )))
        print("minutes %d, batch_size %d, 并发 num %d, processes start" %
              (minutes, batch_size, num))
        for process in processes:
            process.start()
        for process in processes:
            process.join()
        with open("minutes_" + str(minutes) + "_batch_size_" +
                  str(batch_size) + "_num_" + str(num) + ".txt",
                  "w",
                  encoding="utf-8") as f:
            f.write("batch_size\tnum\tcost\timages\n")
            while not q.empty():
                data = q.get()  # cost_time, run_batch_num
                total_count += data[1]
                total_time += data[0]
                f.write(
                    str(batch_size) + "\t" + str(num) + "\t" + str(data[0]) +
                    "\t" + str(data[1]) + "\n")
                print('吞吐量:', 1000 * num * data[1] / data[0]
                      )  # , ', 延时:', data[0]/(batch_size*num*data[1]), 'ms\n')
            print('总吞吐量:', 1000 * num * total_count / total_time)
        is_exit.value = False
        for process in processes:
            if process.is_alive:
                process.terminate()
        print("sleep:10s")
        time.sleep(10)
