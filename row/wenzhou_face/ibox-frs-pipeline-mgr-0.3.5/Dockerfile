FROM harbor.ctyuncdn.cn/ai-service/ubuntu:18.04-aarch64-py3.8

ADD config.yaml /config.yaml
ADD requirements.txt /app/requirements.txt
WORKDIR /app/
ADD app /app/
ADD app/client /app/client
ADD test /test

RUN apt install libsm6 libxext6 libgl1 -y \
    && pip install -r requirements.txt -i https://pypi.mirrors.ustc.edu.cn/simple/ \
    && rm -rf ~/.cache/pip

CMD ["python", "teleservice.py"]

# https://pypi.mirrors.ustc.edu.cn/simple/
# https://pypi.tuna.tsinghua.edu.cn/simple