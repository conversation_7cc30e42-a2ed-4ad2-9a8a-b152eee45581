FROM ubuntu:18.04

ENV DEBIAN_FRONTEND=noninteractive
ENV LANG C.UTF-8

RUN apt-get update \
    && apt-get install iputils-ping net-tools curl vim telnet wget software-properties-common -y \
    && apt-get install python3.8 python3.8-dev python3-pip -y \
    && apt-get install python3-distutils -y \
    && rm -rf /usr/bin/python \
    && ln -s /usr/bin/python3.8 /usr/bin/python \
    && python -m pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple \
    && cd /usr/lib/python3.8 \
    && ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && apt-get clean \
    && apt-get autoclean \
    && du -sh /var/cache/apt/ \
    && rm -rf /var/cache/apt/archives \
    && rm -rf ~/.cache/pip

