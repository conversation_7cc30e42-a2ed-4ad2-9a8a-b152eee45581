from email import header
import os
import base64
import unittest
import requests
from pymilvus import (
    connections,
    utility,
    FieldSchema, CollectionSchema, DataType,
    Collection,
)


class TestApi(unittest.TestCase):
    def setUp(self) -> None:
        print('TestApi初始化')
        #self.domain = 'http://************:8899'
        self.domain = 'http://localhost:8899'

    def test_001_health(self):
        resp = requests.get(self.domain + '/qc')
        print(resp.json())

    def test_002_version(self):
        resp = requests.get(self.domain + '/version')
        print(resp.json())

    def test_003_create_face_db(self):
        headers = {
            'Content_Type': 'application/json',
            'appkey': '562b89493b1a40e1b97ea05e50dd8170'
        }
        data = {
            'Name': 'test' 
        }
        resp = requests.post(self.domain + '/faces/create_face_db', json=data, headers=headers)
        print('finish')
        print(resp.json())

    def test_004_list_face_dbs(self):
        headers = {
            'Content_Type': 'application/json',
            'appkey': '562b89493b1a40e1b97ea05e50dd8170'
        }
        data = {
            'Page': 1,
            'PageSize': 10
        }
        resp = requests.post(self.domain + '/faces/list_face_dbs', json=data, headers=headers)
        print('finish')
        print(resp.json())

    def test_005_delete_face_db(self):
        headers = {
            'Content_Type': 'application/json',
            'appkey': '562b89493b1a40e1b97ea05e50dd8170'
        }
        data = {
            'Name': 'test' 
        }
        resp = requests.post(self.domain + '/faces/delete_face_db', json=data, headers=headers)
        print('finish')
        print(resp.json())

    def test_006_add_face_entity(self):
        headers = {
            'Content_Type': 'application/json',
            'appkey': '562b89493b1a40e1b97ea05e50dd8170'
        }
        data = {
            'DbName': 'test',
            'EntityId': 'xiaoming',
            'Labels': 'xiaoming'
        }
        resp = requests.post(self.domain + '/faces/add_face_entity', json=data, headers=headers)
        print('finish')
        print(resp.json())

    def test_007_get_face_entity(self):
        headers = {
            'Content_Type': 'application/json',
            'appkey': '562b89493b1a40e1b97ea05e50dd8170'
        }
        data = {
            'DbName': 'test',
            'EntityId': 'xiaoming'
        }
        resp = requests.post(self.domain + '/faces/get_face_entity', json=data, headers=headers)
        print('finish')
        print(resp.json())

    def test_008_list_face_entities(self):
        headers = {
            'Content_Type': 'application/json',
            'appkey': '562b89493b1a40e1b97ea05e50dd8170'
        }
        data = {
            'DbName': 'test',
            'Page': 1,
            'PageSize': 10
        }
        resp = requests.post(self.domain + '/faces/list_face_entities', json=data, headers=headers)
        print('finish')
        print(resp.json())

    def test_009_update_face_entity(self):
        headers = {
            'Content_Type': 'application/json',
            'appkey': '562b89493b1a40e1b97ea05e50dd8170'
        }
        data = {
            'DbName': 'test',
            'EntityId': 'test_id',
            'NewEntityId': 'new_id',
            'Labels': 'xiaohong'
        }
        resp = requests.post(self.domain + '/faces/update_face_entity', json=data, headers=headers)
        print('finish')
        print(resp.json())

    def test_010_delete_face_entity(self):
        headers = {
            'Content_Type': 'application/json',
            'appkey': '562b89493b1a40e1b97ea05e50dd8170'
        }
        data = {
            'DbName': 'test',
            'EntityId': 'xiaoming'
        }
        resp = requests.post(self.domain + '/faces/delete_face_entity', json=data, headers=headers)
        print('finish')
        print(resp.json())

    def test_011_add_face(self):
        image_path = "./assets/one_person.jpg"
        with open(image_path, 'rb') as file:
            image = file.read()
        headers = {
            'Content_Type': 'application/json',
            'appkey': '562b89493b1a40e1b97ea05e50dd8170'
        }
        data = {
            "ImageData": base64.b64encode(image).decode(),
            "TimeStamp": 1600000000,
            "ExtraData": "/tmp/xiaoming.jpg",
            "DbName": "test",
            "EntityId": "xiaoming"
        }
        resp = requests.post(self.domain + '/faces/add_face', json=data, headers=headers)
        print(resp.json())

    def test_012_search_face(self):
        image_path = "./test/assets/one_person.jpg"
        with open(image_path, 'rb') as file:
            image = file.read()
        headers = {
            'Content_Type': 'application/json',
            'appkey': '562b89493b1a40e1b97ea05e50dd8170'
        }
        data = {
            "ImageData": base64.b64encode(image).decode(),
            "StartTime": 1600000000,
            "EndTime": 1600000001,
            "DbName": ["test"],
            "TopK": 2
        }
        resp = requests.post(self.domain + '/faces/search_face', json=data, headers=headers)
        print(resp.json())

    def test_013_delete_face(self):
        headers = {
            'Content_Type': 'application/json',
            'appkey': '562b89493b1a40e1b97ea05e50dd8170'
        }
        data = {
            "DbName": "test",
            "FaceId": "439765614396815786"
        }
        resp = requests.post(self.domain + '/faces/delete_face', json=data, headers=headers)
        print(resp.json())

    def test_014_list_collections(self):
        connections.connect("default", host='************', port=str(19530))
        coll = utility.list_collections()
        print(coll)

    def test_015_delete_collection(self):
        connections.connect("default", host='************', port=str(19530))
        utility.drop_collection("f202303131621228416571029108")

    def test_016_list_vectors(self):
        name = "test"
        connections.connect("default", host='************', port=str(19530))
        expr = "timestamp > 0"
        collection = Collection(name)
        collection.load()
        # result = collection.delete(expr)
        # print(result)
        # print(result.delete_count)
        # print(collection.schema)
        res = collection.query(
            expr=expr,
            output_fields=["id", "timestamp"],
            # consistency_level="Strong"
        )
        print(res)
    


if __name__ == '__main__':
    unittest.main()
