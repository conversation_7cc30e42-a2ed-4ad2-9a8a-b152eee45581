# -*- coding: utf-8 -*-
import base64
import json
import os
import requests
import yaml
from icecream import ic
ic.configureOutput(includeContext=True)

with open("../config.yaml", "r", encoding="utf-8") as f:
    configs = yaml.load(f, Loader=yaml.FullLoader)

# 最小功能验证测试脚本

def make_request(payload):

    host = "localhost"
    port = os.environ.get('HTTP_PORT', configs["ports"]["BACKEND_PORT"])

    url = 'http://{}:{}/predict'.format(host, port)
    # print(url)

    request = requests.Request(
        'POST',
        url,
        headers={"Content-Type": "application/json"},
        data=json.dumps(payload),
    )
    prepped_request = request.prepare()

    with requests.Session() as sess:
        resp = sess.send(prepped_request)
        # print(resp.headers)
    return resp.content


def b64_content(img_path):
    with open(img_path, 'rb') as f:
        content = f.read()
    b64_content = base64.urlsafe_b64encode(content)
    return b64_content.decode()


if __name__ == '__main__':

    files = ['face1.jpg', "face2.jpg",'face3.jpg', "face4.jpg"]

    for idx, file in enumerate(files[:]):
        ic(idx, file)
        
        img_path = file
        data = b64_content(img_path)

        payload = {
            'Action': 'DetectFace',
            'ImageData': data
        }

        res = make_request(payload)
        res = json.loads(res)
        ic(res)
