from email import header
import os
import base64
import unittest
import requests
import glob
from icecream import ic

def getfilelist(path, filelist):
    file = os.listdir(path)
    for im_name in file:
        if os.path.isdir(os.path.join(path, im_name)):
            getfilelist(os.path.join(path, im_name), filelist)
        else:
            if im_name.endswith(".jpg"):
                name = os.path.join(path, im_name)
                filelist.append(name)

class TestApi(unittest.TestCase):
    def setUp(self) -> None:
        print('TestApi初始化')
        #self.domain = 'http://************:8899'
        self.domain = 'http://localhost:8899'

    def test_001_health(self):
        resp = requests.get(self.domain + '/qc')
        print(resp.json())

    def test_002_version(self):
        resp = requests.get(self.domain + '/version')
        print(resp.json())

    # def test_003_create_face_db(self):
    #     headers = {
    #         'Content_Type': 'application/json',
    #         'appkey': '562b89493b1a40e1b97ea05e50dd8170'
    #     }
    #     data = {
    #         'Name': 'test_name' 
    #     }
    #     resp = requests.post(self.domain + '/faces/create_face_db', json=data, headers=headers)
    #     print('finish')
    #     print(resp.json())

    # def test_004_list_face_dbs(self):
    #     headers = {
    #         'Content_Type': 'application/json',
    #         'appkey': '562b89493b1a40e1b97ea05e50dd8170'
    #     }
    #     data = {
    #         'Page': 1,
    #         'PageSize': 10
    #     }
    #     resp = requests.post(self.domain + '/faces/list_face_dbs', json=data, headers=headers)
    #     print('finish')
    #     print(resp.json())

    # def test_005_delete_face_db(self):
    #     headers = {
    #         'Content_Type': 'application/json',
    #         'appkey': '562b89493b1a40e1b97ea05e50dd8170'
    #     }
    #     data = {
    #         'Name': 'test_name' 
    #     }
    #     resp = requests.post(self.domain + '/faces/delete_face_db', json=data, headers=headers)
    #     print('finish')
    #     print(resp.json())

    # def test_006_add_face_entity(self):
    #     headers = {
    #         'Content_Type': 'application/json',
    #         'appkey': '562b89493b1a40e1b97ea05e50dd8170'
    #     }
    #     data = {
    #         'DbName': 'test_name',
    #         'EntityId': 'XMY_6',
    #         'Labels': 'XMY_6'
    #     }
    #     resp = requests.post(self.domain + '/faces/add_face_entity', json=data, headers=headers)
    #     print('finish')
    #     print(resp.json())

    # def test_007_get_face_entity(self):
    #     headers = {
    #         'Content_Type': 'application/json',
    #         'appkey': '562b89493b1a40e1b97ea05e50dd8170'
    #     }
    #     data = {
    #         'DbName': 'test_name',
    #         'EntityId': 'XMY'
    #     }
    #     resp = requests.post(self.domain + '/faces/get_face_entity', json=data, headers=headers)
    #     print('finish')
    #     print(resp.json())

    # def test_008_list_face_entities(self):
    #     headers = {
    #         'Content_Type': 'application/json',
    #         'appkey': '562b89493b1a40e1b97ea05e50dd8170'
    #     }
    #     data = {
    #         'DbName': 'test_name',
    #         'Page': 1,
    #         'PageSize': 10
    #     }
    #     resp = requests.post(self.domain + '/faces/list_face_entities', json=data, headers=headers)
    #     print('finish')
    #     print(resp.json())

    # def test_009_update_face_entity(self):
    #     headers = {
    #         'Content_Type': 'application/json',
    #         'appkey': '562b89493b1a40e1b97ea05e50dd8170'
    #     }
    #     data = {
    #         'DbName': 'test_name',
    #         'EntityId': 'new_XMY',
    #         'NewEntityId': 'XMY',
    #         'Labels': 'xiaohong'
    #     }
    #     resp = requests.post(self.domain + '/faces/update_face_entity', json=data, headers=headers)
    #     print('finish')
    #     print(resp.json())

    # def test_010_delete_face_entity(self):
    #     headers = {
    #         'Content_Type': 'application/json',
    #         'appkey': '562b89493b1a40e1b97ea05e50dd8170'
    #     }
    #     data = {
    #         'DbName': 'test_name',
    #         'EntityId': 'XMY'
    #     }
    #     resp = requests.post(self.domain + '/faces/delete_face_entity', json=data, headers=headers)
    #     print('finish')
    #     print(resp.json())

    # def test_011_add_face(self):
        headers = {
            'Content_Type': 'application/json',
            'appkey': '562b89493b1a40e1b97ea05e50dd8170'
        }
        file_pref = "./assets/lfw"
        paris_txt_path = "pairs.txt"
        person_list = os.listdir(file_pref)
        filenames = []
        getfilelist(file_pref, filenames)  # 获取所有图片路径
        filenames.sort()
        print(f"总人数: {len(person_list)}，图片数量: {len(filenames)}")
        with open(paris_txt_path, "r") as f:
            contents = f.readlines()
            ic(len(contents))
        for pair in contents:
            pair = pair.split("\t")
            pair_count = len(pair)
            person_name = pair[0].strip()
            img_a_name = person_name + "_" + pair[1].strip().zfill(4) + ".jpg"
            img_a_path = os.path.join(file_pref, person_name, img_a_name)

            data = {
                'DbName': 'test_name',
                'EntityId': person_name + "_" + "repeat",
                'Labels': person_name + "_" + "repeat"
            }
            resp = requests.post(self.domain + '/faces/add_face_entity', json=data, headers=headers)
            print('finish')
            print(resp.json())
            # image_path = "./assets/test_search_1.jpg"
            with open(img_a_path, 'rb') as file:
                image = file.read()
            # headers = {
            #     'Content_Type': 'application/json',
            #     'appkey': '562b89493b1a40e1b97ea05e50dd8170'
            # }
            data = {
                "ImageData": base64.b64encode(image).decode(),
                "TimeStamp": 1600000000,
                "ExtraData": "/tmp/" + img_a_name,
                "DbName": "test_name",
                "EntityId": person_name + "_" +"repeat"
            }
            resp = requests.post(self.domain + '/faces/add_face', json=data, headers=headers)
            print(resp.json())

    # def test_012_search_face(self):
    #     image_path = "./assets/test_search_1.jpg"
    #     with open(image_path, 'rb') as file:
    #         image = file.read()
    #     headers = {
    #         'Content_Type': 'application/json',
    #         'appkey': '562b89493b1a40e1b97ea05e50dd8170'
    #     }
    #     data = {
    #         "ImageData": base64.b64encode(image).decode(),
    #         "StartTime": 1600000000,
    #         "EndTime": 1600000001,
    #         "DbName": ["test_name"],
    #         "TopK": 2,
    #         "Boxes": [[0.32936388254165649,0.0084350509569048882,0.31559017300605774,0.78901100158691406]],
    #         "Landmark": [[[0.41728514432907104,0.31675347685813904],[0.56679689884185791,0.32352429628372192],[0.49468994140625,0.46179470419883728],
    #                     [0.43281251192092896,0.61536461114883423],[0.55000001192092896,0.62144094705581665]]]
    #     }
    #     resp = requests.post(self.domain + '/faces/search_face', json=data, headers=headers)
    #     print(resp.json())

    # def test_013_delete_face(self):
    #     headers = {
    #         'Content_Type': 'application/json',
    #         'appkey': '562b89493b1a40e1b97ea05e50dd8170'
    #     }
    #     data = {
    #         "DbName": "test_name",
    #         "FaceId": "92",
    #     }
    #     resp = requests.post(self.domain + '/faces/delete_face', json=data, headers=headers)
    #     print(resp.json())

    


if __name__ == '__main__':
    unittest.main()
