from email import header
import os
import base64
import unittest
import requests

class TestApi(unittest.TestCase):
    def setUp(self) -> None:
        print('TestApi初始化')
        #self.domain = 'http://************:8899'
        self.domain = 'http://localhost:8899'

    def test_001_health(self):
        resp = requests.get(self.domain + '/qc')
        print(resp.json())

    def test_002_version(self):
        resp = requests.get(self.domain + '/version')
        print(resp.json())

    # def test_003_create_face_db(self):
    #     headers = {
    #         'Content_Type': 'application/json',
    #         'appkey': '562b89493b1a40e1b97ea05e50dd8170'
    #     }
    #     data = {
    #         'Name': 'test_name' 
    #     }
    #     resp = requests.post(self.domain + '/faces/create_face_db', json=data, headers=headers)
    #     print('finish')
    #     print(resp.json())

    # def test_004_list_face_dbs(self):
    #     headers = {
    #         'Content_Type': 'application/json',
    #         'appkey': '562b89493b1a40e1b97ea05e50dd8170'
    #     }
    #     data = {
    #         'Page': 1,
    #         'PageSize': 10
    #     }
    #     resp = requests.post(self.domain + '/faces/list_face_dbs', json=data, headers=headers)
    #     print('finish')
    #     print(resp.json())

    # def test_005_delete_face_db(self):
    #     headers = {
    #         'Content_Type': 'application/json',
    #         'appkey': '562b89493b1a40e1b97ea05e50dd8170'
    #     }
    #     data = {
    #         'Name': 'test_name' 
    #     }
    #     resp = requests.post(self.domain + '/faces/delete_face_db', json=data, headers=headers)
    #     print('finish')
    #     print(resp.json())

    # def test_006_add_face_entity(self):
    #     headers = {
    #         'Content_Type': 'application/json',
    #         'appkey': '562b89493b1a40e1b97ea05e50dd8170'
    #     }
    #     data = {
    #         'DbName': 'test_name',
    #         'EntityId': 'test_penson_one',
    #         'Labels': 'test_penson_one'
    #     }
    #     resp = requests.post(self.domain + '/faces/add_face_entity', json=data, headers=headers)
    #     print('finish')
    #     print(resp.json())

    # def test_007_get_face_entity(self):
    #     headers = {
    #         'Content_Type': 'application/json',
    #         'appkey': '562b89493b1a40e1b97ea05e50dd8170'
    #     }
    #     data = {
    #         'DbName': 'test_name',
    #         'EntityId': 'XMY'
    #     }
    #     resp = requests.post(self.domain + '/faces/get_face_entity', json=data, headers=headers)
    #     print('finish')
    #     print(resp.json())

    # def test_008_list_face_entities(self):
    #     headers = {
    #         'Content_Type': 'application/json',
    #         'appkey': '562b89493b1a40e1b97ea05e50dd8170'
    #     }
    #     data = {
    #         'DbName': 'test_name',
    #         'Page': 1,
    #         'PageSize': 10
    #     }
    #     resp = requests.post(self.domain + '/faces/list_face_entities', json=data, headers=headers)
    #     print('finish')
    #     print(resp.json())

    # def test_009_update_face_entity(self):
    #     headers = {
    #         'Content_Type': 'application/json',
    #         'appkey': '562b89493b1a40e1b97ea05e50dd8170'
    #     }
    #     data = {
    #         'DbName': 'test_name',
    #         'EntityId': 'new_XMY',
    #         'NewEntityId': 'XMY',
    #         'Labels': 'xiaohong'
    #     }
    #     resp = requests.post(self.domain + '/faces/update_face_entity', json=data, headers=headers)
    #     print('finish')
    #     print(resp.json())

    # def test_010_delete_face_entity(self):
    #     headers = {
    #         'Content_Type': 'application/json',
    #         'appkey': '562b89493b1a40e1b97ea05e50dd8170'
    #     }
    #     data = {
    #         'DbName': 'test_name',
    #         'EntityId': 'test_penson_one'
    #     }
    #     resp = requests.post(self.domain + '/faces/delete_face_entity', json=data, headers=headers)
    #     print('finish')
    #     print(resp.json())

    # def test_011_add_face(self):
    #     image_path = "./assets/test_penson_one.png"
    #     with open(image_path, 'rb') as file:
    #         image = file.read()
    #     headers = {
    #         'Content_Type': 'application/json',
    #         'appkey': '562b89493b1a40e1b97ea05e50dd8170'
    #     }
    #     data = {
    #         "ImageData": base64.b64encode(image).decode(),
    #         "TimeStamp": 1600000000,
    #         "ExtraData": "/tmp/test_penson_one.png",
    #         "DbName": "test_name",
    #         "EntityId": "test_penson_one"
    #     }
    #     resp = requests.post(self.domain + '/faces/add_face', json=data, headers=headers)
    #     print(resp.json())

    # def test_012_search_face(self):
    #     image_path = "./assets/test_search.jpg"
    #     with open(image_path, 'rb') as file:
    #         image = file.read()
    #     headers = {
    #         'Content_Type': 'application/json',
    #         'appkey': '562b89493b1a40e1b97ea05e50dd8170'
    #     }
    #     data = {
    #         "ImageData": base64.b64encode(image).decode(),
    #         "StartTime": 1600000000,
    #         "EndTime": 1600000001,
    #         "DbName": ["test_name"],
    #         "TopK": 1,
    #         "Boxes": [[0.43886706233024597,9.9999999747524271e-07,0.028130818158388138,0.040012292563915253],
    #                     [0.63297116756439209,9.9999999747524271e-07,0.028915023431181908,0.05375959724187851],
    #                     [0.20821499824523926,0.08871915191411972,0.030484508723020554,0.060934256762266159]],
    #         "Landmark": [[[0.44736328721046448,0.0],[0.46048584580421448,0.0],[0.45420226454734802,0.01136067695915699],
    #                     [0.44831237196922302,0.02195366844534874],[0.45946654677391052,0.02239854633808136]],
    #                     [[0.63931882381439209,0.0068901907652616501],[0.65266722440719604,0.010004340671002865],
    #                     [0.64331662654876709,0.02161865308880806],[0.63831788301467896,0.033148869872093201],
    #                     [0.64952927827835083,0.035514321178197861]],[[0.21489258110523224,0.11297200620174408],
    #                     [0.22843627631664276,0.11293674260377884],[0.22116699814796448,0.1272786408662796],
    #                     [0.21680907905101776,0.13630641996860504],[0.22751159965991974,0.13611111044883728]]]
    #     }
    #     resp = requests.post(self.domain + '/faces/search_face', json=data, headers=headers)
    #     print(resp.json())

    def test_013_delete_face(self):
        headers = {
            'Content_Type': 'application/json',
            'appkey': '562b89493b1a40e1b97ea05e50dd8170'
        }
        data = {
            "DbName": "test_name",
            "FaceId": "12067",
        }
        resp = requests.post(self.domain + '/faces/delete_face', json=data, headers=headers)
        print(resp.json())

    


if __name__ == '__main__':
    unittest.main()
