



# ibox-frs-pipeline-mgr 人脸识别(faiss版本)一算法服务识别

### 0. 文档说明

- **版本信息**：

  - **当前版本**：0.3.5-aarch64
  - **已支持基础镜像**：harbor.ctyuncdn.cn/ai-service/ubuntu:18.04-aarch64-py3.8
  - **依赖镜像-triton：**harbor.ctyuncdn.cn/ai-service/ibox/ibox-triton-face-recog：0.3.1-21.12-XavierNX;
  - **依赖镜像-mysql:**harbor.ctyuncdn.cn/ai-service/ibox/mysql:8.0.32
  - **已支持GPU类型**：XavierNX
  - **镜像版本历史**：
    - [harbor.ctyuncdn.cn/ai-service/ibox/ibox-frs-pipeline-mgr:0.3.0-aarch64]
    - [harbor.ctyuncdn.cn/ai-service/ibox/ibox-frs-pipeline-mgr:0.3.1-aarch64]
    - [harbor.ctyuncdn.cn/ai-service/ibox/ibox-frs-pipeline-mgr:0.3.2-aarch64]
    - [harbor.ctyuncdn.cn/ai-service/ibox/ibox-frs-pipeline-mgr:0.3.3-aarch64]
    - [harbor.ctyuncdn.cn/ai-service/ibox/ibox-frs-pipeline-mgr:0.3.5-aarch64]
  - **更新时间**：2023.05.31

- **版本记录**：
  
  - |  更新时间  | 版本号 | 修订内容 |
    | :--------: | :----: | :------: |
    | 2023.04.21 | 0.3.0-aarch64 | 第一版本 |
    | 2023.04.26 | 0.3.1-aarch64 | 更改faiss数据落盘挂载路径 |
    | 2023.04.27 | 0.3.2-aarch64 | 人脸识别修改增加face数据bug |
    | 2023.05.04 | 0.3.3-aarch64 | 人脸识别支持多个DbName同时收索 |
    | 2023.05.31 | 0.3.5-aarch64 | 支持多并发（多线程访问） |
    
    
    
    

#### 1. 启动

通过以下命令启动，注意修改MYSQL的密码和挂载路径，挂载路径主要落盘faiss人脸特征数据。

```
NAME="ibox-frs-pipeline-mgr"
IMAGE="harbor.ctyuncdn.cn/ai-service/ibox/${NAME}"
VERSION=0.3.3-aarch64
  
 
sudo docker run -d --rm \
     -v xxx/xxx/xxx:/app/client/data/ \
    # --net=host \
    --name=${NAME}-${VERSION} \
    -e MYSQL_PASSWD="xxxx" \
    -e MYSQL_USER="xxxx"\
    -e MYSQL_DB_NAME="xxxx"\
    -e MYSQL_PORT="xxxx"\
    -e MYSQL_HOST="xxxx"\
    -e TRITON_URL='localhost:7010' \
    ${IMAGE}:${VERSION}
    
```

