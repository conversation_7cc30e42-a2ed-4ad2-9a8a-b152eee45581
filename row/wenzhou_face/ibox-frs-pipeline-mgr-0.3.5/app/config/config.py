# -*- coding:utf-8 -*-
"""
配置文件, 分动态配置和静态配置
"""
import os
import logging
from yaml import load, Loader
from distutils.util import strtobool
from easydict import EasyDict as edict

from app.const.service import LOCK_PATH

logger = logging.getLogger(__name__)


try:
    with open(os.path.join(os.path.split(os.path.realpath(__file__))[0], 'config.yaml'), 'rb') as file:
        cfg = load(file, Loader)
except ImportError:
    logger.fatal('Init, parse config file error')

if os.environ.get('SERVICE_NAME', None):
    cfg['SERVICE']['NAME'] = os.environ.get('SERVICE_NAME')

if os.environ.get('HTTP_FRAMEWORK', None):
    cfg['SERVICE']['FRAMEWORK'] = os.environ.get('HTTP_FRAMEWORK')

if os.environ.get('HTTP_HOST', None):
    cfg['SERVICE']['HOST'] = os.environ.get('HTTP_HOST')

if os.environ.get('HTTP_PORT', None):
    cfg['SERVICE']['PORT'] = int(os.environ.get('HTTP_PORT'))

if os.environ.get('SERVICE_VERSION', None):
    cfg['SERVICE']['PORT'] = os.environ.get('SERVICE_VERSION')

if os.environ.get('PROCESS_NUM', None):
    cfg['SERVICE']['PROCESS_NUM'] = int(os.environ.get('PROCESS_NUM'))

if os.environ.get('MULTI_THREAD', None):
    cfg['SERVICE']['MULTI_THREAD'] = strtobool(os.environ.get('MULTI_THREAD'))

if os.environ.get('LOG_LEVEL', None):
    cfg['LOG']['LEVEL'] = os.environ.get('LOG_LEVEL')

if os.environ.get('SLOW_RESPONSE', None):
    cfg['LOG']['SLOW_RESPONSE'] = int(os.environ.get('SLOW_RESPONSE'))

if os.environ.get('MYSQL_HOST', None):
    cfg['MYSQL']['HOST'] = os.environ.get('MYSQL_HOST')

if os.environ.get('MYSQL_PORT', None):
    cfg['MYSQL']['PORT'] = int(os.environ.get('MYSQL_PORT'))

if os.environ.get('MYSQL_DB_NAME', None):
    cfg['MYSQL']['DB_NAME'] = os.environ.get('MYSQL_DB_NAME')

if os.environ.get('MYSQL_USER', None):
    cfg['MYSQL']['USER'] = os.environ.get('MYSQL_USER')

if os.environ.get('MILVUS_HOST', None):
    cfg['MILVUS']['HOST'] = os.environ.get('MILVUS_HOST')

if os.environ.get('MILVUS_PORT', None):
    cfg['MILVUS']['PORT'] = int(os.environ.get('MILVUS_PORT'))

if os.environ.get('MILVUS_VECTOR_DIM', None):
    cfg['MILVUS']['VECTOR_DIM'] = int(os.environ.get('MILVUS_VECTOR_DIM'))

if os.environ.get('MILVUS_METRIC_TYPE', None):
    cfg['MILVUS']['METRIC_TYPE'] = os.environ.get('MILVUS_METRIC_TYPE')

if os.environ.get('TRITON_IMAGE_URL', None):
    cfg['TRITON']['IMAGE_URL'] = os.environ.get('TRITON_IMAGE_URL')

if os.environ.get('TRITON_SERVER', None):
    cfg['TRITON']['SERVER'] = os.environ.get('TRITON_SERVER')

if not os.path.exists(LOCK_PATH) or not os.path.isdir(LOCK_PATH):
    os.makedirs(LOCK_PATH, exist_ok=True)
    logger.info(f'Init, service config: {cfg}')

if os.environ.get('MYSQL_PASSWD', None):
    cfg['MYSQL']['PASSWD'] = os.environ.get('MYSQL_PASSWD')

logger.info(f'Init, load config success')
cfg = edict(cfg)
