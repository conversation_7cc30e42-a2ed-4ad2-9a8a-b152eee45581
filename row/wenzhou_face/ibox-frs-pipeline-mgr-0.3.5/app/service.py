# -*- coding:utf-8 -*-
"""
入口函数
"""
import os
import sys
import shutil
import logging
import uvicorn

from dotenv import load_dotenv
from telelog import get_logger

if os.environ.get('RUNTIME_ENV', '') == 'local':
    load_dotenv()

# setup logging format and logger
logger = logging.getLogger()
logger = get_logger(logger=logger, level='INFO')

from app.config.config import cfg
from app.const.service import LOCK_PATH

logger.setLevel(cfg.LOG.LOG_LEVEL.upper())


def grace_exit():
    if os.path.exists(LOCK_PATH) and os.path.isdir(LOCK_PATH):
        shutil.rmtree(LOCK_PATH)
        logger.info(f'Exit, clean lock path: {LOCK_PATH}')
        sys.exit(0)


def start_service():
    logger.info(f'Init, main process id: {os.getpid()}')
    if cfg.SERVICE.FRAMEWORK.lower() == 'fastapi':
        logger.info(f'Init, start fastapi at {cfg.SERVICE.PORT}')
        # uvicorn.run("app.servers.fastapi_server:app", host=cfg.SERVICE.HOST, port=cfg.SERVICE.PORT, workers=cfg.SERVICE.PROCESS_NUM)
        # uvicorn.run("app.servers.fastapi_server:app", host=cfg.SERVICE.HOST, port=cfg.SERVICE.PORT, workers=1)
        uvicorn.run("app.servers.fastapi_server:app", host=cfg.SERVICE.HOST, port=cfg.SERVICE.PORT, workers=cfg.SERVICE.PROCESS_NUM,loop="asyncio")

    elif cfg.SERVICE.FRAMEWORK.lower() == 'tornado':
        pass

    else:
        logger.fatal(f'Init, unsupport framework {cfg.SERVICE.FRAMEWORK}')

    grace_exit()
