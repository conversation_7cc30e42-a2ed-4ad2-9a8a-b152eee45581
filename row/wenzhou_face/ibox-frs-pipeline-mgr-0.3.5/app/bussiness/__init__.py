# -*- encoding: utf-8 -*-
import logging
from sqlalchemy.exc import OperationalError
from app.client.mysql_base import MySQLBase
#from app.client.milvus_base import MilvusBase
from app.client.faiss_base import FaissBase
from app.models.base import BaseData
from app.bussiness.faces import FacesModule
from app.bussiness.user import UserModule

logger = logging.getLogger(__name__)

def init(config):
    """
    功能模块初始化
    :param kwargs:
    :return:
    """
    # 初始化数据库表
    mysql_db = MySQLBase(mysql_host=config.MYSQL.HOST,
                         mysql_port=config.MYSQL.PORT,
                         mysql_database=config.MYSQL.DB_NAME,
                         mysql_user=config.MYSQL.USER,
                         mysql_password=config.MYSQL.PASSWD)
    _init_tables_(mysql_db)
    faiss_db = FaissBase(config)
    #milvus_db = MilvusBase(config)
    # 初始化功能模块
    #UserModule.get_instance(mysql_db=mysql_db, milvus_db=milvus_db, config=config)
    #FacesModule.get_instance(mysql_db=mysql_db, milvus_db=milvus_db, config=config)
    UserModule.get_instance(mysql_db=mysql_db, faiss_db=faiss_db, config=config)
    FacesModule.get_instance(mysql_db=mysql_db, faiss_db=faiss_db, config=config)


def _init_tables_(db):
    """
    数据库初始化
    :param db:
    :return:
    """
    try:
        BaseData.metadata.create_all(db.engine)
    except OperationalError as ex:
        logger.error(ex)
