# !/bin/env python
# -*- encoding: utf-8 -*-

import logging
import threading
from app.models.base import UserCollection
from datetime import datetime
from sqlalchemy.exc import OperationalError
from app.utils.http_utils import HTTPStatus
from app.utils.exception_utils import ApiServerException
from sqlalchemy import asc
import random


logger = logging.getLogger(__name__)


def tid_maker():
    return '{0:%Y%m%d%H%M%S%f}'.format(datetime.now()) + ''.join(
        [str(random.randint(1, 10)) for i in range(5)])


class UserModule(object):
    _instance_lock = threading.Lock()
    def __init__(self, db=None, faiss_db=None, config=None):
        self.db = db
        self.faiss_db = faiss_db
        self.config = config

    @classmethod
    def get_instance(cls, mysql_db=None, faiss_db=None, config=None):
        """
        获取Master单例
        Args:
            config: 配置文件内容

        Return: Master单例

        """
        if not hasattr(UserModule, "_instance"):
            with UserModule._instance_lock:
                if not hasattr(UserModule, "_instance"):
                    UserModule._instance = UserModule(mysql_db, faiss_db, config)
        return UserModule._instance

    def add_collection(self, app_key):
        # 为此app_kek创建一个底库
        collection_name = "f" + tid_maker()
        # 创建底库
        self.faiss_db.create_collection(collection_name)
        sess = self.db.get_session()
        try:
            data = UserCollection(app_key=app_key, collection_name=collection_name, create_time=datetime.now(),
                                  update_time=datetime.now())
            sess.add(data)
            sess.commit()
        except OperationalError as ex:
            sess.rollback()
            logger.error(str(ex))
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        except Exception as ex:
            sess.rollback()
            logger.error(str(ex))
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        finally:
            self.db.close_session(sess)
        return collection_name

    def add_user_collection(self, app_key, collection_name, group):
        sess = self.db.get_session()
        try:
            user_collection_data = UserCollection(app_key=app_key, collection_name=collection_name, group=group,
                                                  create_time=datetime.now(), update_time=datetime.now())
            sess.add(user_collection_data)
            sess.commit()
        except OperationalError as ex:
            sess.rollback()
            logger.error(str(ex))
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        except Exception as ex:
            sess.rollback()
            logger.error(str(ex))
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        finally:
            self.db.close_session(sess)

    def get_group_name(self, app_key, group):
        try:
            sess = self.db.get_session()
            name = sess.query(UserCollection).filter(UserCollection.app_key == app_key,
                                                     UserCollection.group == group).first()
        except Exception as ex:
            logger.error(str(ex))
            self.db.close_session(sess)
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        finally:
            self.db.close_session(sess)
        if name:
            return name.to_dict()["collection_name"]
        else:
            return None

    def get_list_dbs(self, app_key):
        try:
            sess = self.db.get_session()
            names = sess.query(UserCollection).filter(UserCollection.app_key == app_key).all()
        except Exception as ex:
            logger.error(str(ex))
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        finally:
            self.db.close_session(sess)
        name_list = [name.to_dict()["group"] if name.to_dict()["group"] else None for name in names if name]
        new_name_list = [name for name in name_list if name]
        if names:
            collection_name = names[0].to_dict()["collection_name"]
        else:
            collection_name = None
        return collection_name, new_name_list

    def list_face_dbs(self, app_key, page, page_size):
        filter_params = []
        filter_params.append(UserCollection.app_key == app_key)
        filter_params.append(UserCollection.group.isnot(None))
        sess = self.db.get_session()
        offset_data = page_size * (page - 1)
        try:
            total = sess.query(UserCollection).filter(*filter_params).count()
            dbs_filter = sess.query(UserCollection).filter(*filter_params).order_by(asc(UserCollection.create_time)).offset(offset_data).limit(page_size)
        except Exception as ex:
            logger.error(str(ex))
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        finally:
            self.db.close_session(sess)
        dbs = []
        for db in dbs_filter:
            dbs.append(db.to_dict()["group"])
        result = {
            "total_count": total,
            "dbs": dbs
        }
        return result
