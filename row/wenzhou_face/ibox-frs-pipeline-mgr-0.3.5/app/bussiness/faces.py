# -*- encoding: utf-8 -*-
import logging
import threading
import time
import numpy as np
from datetime import datetime
from sqlalchemy.exc import OperationalError
from app.utils.http_utils import HTTPStatus
from app.checker.face import process
from app.models.base import Faces, Entity, UserCollection
from app.utils.exception_utils import ApiServerException
from numpy import linalg as LA
from sqlalchemy import func, desc, asc

logger = logging.getLogger(__name__)

def get_scores(x, meam=1.40, std=0.2):
    """
    人脸距离到人脸相似分数的映射
    Args:
        x: 欧式距离的值
        meam: 均值,默认meam=1.40
        std: 方差,默认std=0.2

    Returns: 返回人脸相似分数(0,1),值越大越相似

    """
    x = -(x - meam) / std
    # sigmoid
    scores = 1.0 / (1.0 + np.exp(-x))
    return scores

class FacesModule(object):
    _instance_lock = threading.Lock()
    def __init__(self, mysql_db=None, faiss_db=None, config=None):
        self.mysql_db = mysql_db
        self.faiss_db = faiss_db
        #self.face_process = process
        self.config = config
        self.image_url = config.TRITON.IMAGE_URL

    @classmethod
    def get_instance(cls, mysql_db=None, faiss_db=None, config=None):
        """
        获取Master单例
        Args:
            config: 配置文件内容

        Return: Master单例

        """
        if not hasattr(FacesModule, "_instance"):
            with FacesModule._instance_lock:
                if not hasattr(FacesModule, "_instance"):
                    FacesModule._instance = FacesModule(mysql_db, faiss_db, config)
        return FacesModule._instance

    def create_faiss_collection(self, collection_name):
        return self.faiss_db.create_collection(collection_name)

    def create_faiss_collection_part(self, collection_name, part_name):
        return self.faiss_db.create_collection_part(collection_name, part_name)

    def add_face_entity(self, data, collection_name):
        labels = data.get("Labels", None)
        entity_id = data.get("EntityId", None)
        db_name = data.get("DbName", None)
        # 插入数据库
        try:
            sess = self.mysql_db.get_session()
            entity = sess.query(Entity).filter(Entity.entity_id == entity_id,
                                               Entity.collection_name == collection_name,
                                               Entity.group == db_name).first()
        except Exception as ex:
            logger.error(str(ex))
            self.mysql_db.close_session(sess)
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        if entity is not None:
            raise ApiServerException(HTTPStatus.EXISTENTITY.value,
                                     HTTPStatus.EXISTENTITY.phrase, 
                                     HTTPStatus.EXISTENTITY.description)
        try:
            entity_data = Entity(entity_id=entity_id, collection_name=collection_name,
                                 group=db_name, labels=labels, create_time=datetime.now(),
                                 update_time=datetime.now())
            sess.add(entity_data)
            sess.commit()
            result = entity_data.to_dict()
            return result
        except OperationalError as ex:
            sess.rollback()
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value,
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        except Exception as ex:
            sess.rollback()
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        finally:
            self.mysql_db.close_session(sess)

    def get_face_entity(self, data, collection_name):
        entity_id = data.get("EntityId", None)
        db_name = data.get("DbName", None)
        # 首先查到实体对应的ID
        try:
            sess = self.mysql_db.get_session()
            entity = sess.query(Entity).filter(Entity.entity_id == entity_id,
                                               Entity.collection_name == collection_name,
                                               Entity.group == db_name).first()
        except Exception as ex:
            logger.error(str(ex))
            self.mysql_db.close_session(sess)
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        if entity is None:
            raise ApiServerException(HTTPStatus.NOTEXISTENTITY,
                                     HTTPStatus.NOTEXISTENTITY.phrase, 
                                     HTTPStatus.NOTEXISTENTITY.description)
        entity_data = entity.to_dict()
        entity_uid = entity_data["id"]
        labels = entity_data["labels"]
        # 查询faces表找到对应实体的人脸信息
        try:
            faces = sess.query(Faces).filter(Faces.entity_uid == entity_uid).all()
        except Exception as ex:
            logger.error(str(ex))
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        finally:
            self.mysql_db.close_session(sess)
        faces_ids = [face.to_dict()["id"] for face in faces if face]
        result = {
            "db_name": db_name,
            "entity_id": entity_id,
            "labels": labels,
            "faces": faces_ids
        }
        return result

    def list_face_entities(self, data, collection_name):
        entity_id_prefix = data.get("EntityIdPrefix",None)
        page = data.get("Page",1)
        page_size = data.get("PageSize",10)
        labels = data.get("Labels",None)
        order = data.get("Order","asc")
        filter_params = []
        filter_params.append(Entity.collection_name == collection_name)
        filter_params.append(Entity.group == data["DbName"])
        if entity_id_prefix:
            filter_params.append(Entity.entity_id.startswith(entity_id_prefix))
        if labels:
            # l = "%{}%".format(data["Labels"])
            filter_params.append(Entity.labels.contains(labels))
        sess = self.mysql_db.get_session()
        try:
            total = sess.query(Entity).filter(*filter_params).count()
        except Exception as ex:
            logger.error(str(ex))
            self.mysql_db.close_session(sess)
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        offset_data = page_size * (page - 1)
        if order == "asc":
            entity_filter = sess.query(Entity).filter(*filter_params).order_by(asc(Entity.create_time)).offset(offset_data).limit(page_size)
        else:
            entity_filter = sess.query(Entity).filter(*filter_params).order_by(desc(Entity.create_time)).offset(offset_data).limit(page_size)
        # 获取当前数据库中的结果
        entitys = []
        for li in entity_filter:
            li_data = li.to_dict()
            id = li_data["id"]
            # 查询此id对应的人脸个数
            try:
                count = sess.query(Faces).filter(Faces.entity_uid == id).count()
            except Exception as ex:
                logger.error(str(ex))
                self.mysql_db.close_session(sess)
                raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                         HTTPStatus.DBOPERATEERR.phrase, 
                                         HTTPStatus.DBOPERATEERR.description)
            li_data["face_count"] = count
            entitys.append(li_data)
        self.mysql_db.close_session(sess)
        result = {
            "total_count": total,
            "entities": entitys
        }
        return result

    def update_face_entity(self, data, collection_name):
        new_entity_id = data.get("NewEntityId", None)
        labels = data.get("Labels", None)
        try:
            sess = self.mysql_db.get_session()
            entity = sess.query(Entity).filter(Entity.entity_id == data["EntityId"],
                                               Entity.collection_name == collection_name,
                                               Entity.group == data["DbName"]).first()
            new_entity = sess.query(Entity).filter(Entity.entity_id == new_entity_id,
                                                   Entity.collection_name == collection_name,
                                                   Entity.group == data["DbName"]).first()
        except Exception as ex:
            logger.error(str(ex))
            self.mysql_db.close_session(sess)
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        if entity is None:
            raise ApiServerException(HTTPStatus.NOTEXISTENTITY.value,
                                     HTTPStatus.NOTEXISTENTITY.phrase, 
                                     HTTPStatus.NOTEXISTENTITY.description)
        if new_entity and new_entity_id != data["EntityId"]:
            raise ApiServerException(HTTPStatus.EXISTENTITY.value, 
                                     HTTPStatus.EXISTENTITY.phrase, 
                                     HTTPStatus.EXISTENTITY.description)
        if not labels:
            labels = entity.to_dict()["labels"]
        try:
            sess.query(Entity).filter(Entity.entity_id == data["EntityId"],
                                      Entity.collection_name == collection_name,
                                      Entity.group == data["DbName"]).update({Entity.entity_id: new_entity_id, Entity.labels: labels})
            sess.commit()
        except Exception as ex:
            sess.rollback()
            logger.error(str(ex))
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        finally:
            self.mysql_db.close_session(sess)

    # def update_face_group(self, data, collection_name):
    #     new_group_id = data.get("NewDbName", None)
    #     try:
    #         sess = self.mysql_db.get_session()
    #         faces = sess.query(Faces).filter(Faces.collection_name == collection_name,
    #                                            Faces.group == data["DbName"]).first()
    #         new_faces = sess.query(Faces).filter(Faces.collection_name == collection_name,
    #                                            Faces.group ==new_group_id).first()

    #         entity = sess.query(Entity).filter(Entity.collection_name == collection_name,
    #                                            Entity.group == data["DbName"]).first()
    #         new_entity = sess.query(Entity).filter(Entity.collection_name == collection_name,
    #                                            Entity.group == new_group_id).first()
    #         user_collection = sess.query(UserCollection).filter(UserCollection.collection_name == collection_name,
    #                                                             UserCollection.group == data["DbName"]).first()
    #         new_user_collection = sess.query(UserCollection).filter(UserCollection.collection_name == collection_name,
    #                                                             UserCollection.group == new_group_id).first()
    #     except Exception as ex:
    #         logger.error(str(ex))
    #         self.mysql_db.close_session(sess)
    #         raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
    #                                  HTTPStatus.DBOPERATEERR.phrase, 
    #                                  HTTPStatus.DBOPERATEERR.description)
    #     if entity is None or user_collection is None:
    #         raise ApiServerException(HTTPStatus.NOTEXISTENTITY.value,
    #                                  HTTPStatus.NOTEXISTENTITY.phrase, 
    #                                  HTTPStatus.NOTEXISTENTITY.description)
    #     if new_entity and new_group_id != data["DbName"]:
    #         raise ApiServerException(HTTPStatus.EXISTENTITY.value, 
    #                                  HTTPStatus.EXISTENTITY.phrase, 
    #                                  HTTPStatus.EXISTENTITY.description)
    #     if new_faces or new_user_collection:
    #         raise ApiServerException(HTTPStatus.EXISTENTITY.value, 
    #                                  HTTPStatus.EXISTENTITY.phrase, 
    #                                  HTTPStatus.EXISTENTITY.description)
    #     try:
    #         sess.query(Entity).filter(Entity.collection_name == collection_name,
    #                                   Entity.group == data["DbName"]).update({Entity.group: new_group_id})
    #         sess.query(UserCollection).filter(UserCollection.collection_name == collection_name,
    #                                   UserCollection.group == data["DbName"]).update({UserCollection.group: new_group_id})
    #         if faces:
    #             sess.query(Faces).filter(Faces.collection_name == collection_name,
    #                                     Faces.group == data["DbName"]).update({Faces.group: new_group_id})                                                                            
    #         sess.commit()
    #     except Exception as ex:
    #         sess.rollback()
    #         logger.error(str(ex))
    #         raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
    #                                  HTTPStatus.DBOPERATEERR.phrase, 
    #                                  HTTPStatus.DBOPERATEERR.description)
    #     finally:
    #         self.mysql_db.close_session(sess)
    #     old_dbname = data["DbName"]
    #     self.faiss_db.rename_part_name(collection_name, old_dbname, new_group_id)

    def add_face(self, data, images, collection_name):
        extra_data = data.get("ExtraData", None)
        db_name = data.get("DbName", None)
        entity_id = data.get("EntityId", None)
        time_stamp = data.get("TimeStamp", int(time.time()))
        # 获取特征向量列表
        request_data = {
            "Action": "FaceDetect",
            "ImageData": images
        }
        vector = process(request_data, url=self.image_url)
        # 进行插入milvus，和mysql
        # 进行归一化
        feature_vector = np.array(vector)
        #feature_vector = feature_vector.astype(np.float)
        feature_vector_norm = feature_vector / LA.norm(feature_vector)
        # ids = self.milvus_db.insert(collection_name, db_name, [feature_vector_norm], time_stamp)
        # milvus_id = str(ids[0])
        sess = self.mysql_db.get_session()
        # 获取entity_id
        try:
            entity = sess.query(Entity).filter(Entity.entity_id == entity_id,
                                               Entity.collection_name == collection_name,
                                               Entity.group == db_name).first()
        except Exception as ex:
            logger.error(str(ex))
            self.mysql_db.close_session(sess)
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        if entity is None:
            raise ApiServerException(HTTPStatus.NOTEXISTENTITY,
                                     HTTPStatus.NOTEXISTENTITY.phrase, 
                                     HTTPStatus.NOTEXISTENTITY.description)
        
        entity_data = entity.to_dict()
        entity_uid = entity_data["id"]
        try:
            # face_data = Faces(milvus_id=milvus_id, collection_name=collection_name,
            #                   group=db_name, entity_uid=entity_uid, extra_data=extra_data, create_time=datetime.now(),
            #                   update_time=datetime.now())
            face_data = Faces(collection_name=collection_name,
                              group=db_name, entity_uid=entity_uid, extra_data=extra_data, create_time=datetime.now(),
                              update_time=datetime.now())
            #result = face_data.to_dict()
            sess.add(face_data)
            sess.flush()
            face_id = face_data.id
            sess.commit()
            self.faiss_db.insert(collection_name, db_name, feature_vector_norm, face_id)
            result = face_data.to_dict()
            result.pop('collection_name', None)
            result.pop('entity_uid', None)
            return result
        except OperationalError as ex:
            logger.error(str(ex))
            sess.rollback()
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        except Exception as ex:
            logger.error(str(ex))
            sess.rollback()
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        finally:
            self.mysql_db.close_session(sess)

    def search_face(self, collection_name, group, image, boxes, landmarks, topk, start_time, end_time):
        # 获取特征向量
        request_data = {
            "Action": "FaceRecog",
            "ImageData": image,
        }
        results = []
        url = self.image_url
        vectors, iboxes= process(request_data, url, boxes, landmarks)
        for j in range(len(vectors)):
            vector = vectors[j]
            # 进行归一化
            feature_vector = np.array(vector)
            feature_vector_norm = feature_vector / LA.norm(feature_vector)
            distances, idxs = self.faiss_db.search_vectors(collection_name, group, feature_vector_norm, topk, start_time, end_time)
            if distances is None:
                raise ApiServerException(HTTPStatus.MILVUSERR.value, 
                                        HTTPStatus.MILVUSERR.phrase, 
                                        HTTPStatus.MILVUSERR.description)
            # vids = [str(x.id) for x in vectors[0]]
            # distances = [(1 + (1 - (x.distance/2)))/2 for x in vectors[0]]
            vids = [str(x) for x in idxs[0]]
            #distances = get_scores(distances[0])
            distances = [(1 + (1 - (x/2)))/2 for x in distances[0]]
            result = []
            for i in range(len(vids)):
                # 查询mysql ,获取faiss_id 对应的信息
                sess = self.mysql_db.get_session()
                try:
                    face_data = sess.query(Faces).filter(Faces.collection_name == collection_name,
                                                        Faces.id == vids[i]).all()
                    if face_data:
                        face = face_data[0].to_dict()
                        face["similarity"] = distances[i]
                        id = face["entity_uid"]
                        entity_data = sess.query(Entity).filter(Entity.id == id).first()
                        face["entity_id"] = entity_data.to_dict()["entity_id"]
                        face["box"] = iboxes[j][:4]
                        #face["face_id"] = str(face["id"])
                        face.pop('collection_name', None)
                        face.pop('entity_uid', None)
                        result.append(face)
                except OperationalError as ex:
                    logger.error(str(ex))
                    raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                            HTTPStatus.DBOPERATEERR.phrase, 
                                            HTTPStatus.DBOPERATEERR.description)
                except Exception as ex:
                    logger.error(str(ex))
                    raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                            HTTPStatus.DBOPERATEERR.phrase, 
                                            HTTPStatus.DBOPERATEERR.description)
                finally:
                    self.mysql_db.close_session(sess)
            results.append(result)
        return results

    def delete_face(self, face_id):
        if face_id is None or len(str(face_id).strip()) == 0:
            raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                     HTTPStatus.TYPEERR.phrase, 
                                     HTTPStatus.TYPEERR.description)
        # 查询mysql数据库
        try:
            sess = self.mysql_db.get_session()
            data = sess.query(Faces).filter(Faces.id == face_id).first()
        except OperationalError as ex:
            logger.error(str(ex))
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        except Exception as ex:
            logger.error(str(ex))
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        finally:
            self.mysql_db.close_session(sess)
        if data is None:
            raise ApiServerException(HTTPStatus.NOTFACEID.value, 
                                     HTTPStatus.NOTFACEID.phrase, 
                                     HTTPStatus.NOTFACEID.description)
        face_data = data.to_dict()
        # 删除mysql里的人脸数据
        try:
            sess = self.mysql_db.get_session()
            sess.query(Faces).filter(Faces.id == face_id).delete()
            sess.commit()
        except Exception as ex:
            sess.rollback()
            logger.error(str(ex))
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        finally:
            self.mysql_db.close_session(sess)
        # 删除faiss里的人脸向量
        self.faiss_db.delete_vector(face_data["collection_name"], face_data["db_name"], face_data["id"])

    def delete_face_entity(self, collection_name, group, entity_id):
        # 查询mysql数据库
        try:
            sess = self.mysql_db.get_session()
            data = sess.query(Entity).filter(Entity.collection_name == collection_name,
                                             Entity.group == group,
                                             Entity.entity_id == entity_id).first()
        except OperationalError as ex:
            logger.error(str(ex))
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        except Exception as ex:
            logger.error(str(ex))
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        finally:
            self.mysql_db.close_session(sess)
        if data is None:
            raise ApiServerException(HTTPStatus.NOTEXISTENTITY.value, 
                                     HTTPStatus.NOTEXISTENTITY.phrase, 
                                     HTTPStatus.NOTEXISTENTITY.description)
        entity_uid = data.to_dict()["id"]
        # 查询此entity_id对应的所有人脸ID
        try:
            sess = self.mysql_db.get_session()
            faces_data = sess.query(Faces).filter(Faces.collection_name == collection_name,
                                                  Faces.group == group, Faces.entity_uid == entity_uid).all()
            face_list = [face_data.to_dict()["id"] for face_data in faces_data if face_data]
        except Exception as ex:
            sess.rollback()
            logger.error(str(ex))
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        finally:
            self.mysql_db.close_session(sess)
        try:
            sess = self.mysql_db.get_session()
            sess.query(Entity).filter(Entity.collection_name == collection_name,
                                      Entity.group == group,
                                      Entity.entity_id == entity_id).delete()
            sess.query(Faces).filter(Faces.entity_uid == entity_uid).delete()
            sess.commit()
        except Exception as ex:
            sess.rollback()
            logger.error(str(ex))
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        finally:
            self.mysql_db.close_session(sess)
        # 最后删除faiss中的人脸数据
        if len(face_list) != 0:
            faiss_ids = [int(i) for i in face_list]
            self.faiss_db.delete_vector(collection_name, group, faiss_ids)

    def delete_face_db(self, app_key, collection_name, group):
        # 删除mysql 表里的记录
        # 获取数据
        sess = self.mysql_db.get_session()
        try:
            sess.query(UserCollection).filter(UserCollection.app_key == app_key,
                                              UserCollection.collection_name == collection_name,
                                              UserCollection.group == group).delete()
            sess.query(Entity).filter(Entity.collection_name == collection_name,
                                      Entity.group == group).delete()
            sess.query(Faces).filter(Faces.collection_name == collection_name,
                                     Faces.group == group).delete()
            sess.commit()
        except OperationalError as ex:
            sess.rollback()
            logger.error(str(ex))
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        except Exception as ex:
            sess.rollback()
            logger.error(str(ex))
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        finally:
            self.mysql_db.close_session(sess)
        # 删除faiss里的group
        self.faiss_db.delete_group(collection_name, group)

    def count(self, collection_name):
        results = []
        # 按照group分组
        sess = self.mysql_db.get_session()
        try:
            user_group = sess.query(Faces.group, func.count(Faces.id)).\
                filter(Faces.collection_name == collection_name).group_by("group").all()
        except OperationalError as ex:
            logger.error(str(ex))
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        except Exception as ex:
            logger.error(str(ex))
            raise ApiServerException(HTTPStatus.DBOPERATEERR.value, 
                                     HTTPStatus.DBOPERATEERR.phrase, 
                                     HTTPStatus.DBOPERATEERR.description)
        finally:
            self.mysql_db.close_session(sess)
        return results
