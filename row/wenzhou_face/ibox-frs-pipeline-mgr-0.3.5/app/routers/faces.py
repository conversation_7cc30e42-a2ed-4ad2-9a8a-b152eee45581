from app.checker.face import check_params, check_add_face_entity_params, \
    check_list_face_entities_params, check_update_face_entity, check_is_none, check_is_not_str, check_update_face_db,\
    check_is_not_int, check_request_body_type, check_add_face_params, check_is_empty
from app.checker.face import user_collection_authen

# from gevent import monkey
# monkey.patch_all(thread=False)

# -*- coding:utf-8 -*-
"""
Face router
"""
import logging
from pydantic import BaseModel
from fastapi import APIRouter, Request, Header
from app.bussiness.faces import FacesModule
from app.bussiness.user import UserModule

from fastapi.encoders import jsonable_encoder
from app.utils.http_utils import HTTPStatus
from app.utils.exception_utils import ApiServerException
from app.checker.face import check_params


logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/faces",
    tags=["faces"],
)


@router.post("/create_face_db",
             summary="创建人脸数据库",
             description="创建人脸数据库",
             response_description="请求成功",
             name="创建人脸数据库")
def create_face_db(params: BaseModel, request: Request, app_key: str = Header("AppKey")):
    data = request._json
    check_request_body_type(data)
    group = data.get("Name", None)
    if check_is_none(group):
        logger.info("name参数不能为空")
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    if check_is_not_str(group):
        raise ApiServerException(HTTPStatus.IMAGEERR.value, 
                                 HTTPStatus.IMAGEERR.phrase, 
                                 HTTPStatus.IMAGEERR.description)
    if check_is_empty(group):
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    # 判断是否已经创建好底库
    user_module = UserModule.get_instance()
    collection_name, collection_part_name_list = user_module.get_list_dbs(app_key)
    if not collection_name:
        #  此app_key没有底库，创建底库
        collection_name = user_module.add_collection(app_key)
    if collection_part_name_list and group in collection_part_name_list:
        logger.info("%s 人脸库已经存在mysql" % group)
        raise ApiServerException(HTTPStatus.EXIST.value, 
                                 HTTPStatus.EXIST.phrase, 
                                 HTTPStatus.EXIST.description)
    faces_module = FacesModule.get_instance()
    message = faces_module.create_faiss_collection_part(collection_name, group)
    if message == "exist":
        logger.info("%s 人脸库已经存在faiss" % group)
        raise ApiServerException(HTTPStatus.EXIST.value, 
                                 HTTPStatus.EXIST.phrase, 
                                 HTTPStatus.EXIST.description)
    # 添加成功后，记录app_key与collection_name, group 的对应关系
    user_module.add_user_collection(app_key, collection_name, group)
    result = {"name": group}
    return jsonable_encoder({"code": int(HTTPStatus.SUCCESS.value), "message": message, "result": result})


@router.post("/list_face_dbs",
             summary="查询人脸数据库列表",
             description="查询人脸数据库列表",
             response_description="请求成功",
             name="查询人脸数据库列表")
def list_face_dbs(params: BaseModel, request: Request, app_key: str = Header("AppKey")):
    data = request._json
    check_request_body_type(data)
    page = data.get("Page", None)
    page_size = data.get("PageSize", None)
    if check_is_none(page) or check_is_none(page_size):
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    if check_is_not_int(page) or check_is_not_int(page_size):
        raise ApiServerException(HTTPStatus.IMAGEERR.value, 
                                 HTTPStatus.IMAGEERR.phrase, 
                                 HTTPStatus.IMAGEERR.description)
    if page <= 0 or page_size <= 0:
        raise ApiServerException(HTTPStatus.INTERR.value, 
                                 HTTPStatus.INTERR.phrase, 
                                 HTTPStatus.INTERR.description)
    user_module = UserModule.get_instance()
    result = user_module.list_face_dbs(app_key, page, page_size)
    return jsonable_encoder({"code": int(HTTPStatus.SUCCESS.value), "message": "OK", "result": result})


@router.post("/add_face_entity",
             summary="添加人脸样本",
             description="添加人脸样本",
             response_description="请求成功",
             name="添加人脸样本")
def add_face_entity(params: BaseModel, request: Request, app_key: str = Header("AppKey")):
    data = request._json
    check_request_body_type(data)
    collection_name = check_add_face_entity_params(data, app_key)
    faces_module = FacesModule.get_instance()
    result = faces_module.add_face_entity(data, collection_name)
    return jsonable_encoder({"code": int(HTTPStatus.SUCCESS.value), "message": "success", "result": result})


@router.post("/get_face_entity",
             summary="查询人脸样本",
             description="查询人脸样本",
             response_description="请求成功",
             name="查询人脸样本")
def get_face_entity(params: BaseModel, request: Request, app_key: str = Header("AppKey")):
    data = request._json
    check_request_body_type(data)
    collection_name = check_add_face_entity_params(data, app_key)
    faces_module = FacesModule.get_instance()
    result = faces_module.get_face_entity(data, collection_name)
    return jsonable_encoder({"code": int(HTTPStatus.SUCCESS.value), "message": "success", "result": result})


@router.post("/list_face_entities",
             summary="查询人脸样本列表",
             description="查询人脸样本列表",
             response_description="请求成功",
             name="查询人脸样本列表")
def list_face_entities(params: BaseModel, request: Request, app_key: str = Header("AppKey")):
    data = request._json
    check_request_body_type(data)
    collection_name = check_list_face_entities_params(data, app_key)
    faces_module = FacesModule.get_instance()
    result = faces_module.list_face_entities(data, collection_name)
    return jsonable_encoder({"code": int(HTTPStatus.SUCCESS.value), "message": "success", "result": result})


@router.post("/update_face_entity",
             summary="更新人脸样本",
             description="更新人脸样本",
             response_description="请求成功",
             name="更新人脸样本")
def update_face_entity(params: BaseModel, request: Request, app_key: str = Header("AppKey")):
    data = request._json
    check_request_body_type(data)
    collection_name = check_update_face_entity(data, app_key)
    faces_module = FacesModule.get_instance()
    faces_module.update_face_entity(data, collection_name)
    new_entity_id = data.get("NewEntityId", None)
    return jsonable_encoder({"code": int(HTTPStatus.SUCCESS.value), "message": "success", "result": {"entity_id": new_entity_id}})

# @router.post("/update_face_db",
#              summary="更新人脸底库名",
#              description="更新人脸底库名",
#              response_description="请求成功",
#              name="更新人脸底库名")
# def update_face_db(params: BaseModel, request: Request, app_key: str = Header("AppKey")):
#     data = request._json
#     check_request_body_type(data)
#     collection_name = check_update_face_db(data, app_key)
#     faces_module = FacesModule.get_instance()
#     faces_module.update_face_group(data, collection_name)
#     new_dbname_id = data.get("NewDbName", None)
#     return jsonable_encoder({"code": int(HTTPStatus.SUCCESS.value), "message": "success", "result": {"dbname_id": new_dbname_id}})

@router.post("/add_face",
             summary="添加人脸数据",
             description="添加人脸数据",
             response_description="请求成功",
             name="添加人脸数据")
def add_face(params: BaseModel, request: Request, app_key: str = Header("AppKey")):
    data = request._json
    check_request_body_type(data)
    images, collection_name = check_add_face_params(data, app_key)
    faces_module = FacesModule.get_instance()
    result = faces_module.add_face(data, images, collection_name)
    return jsonable_encoder({"code": int(HTTPStatus.SUCCESS.value), "message": "success", "result": result})


@router.post("/search_face",
             summary="人脸搜索",
             description="人脸搜索",
             response_description="请求成功",
             name="人脸搜索")
def search_face(params: BaseModel, request: Request, app_key: str = Header("AppKey")):
    data = request._json
    check_request_body_type(data)
    images, collection_name = check_params(data, app_key)
    group = data.get("DbName", None)
    top_k = data.get("TopK", 1)
    start_time = data.get("StartTime", None)
    end_time = data.get("EndTime", None)
    boxes = data.get("Boxes", None)
    landmarks = data.get("Landmark", None)
    faces_module = FacesModule.get_instance()
    result = faces_module.search_face(collection_name, group, images, boxes, landmarks, top_k,
                                              start_time, end_time)
    return jsonable_encoder({"code": int(HTTPStatus.SUCCESS.value), "message": "success", "result": result})


@router.post('/delete_face',
             summary="删除人脸",
             description="删除人脸",
             response_description="请求成功",
             name="删除人脸")
def delete_face(params: BaseModel, request: Request, app_key: str = Header("AppKey")):
    data = request._json
    check_request_body_type(data)
    group = data.get("DbName", None)
    face_id = data.get("FaceId", None)
    if check_is_none(group) or check_is_none(face_id):
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    if check_is_not_str(group) or check_is_not_str(face_id):
        raise ApiServerException(HTTPStatus.IMAGEERR.value, 
                                 HTTPStatus.IMAGEERR.phrase, 
                                 HTTPStatus.IMAGEERR.description)
    if check_is_empty(group) or check_is_empty(face_id):
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    # 校验group和app_key的一致性
    user_collection_authen(app_key, group)
    faces_module = FacesModule.get_instance()
    faces_module.delete_face(face_id)
    return jsonable_encoder({"code": int(HTTPStatus.SUCCESS.value), "message": "success", "result": {"face_id": face_id}})


@router.post('/delete_face_entity',
             summary="删除人脸样本",
             description="删除人脸样本",
             response_description="请求成功",
             name="删除人脸样本")
def delete_face_entity(params: BaseModel, request: Request, app_key: str = Header("AppKey")):
    data = request._json
    check_request_body_type(data)
    group = data.get("DbName", None)
    entity_id = data.get("EntityId", None)
    if check_is_none(group) or check_is_none(entity_id):
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    if check_is_not_str(group) or check_is_not_str(entity_id):
        raise ApiServerException(HTTPStatus.IMAGEERR.value, 
                                 HTTPStatus.IMAGEERR.phrase, 
                                 HTTPStatus.IMAGEERR.description)
    if check_is_empty(group) or check_is_empty(entity_id):
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    # 校验group和app_key的一致性
    collection_name = user_collection_authen(app_key, group)
    faces_module = FacesModule.get_instance()
    faces_module.delete_face_entity(collection_name, group, entity_id)
    return jsonable_encoder({"code": int(HTTPStatus.SUCCESS.value), "message": "success", "result": {"entity_id": entity_id}})


@router.post("/delete_face_db",
             summary="删除数据库",
             description="删除数据库",
             response_description="请求成功",
             name="删除数据库")
def delete_face_db(params: BaseModel, request: Request, app_key: str = Header("AppKey")):
    data = request._json
    check_request_body_type(data)
    group = data.get("Name", None)
    if check_is_none(group):
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    if check_is_not_str(group):
        raise ApiServerException(HTTPStatus.IMAGEERR.value, 
                                 HTTPStatus.IMAGEERR.phrase, 
                                 HTTPStatus.IMAGEERR.description)
    if check_is_empty(group):
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    collection_name = user_collection_authen(app_key, group)
    faces_module = FacesModule.get_instance()
    faces_module.delete_face_db(app_key, collection_name, group)
    return jsonable_encoder({"code": int(HTTPStatus.SUCCESS.value), "message": "success", "result": {"name": group}})
