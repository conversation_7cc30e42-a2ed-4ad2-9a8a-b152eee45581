# coding: utf-8
"""
__init__.py
"""
import glob
from importlib import import_module
from os.path import basename, dirname, isfile, join


def init_app(app, **kwargs):
    modules = glob.glob(join(dirname(__file__), '*.py'))
    modules = [basename(f)[:-3] for f in modules if isfile(f) and not f.endswith('__init__.py')]
    for m in modules:
        app.include_router(import_module('.{}'.format(m), package=__name__).router)
