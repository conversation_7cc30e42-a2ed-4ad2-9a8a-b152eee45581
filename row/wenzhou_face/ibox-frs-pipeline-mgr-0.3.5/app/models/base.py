# !/bin/env python
# -*- encoding: utf-8 -*-

from pydantic import BaseConfig
from sqlalchemy import Column, DateTime, String, Integer
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from datetime import datetime
BaseData = declarative_base()


class DataConfig(BaseConfig):
    orm_mode = True


# 图片路径与milvus对应表
class Faces(BaseData):
    """
    图片路径和milvus对应关系表
    """
    __tablename__ = "faiss_faces"

    id: str = Column(Integer, primary_key=True, autoincrement=True, comment="自增ID")
    """自增ID"""

    entity_uid: str = Column(String(255), comment="实体ID对应的映射ID", nullable=False)
    """实体ID对应的映射ID"""

    extra_data: str = Column(String(255), comment="自定义信息")
    """自定义信息"""

    collection_name: str = Column(String(255), comment="图片对应的milvus底库", nullable=False)
    """图片对应的底库"""

    group: str = Column(String(255), comment="图片对应的group", nullable=False)
    """图片对应的group"""

    create_time: datetime = Column(DateTime, comment="创建时间", nullable=False, server_default=func.now())
    """创建时间"""

    update_time: datetime = Column(DateTime, comment="修改时间",  nullable=False, server_default=func.now(),
                                   server_onupdate=func.now())
    """修改时间"""

    def to_dict(self):
        """将对象转换为字典数据"""
        face_dict = {
            "id": self.id,
            "face_id": str(self.id),
            "collection_name": self.collection_name,
            "db_name": self.group,
            "entity_uid": self.entity_uid,
            "extra_data": self.extra_data if self.extra_data else "",
            "create_time": self.create_time.strftime("%Y-%m-%d %H:%M:%S")
        }
        return face_dict


# 人物实体表
class Entity(BaseData):
    """人物实体ID表"""
    __tablename__ = "entity"
    id: str = Column(Integer, primary_key=True, autoincrement=True, comment="自增ID")
    """自增ID"""

    entity_id: str = Column(String(255), comment="实体ID", nullable=False)
    """实体ID"""

    collection_name: str = Column(String(255), comment="实体对应的milvus底库", nullable=False)
    """实体对应的底库"""

    group: str = Column(String(255), comment="实体对应的group", nullable=False)
    """实体对应的group"""

    labels: str = Column(String(255), comment="实体对应的标签")
    """实体对应的标签"""

    create_time: datetime = Column(DateTime, comment="创建时间", nullable=False, server_default=func.now())
    """创建时间"""

    update_time: datetime = Column(DateTime, comment="修改时间",  nullable=False, server_default=func.now(),
                                   server_onupdate=func.now())
    """修改时间"""

    def to_dict(self):
        """将对象转换为字典数据"""
        entity_dict = {
            "id": self.id,
            "entity_id": self.entity_id,
            # "collection_name": self.collection_name,
            "db_name": self.group,
            "labels": self.labels if self.labels else "",
            "create_time": self.create_time.strftime("%Y-%m-%d %H:%M:%S")
        }
        return entity_dict


# app_key与collection表
class UserCollection(BaseData):
    """
    app_key与collection表
    """
    __tablename__ = "user_collection"

    id: str = Column(Integer, primary_key=True, autoincrement=True, comment="自增ID")
    """自增ID"""
    app_key: str = Column(String(255), comment="邮箱", nullable=False)
    collection_name: str = Column(String(255), comment="创建的collection", nullable=False)
    group: str = Column(String(255), comment="创建的分区名称")
    create_time: datetime = Column(DateTime, comment="创建时间", nullable=False, server_default=func.now())
    """创建时间"""
    update_time: datetime = Column(DateTime, comment="修改时间",  nullable=False, server_default=func.now(),
                                   server_onupdate=func.now())
    """修改时间"""

    def to_dict(self):
        """将对象转换为字典数据"""
        name_dict = {
            "collection_name": self.collection_name,
            "group": self.group if self.group else None
        }
        return name_dict


# 记录请求信息
class RequestDetail(BaseData):
    """
    请求信息表
    """
    __tablename__ = "detail"

    id: str = Column(Integer, primary_key=True, autoincrement=True, comment="自增ID")
    """自增ID"""

    ip: str = Column(String(255), comment="请求IP", nullable=False)
    """请求IP"""

    action: str = Column(String(255), comment="请求算法", nullable=False)
    """请求算法"""

    app_key: str = Column(String(255), comment="请求app_key", nullable=True)
    """请求app_key"""

    create_time: datetime = Column(DateTime, comment="创建时间", nullable=False, server_default=func.now())
    """创建时间"""

    update_time: datetime = Column(DateTime, comment="修改时间",  nullable=False, server_default=func.now(),
                         server_onupdate=func.now())
    """修改时间"""

    def to_dict(self):
        """将对象转换为字典数据"""
        datail_dict = {
            "id": self.id,
            "app_key": self.app_key,
            "ip": self.ip,
            "action": self.action,
            "create_time": self.create_time.strftime("%Y-%m-%d %H:%M:%S")
        }
        return datail_dict
