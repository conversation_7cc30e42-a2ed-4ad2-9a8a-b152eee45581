# !/bin/env python
# -*- encoding: utf-8 -*-
import time
from typing import Optional, List
from pydantic import BaseModel, Field


class CreateFaceDb(BaseModel):
    Name: str = Field(None, description="数据库名称", sqlalchemy_safe=True)


class ListFaceDbs(BaseModel):
    Page: int = Field(1, description="页码", sqlalchemy_safe=True)
    PageSize: int = Field(10, description="每页显示个数", sqlalchemy_safe=True)


class AddFaceEntity(BaseModel):
    DbName: str = Field(None, description="数据库名称", sqlalchemy_safe=True)
    EntityId: str = Field(None, description="实体ID", sqlalchemy_safe=True)
    Labels: str = Field(None, description="标签", sqlalchemy_safe=True)


class GetFaceEntity(BaseModel):
    DbName: str = Field(None, description="数据库名称", sqlalchemy_safe=True)
    EntityId: str = Field(None, description="实体ID", sqlalchemy_safe=True)


class ListFaceEntities(BaseModel):
    DbName: str = Field(None, description="数据库名称", sqlalchemy_safe=True)
    Page: int = Field(1, description="页码", sqlalchemy_safe=True)
    PageSize: int = Field(10, description="每页显示个数", sqlalchemy_safe=True)
    Order: str = Field("asc", description="排序方式", sqlalchemy_safe=True)
    Labels: str = Field(None, description="标签", sqlalchemy_safe=True)
    EntityIdPrefix: str = Field(None, description="实体ID", sqlalchemy_safe=True)


class UpdateFaceEntity(BaseModel):
    DbName: str = Field(None, description="数据库名称", sqlalchemy_safe=True)
    EntityId: str = Field(None, description="需要更新的实体ID", sqlalchemy_safe=True)
    NewEntityId: str = Field(None, description="更新结果的实体ID", sqlalchemy_safe=True)
    Labels: str = Field(None, description="标签", sqlalchemy_safe=True)


class AddFace(BaseModel):
    ImageData: Optional[str] = Field(None, description="待添加图片base64", sqlalchemy_safe=True)
    ImageUrl: Optional[str] = Field(None, description="待添加图片链接", sqlalchemy_safe=True)
    TimeStamp: Optional[int] = Field(int(time.time()), description="时间戳", sqlalchemy_safe=True)
    DbName: str = Field(None, description="数据库名称", sqlalchemy_safe=True)
    EntityId: str = Field(None, description="实体ID", sqlalchemy_safe=True)
    ExtraData: Optional[str] = Field(None, description="自定义信息", sqlalchemy_safe=True)


class SearchFace(BaseModel):
    ImageData: Optional[str] = Field(None, description="待搜索图片base64", sqlalchemy_safe=True)
    ImageUrl: Optional[str] = Field(None, description="待搜索图片链接", sqlalchemy_safe=True)
    StartTime: Optional[int] = Field(None, description="查询的起始时间戳", sqlalchemy_safe=True)
    EndTime: Optional[int] = Field(None, description="查询的结束时间戳", sqlalchemy_safe=True)
    TopK: Optional[int] = Field(3, description="搜图topk", sqlalchemy_safe=True)
    DbName: List[str] = Field(None, description="搜索的数据库名称列表", sqlalchemy_safe=True)


class DeleteFace(BaseModel):
    DbName: str = Field(None, description="数据库名称", sqlalchemy_safe=True)
    FaceId: str = Field(None, description="待删除的FACEID", sqlalchemy_safe=True)


class GetImageParams(BaseModel):
    FaceId: str = Field(None, description="待查询的FACEID", sqlalchemy_safe=True)

