# -*- coding:utf-8 -*-
"""
HTTP/REST server
"""
import logging
from fastapi import Fast<PERSON><PERSON>, Request
from starlette.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from app.bussiness import init
from app.config.config import cfg
from app.utils.http_utils import HTTPStatus
from app.utils.fastapi_utils import ApiServerFailedResponse
from app.utils.exception_utils import ApiServerException


from app.routers import init_app

logger = logging.getLogger(__name__)


def on_start():
    """
    模块初始化
    :return: None
    :rtype: None
    """
    # 初始化modules
    init(cfg)

app = FastAPI(title="faces_server服务",
              on_startup=[on_start])


app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

init_app(app)


# ---------- BEGIN 异常处理 ----------
@app.exception_handler(ApiServerException)
def handle_apiserver_exception(request: Request, exc: ApiServerException):
    data = jsonable_encoder(ApiServerFailedResponse(code=exc.status, 
                                                    message=exc.message, 
                                                    details=exc.details))
    return JSONResponse(content=data, status_code=200)


@app.exception_handler(Exception)
def handle_exception(request: Request, exc: Exception):
    data = jsonable_encoder(ApiServerFailedResponse(code=HTTPStatus.INTERNAL_SERVER_ERROR.value,
                                                    message=HTTPStatus.INTERNAL_SERVER_ERROR.phrase,
                                                    details=HTTPStatus.INTERNAL_SERVER_ERROR.description))
    return JSONResponse(content=data, status_code=200)


@app.exception_handler(RequestValidationError)
def validation_exception_handler(request: Request, exc: RequestValidationError):
    if not exc.body:
        data = jsonable_encoder(ApiServerFailedResponse(code=HTTPStatus.BODYERR.value,
                                                        message=HTTPStatus.BODYERR.phrase,
                                                        details=HTTPStatus.BODYERR.description))
        return JSONResponse(content=data, status_code=200)
    elif not isinstance(exc.body, dict):
        data = jsonable_encoder(ApiServerFailedResponse(code=HTTPStatus.BODYTYPEERR.value,
                                                        message=HTTPStatus.BODYTYPEERR.phrase,
                                                        details=HTTPStatus.BODYTYPEERR.description))
        return JSONResponse(content=data, status_code=200)
    if exc.errors():
        data = jsonable_encoder(ApiServerFailedResponse(code=HTTPStatus.IMAGEERR.value,
                                                        message=HTTPStatus.IMAGEERR.phrase,
                                                        details=HTTPStatus.IMAGEERR.description))
        return JSONResponse(content=data, status_code=200)


@app.exception_handler(StarletteHTTPException)
def http_exception_handler(request, exc):
    if exc.status_code == 405:
        data = jsonable_encoder(ApiServerFailedResponse(code=HTTPStatus.REQUESTMETHOD.value,
                                                        message=HTTPStatus.REQUESTMETHOD.phrase,
                                                        details=HTTPStatus.REQUESTMETHOD.description))
        return JSONResponse(content=data, status_code=200)
    elif exc.status_code == 404:
        data = jsonable_encoder(ApiServerFailedResponse(code=HTTPStatus.REQUESTNOTFOUND.value,
                                                        message=HTTPStatus.REQUESTNOTFOUND.phrase,
                                                        details=HTTPStatus.REQUESTNOTFOUND.description))
        return JSONResponse(content=data, status_code=200)

# =========== END 异常处理 ==========