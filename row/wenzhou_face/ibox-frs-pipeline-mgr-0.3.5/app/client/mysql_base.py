# !/bin/env python

import logging
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine


logger = logging.getLogger(__name__)

class MySQLBase(object):

    def __init__(self, mysql_host, mysql_port, mysql_database, mysql_user, mysql_password,
                 mysql_charset="utf8mb4", **kwargs):
        """
        :param mysql_host:
        :param mysql_port:
        :param mysql_database:
        :param mysql_user:
        :param mysql_password:
        :param mysql_charset:
        """
        self.uri = "mysql+pymysql://%s:%s@%s:%d/%s?charset=%s" % (mysql_user, mysql_password, mysql_host,
                                                                  mysql_port, mysql_database, mysql_charset)

        self.engine = create_engine(self.uri, pool_pre_ping=True, pool_size=100, pool_recycle=3600, echo=False)
        self.session_local = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)

    def get_session(self):
        """
        开始数据库事务

        :return: session
        :rtype: Session
        """
        sess = self.session_local()
        return sess

    def close_session(self, sess):
        """
        :param sess:
        :type sess: Session
        :return:
        """
        try:
            sess.close()
        except Exception as ex:
            logger.error(f"closing db session failed: {ex}")
        finally:
            pass




