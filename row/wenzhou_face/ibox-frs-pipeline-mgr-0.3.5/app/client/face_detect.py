import glob
import os
import sys
import time
import cv2
import math
import gevent
import gevent.ssl
# from gevent import monkey
# monkey.patch_all()
# # monkey.patch_all()
# monkey.patch_all(thread=False, ssl=False)
import numpy as np
import pybase64 as base64
import tritonclient.http as httpclient
from PIL import Image
import yaml
import logging
import os
import fleep
import threading
import skimage.transform
from app.utils.exception_utils import ApiServerException
from app.utils.http_utils import HTTPStatus
#from teleexception import StatusException, HTTPStatus
from io import BytesIO

logger = logging.getLogger(__name__)

CONFIG_YAML_PATH = "../../config.yaml"
LOCK_PATH = f'/tmp/init_logger'

with open(os.path.join(os.path.dirname(__file__), CONFIG_YAML_PATH), "r", encoding="utf-8") as f:
    configs = yaml.load(f, Loader=yaml.FullLoader)
    if not os.path.exists(LOCK_PATH) or not os.path.isdir(LOCK_PATH):
        os.makedirs(LOCK_PATH, exist_ok=True)
        logger.info(f"Init, {configs}")

action_configs = configs["action"]  # ACTION
port_configs = configs["ports"]  # 端口配置
models_configs = configs["models"]  # 模型参数配置
process_configs = configs["process"]  # 运行参数配置
FaceCropAlignProcess_configs = configs["FaceCropAlignProcess"]
params_configs = configs["params"]  # 系统参数配置

# 人脸 端口
# TRITON_URL = os.environ.get(
#     'TRITON_URL', 'localhost:{}'.format(port_configs["TRITON_PORT"]))
TRITON_URL = os.environ.get(
     'TRITON_URL', '************:{}'.format(port_configs["TRITON_PORT"]))

# 人脸检测 模型配置
retinaface_configs = models_configs["FaceDetect"]
# 人脸特征 模型配置
arcface_configs = models_configs["FaceRecog"]


# class ModelClient():
#     def __init__(self, url, model_config):
#         self.model_config = model_config
#         try:
#             if self.model_config["ssl"]:
#                 self.triton_client = httpclient.InferenceServerClient(
#                     url=url,
#                     verbose=self.model_config["verbose"],
#                     ssl=True,
#                     ssl_context_factory=gevent.ssl._create_unverified_context,
#                     insecure=True)
#             else:
#                 self.triton_client = httpclient.InferenceServerClient(
#                     url=url, verbose=self.model_config["verbose"])
#         except Exception as e:
#             logger.error("channel creation failed: " + str(e))
#             sys.exit(1)
#         if self.model_config["http_headers"] is not None:
#             self.headers = {
#                 l.split(':')[0]: l.split(':')[1] for l in self.model_config["http_headers"]
#             }
#         else:
#             self.headers = None
#         self.model_name = self.model_config["model_name"]
#         self.request_compression_algorithm = self.model_config["request_compression_algorithm"]
#         self.response_compression_algorithm = self.model_config["response_compression_algorithm"]

#     def run(self, input_data):
#         inputs = [httpclient.InferInput(input_info["name"], input_data.shape, input_info["dtype"])
#                   for input_info in self.model_config["input"]]
#         inputs[0].set_data_from_numpy(input_data, binary_data=True)

#         outputs = [
#             httpclient.InferRequestedOutput(output_info["name"], binary_data=True) for output_info in self.model_config["output"]
#         ]
#         query_params = {'test_1': 1, 'test_2': 2}
#         results = self.triton_client.async_infer(
#             self.model_name,
#             inputs,
#             outputs=outputs,
#             query_params=query_params,
#             headers=self.headers,
#             request_compression_algorithm=self.request_compression_algorithm,
#             response_compression_algorithm=self.response_compression_algorithm)
#         return results


class ModelClient:
    def __init__(self, url, model_config):
        self.model_config = model_config
        try:
            if self.model_config["ssl"]:
                self.triton_client = httpclient.InferenceServerClient(
                    url=url,
                    verbose=self.model_config["verbose"],
                    ssl=True,
                    ssl_context_factory=gevent.ssl._create_unverified_context,
                    insecure=True,
                )
            else:
                self.triton_client = httpclient.InferenceServerClient(
                    url=url, verbose=self.model_config["verbose"]
                )
        except Exception as e:
            logger.error("channel creation failed: " + str(e))
            sys.exit(1)
        if self.model_config["http_headers"] is not None:
            self.headers = {
                l.split(":")[0]: l.split(":")[1]
                for l in self.model_config["http_headers"]
            }
        else:
            self.headers = None
        self.model_name = self.model_config["model_name"]
        self.request_compression_algorithm = self.model_config[
            "request_compression_algorithm"
        ]
        self.response_compression_algorithm = self.model_config[
            "response_compression_algorithm"
        ]

    def run(self, input_data):
        inputs = [
            httpclient.InferInput(
                input_info["name"], input_data.shape, input_info["dtype"]
            )
            for input_info in self.model_config["input"]
        ]
        inputs[0].set_data_from_numpy(input_data, binary_data=True)
        # logger.info(inputs[0].shape)

        outputs = [
            httpclient.InferRequestedOutput(output_info["name"], binary_data=True)
            for output_info in self.model_config["output"]
        ]
        query_params = {"test_1": 1, "test_2": 2}
        results = self.triton_client.async_infer(
            self.model_name,
            inputs,
            outputs=outputs,
            query_params=query_params,
            headers=self.headers,
            request_compression_algorithm=self.request_compression_algorithm,
            response_compression_algorithm=self.response_compression_algorithm,
        )
        return results




class FaceCropAlign(threading.Thread):
    def __init__(self, image_str, boxes, landmarks, h, w, M,padding=0, mean=0, std=1):
        super(FaceCropAlign, self).__init__()
        self.image_str = image_str
        self.boxes = boxes
        self.landmarks = landmarks
        self.height = h
        self.width = w
        self.padding = padding
        self.mean = mean
        self.std = std
        self.tran_mat = (np.array(M, dtype=np.float32).reshape(-1,2) + padding) / (1 + 2 * padding) * np.array([w, h])
        self.out_0 = list()

    def run(self):
        # read image
        #img_bytes = io.BytesIO(self.image_str[0])
        #img_pil = Image.open(img_bytes).convert('RGB')
        #img = np.asarray(img_pil)
        img = self.image_str
        img_h, img_w, img_c = img.shape

        for box, landmark in zip(self.boxes, self.landmarks):

            shape = self.tran_mat.shape
            landmarkn = landmark[:(shape[0]*shape[1])].reshape((-1, 2))
            st = skimage.transform.SimilarityTransform()

            st.estimate(landmarkn, self.tran_mat)
            crop_img_np = cv2.warpAffine(img, st.params[0:2, :], (self.height, self.width), flags=cv2.INTER_LINEAR,
                                         borderValue=0.0)

            # crop_img_np = (crop_img_np - self.mean) / self.std

            # x1 = int(box[0])
            # y1 = int(box[1])
            # x2 = int(box[0] + box[2])
            # y2 = int(box[1] + box[3])
            # x1 = max(0,x1)
            # y1 = max(0,y1)
            # x2 = min(img_w, x2)
            # y2 = min(img_h, y2)
            # crop_img_np = img[y1:y2,x1:x2]
            # logger.info(crop_img_np.shape)
            # crop_img_np = cv2.resize(crop_img_np, (self.width, self.height),
            #                  interpolation=cv2.INTER_LINEAR)
            crop_img_np = crop_img_np.astype(np.float32)
            crop_img_np = (crop_img_np - self.mean) / self.std
            crop_img_np = np.transpose(crop_img_np, (2, 0, 1))
            crop_img_np = np.ascontiguousarray(crop_img_np)
            self.out_0.append(crop_img_np)

    def getResult(self):
        return self.out_0

class FaceCropAlign_Client:
    def __init__(self, FaceCropAlignProcess_configs):
        # self.model_config = model_config = json.loads(args['model_config'])

        # output0_config = pb_utils.get_output_config_by_name(
        #     self.model_config, "croped_image")
        # self.output0_dtype = pb_utils.triton_string_to_numpy(
        #     output0_config['data_type'])
        # self.output0_dims = output0_config['dims']

        # output1_config = pb_utils.get_output_config_by_name(
        #     self.model_config, "objIdxFilter")
        # self.output1_dtype = pb_utils.triton_string_to_numpy(
        #     output1_config['data_type'])

        # output2_config = pb_utils.get_output_config_by_name(
        #     self.model_config, "landmarkFilter")
        # self.output2_dtype = pb_utils.triton_string_to_numpy(
        #     output2_config['data_type'])
        self.output_crop_w = 112
        self.output_crop_h = 112
        self.align_process_config = FaceCropAlignProcess_configs
        try:
            self.mean = eval(self.align_process_config['MEAN'])
        except:
            self.mean = 0
        try:
            self.std = eval(self.align_process_config['STD'])
        except:
            self.std = 1
        try:
            self.padding = eval(self.align_process_config['PADDING'])
        except:
            self.padding = 0
        try:
            self.max_batch_size = eval(self.align_process_config['MAX_BATCH_SIZE'])
        except:
            self.max_batch_size = 32
        try:
            self.min_box_size = eval(self.align_process_config['MIN_BOX_SIZE'])
        except:
            self.min_box_size = [20, 20]
        try:
            self.transform_mat = eval(self.align_process_config['TRANSFORM_MAT'])
        except:
            self.transform_mat = [1,1]

    def execute(self, images, obj_idxes, landmarks):
        # _, c, h, w = self.output0_dims
        h = self.output_crop_h
        w = self.output_crop_w
        responses = list()
        #in_0 = images
        #in_1 = obj_idxes
        #in_2 = landmarks
        image_strs = images
        boxes = obj_idxes
        landmarks = landmarks

        #out_0_all = list()
        # discard boxes size less than min box size
        out_0_all = list()
        # filter_ids = np.where((boxes[:, 2] - boxes[:, 0] > self.min_box_size[0]) & (
        #             boxes[:, 3] - boxes[:, 1] > self.min_box_size[1]))[0]
        filter_ids = np.where((boxes[:, 2] > self.min_box_size[0]) & (
                    boxes[:, 3]> self.min_box_size[1]))[0]
        if len(filter_ids) > 0:
            boxes = boxes[filter_ids]
            landmarks = landmarks[filter_ids]
            thread_list = list()
            img_ids = np.unique(boxes[:, -1]).astype(np.int32)
            if image_strs.ndim == 3:
                image_strs = np.expand_dims(image_strs, axis = 0)
            for img_id in img_ids:
                img_selected = image_strs[img_id]
                box_ids = np.where(boxes[:, -1] == img_id)[0]
                box_selected = boxes[box_ids]
                landmark_selected = landmarks[box_ids]
                t = FaceCropAlign(img_selected, box_selected[:, :4], landmark_selected, h, w,
                                      self.transform_mat,self.padding,
                                      self.mean, self.std)
                t.start()
                thread_list.append(t)

            for t in thread_list:
                t.join()
                out_0 = t.getResult()
                if len(out_0) > 0:
                    out_0_all.extend(out_0)
            b = self.max_batch_size if len(out_0_all) > self.max_batch_size else len(out_0_all)

            out_0_all_np = np.stack(out_0_all[:b])
            out_0_all_np = np.ascontiguousarray(out_0_all_np)
            out_1_all_np = np.stack(boxes[:b])
            out_1_all_np = np.ascontiguousarray(out_1_all_np)
            out_2_all_np = np.stack(landmarks[:b])
            out_2_all_np = np.ascontiguousarray(out_2_all_np)
        else:
            # return a dark test pic
            out_0_all_np = np.zeros((1, 3, h, w))
            out_1_all_np = np.ones((1, 7)) * -1
            out_2_all_np = np.ones((1, 10)) * -1
        output = [out_0_all_np, out_1_all_np, out_2_all_np]
        return out_0_all_np, out_1_all_np

class ScrfdPostProcess(object):

    def __init__(self,
                 fmc=3,
                 feat_stride_fpn=[8, 16, 32],
                 num_anchors=2,
                 use_kps=True,
                 batched=False,
                 thresh=0.5,
                 nms_thresh=0.4,
                 input_size=(640,640)):
        self._fmc = fmc
        self._feat_stride_fpn = feat_stride_fpn
        self._num_anchors = num_anchors
        self._use_kps = use_kps
        self._batched = batched
        self._center_cache = {}
        self._thresh = thresh
        self._nms_thresh = nms_thresh
        self._input_size = input_size

    def _softmax(self, z):
        assert len(z.shape) == 2
        s = np.max(z, axis=1)
        s = s[:, np.newaxis]  # necessary step to do broadcasting
        e_x = np.exp(z - s)
        div = np.sum(e_x, axis=1)
        div = div[:, np.newaxis]  # dito
        return e_x / div

    def _distance2bbox(self, points, distance, max_shape=None):
        """Decode distance prediction to bounding box.

        Args:
            points (Tensor): Shape (n, 2), [x, y].
            distance (Tensor): Distance from the given point to 4
                boundaries (left, top, right, bottom).
            max_shape (tuple): Shape of the image.

        Returns:
            Tensor: Decoded bboxes.
        """
        x1 = points[:, 0] - distance[:, 0]
        y1 = points[:, 1] - distance[:, 1]
        x2 = points[:, 0] + distance[:, 2]
        y2 = points[:, 1] + distance[:, 3]
        if max_shape is not None:
            x1 = x1.clamp(min=0, max=max_shape[1])
            y1 = y1.clamp(min=0, max=max_shape[0])
            x2 = x2.clamp(min=0, max=max_shape[1])
            y2 = y2.clamp(min=0, max=max_shape[0])
        return np.stack([x1, y1, x2, y2], axis=-1)

    def _distance2kps(self, points, distance, max_shape=None):
        """Decode distance prediction to bounding box.

        Args:
            points (Tensor): Shape (n, 2), [x, y].
            distance (Tensor): Distance from the given point to 4
                boundaries (left, top, right, bottom).
            max_shape (tuple): Shape of the image.

        Returns:
            Tensor: Decoded bboxes.
        """
        preds = []
        for i in range(0, distance.shape[1], 2):
            px = points[:, i % 2] + distance[:, i]
            py = points[:, i % 2 + 1] + distance[:, i + 1]
            if max_shape is not None:
                px = px.clamp(min=0, max=max_shape[1])
                py = py.clamp(min=0, max=max_shape[0])
            preds.append(px)
            preds.append(py)
        return np.stack(preds, axis=-1)

    def _parase(self, net_outs, thresh):
        scores_list = []
        bboxes_list = []
        kpss_list = []
        input_height, input_width = self._input_size[:]

        for idx, stride in enumerate(self._feat_stride_fpn):
            # If model support batch dim, take first output
            if self._batched:
                scores = net_outs[idx][0]
                bbox_preds = net_outs[idx + self._fmc][0]
                bbox_preds = bbox_preds * stride
                if self._use_kps:
                    kps_preds = net_outs[idx + self._fmc * 2][0] * stride

            # If model doesn't support batching take output as is
            else:
                scores = net_outs[idx]
                # scores = np.reshape(scores, (1, -1, 1))[0]
                bbox_preds = net_outs[idx + self._fmc]
                bbox_preds = bbox_preds * stride
                # bbox_preds = np.reshape(bbox_preds, (1, -1, 4))[0]
                if self._use_kps:
                    kps_preds = net_outs[idx + self._fmc * 2] * stride
                    # kps_preds = np.reshape(kps_preds, (1, -1, 10))[0]

            height = input_height // stride
            width = input_width // stride
            K = height * width
            key = (height, width, stride)
            if key in self._center_cache:
                anchor_centers = self._center_cache[key]
            else:
                #solution-1, c style:
                #anchor_centers = np.zeros( (height, width, 2), dtype=np.float32 )
                #for i in range(height):
                #    anchor_centers[i, :, 1] = i
                #for i in range(width):
                #    anchor_centers[:, i, 0] = i

                #solution-2:
                #ax = np.arange(width, dtype=np.float32)
                #ay = np.arange(height, dtype=np.float32)
                #xv, yv = np.meshgrid(np.arange(width), np.arange(height))
                #anchor_centers = np.stack([xv, yv], axis=-1).astype(np.float32)

                #solution-3:
                anchor_centers = np.stack(np.mgrid[:height, :width][::-1],
                                          axis=-1).astype(np.float32)
                #print(anchor_centers.shape)

                anchor_centers = (anchor_centers * stride).reshape((-1, 2))
                if self._num_anchors > 1:
                    anchor_centers = np.stack([anchor_centers] *
                                              self._num_anchors,
                                              axis=1).reshape((-1, 2))
                if len(self._center_cache) < 100:
                    self._center_cache[key] = anchor_centers

            pos_inds = np.where(scores >= thresh)[0]
            bboxes = self._distance2bbox(anchor_centers, bbox_preds)
            pos_scores = scores[pos_inds]
            pos_bboxes = bboxes[pos_inds]
            scores_list.append(pos_scores)
            bboxes_list.append(pos_bboxes)
            if self._use_kps:
                kpss = self._distance2kps(anchor_centers, kps_preds)
                #kpss = kps_preds
                kpss = kpss.reshape((kpss.shape[0], -1, 2))
                pos_kpss = kpss[pos_inds]
                kpss_list.append(pos_kpss)
        return scores_list, bboxes_list, kpss_list

    def _nms(self, dets):
        thresh = self._nms_thresh
        x1 = dets[:, 0]
        y1 = dets[:, 1]
        x2 = dets[:, 2]
        y2 = dets[:, 3]
        scores = dets[:, 4]

        areas = (x2 - x1 + 1) * (y2 - y1 + 1)
        order = scores.argsort()[::-1]

        keep = []
        while order.size > 0:
            i = order[0]
            keep.append(i)
            xx1 = np.maximum(x1[i], x1[order[1:]])
            yy1 = np.maximum(y1[i], y1[order[1:]])
            xx2 = np.minimum(x2[i], x2[order[1:]])
            yy2 = np.minimum(y2[i], y2[order[1:]])

            w = np.maximum(0.0, xx2 - xx1 + 1)
            h = np.maximum(0.0, yy2 - yy1 + 1)
            inter = w * h
            ovr = inter / (areas[i] + areas[order[1:]] - inter)

            inds = np.where(ovr <= thresh)[0]
            order = order[inds + 1]

        return keep

    def postprocess(self,
                    det_scale,
                    net_outputs):

        scores_list, bboxes_list, kpss_list = self._parase(net_outputs, self._thresh)

        scores = np.vstack(scores_list)
        scores_ravel = scores.ravel()
        order = scores_ravel.argsort()[::-1]
        #logger.info(order)
        bboxes = np.vstack(bboxes_list) / det_scale
        if self._use_kps:
            kpss = np.vstack(kpss_list) / det_scale
        pre_det = np.hstack((bboxes, scores)).astype(np.float32, copy=False)
        pre_det = pre_det[order, :]
        #logger.info(pre_det)
        keep = self._nms(pre_det)
        det = pre_det[keep, :]
        if self._use_kps:
            kpss = kpss[order, :, :]
            kpss = kpss[keep, :, :]
        else:
            kpss = None
        return det, kpss

    def detect(self,
               input_img,
               thresh=0.5,
               input_size=(640,640),
               max_num=0,
               metric='default'):
        """_summary_

        Args:
            input_img (numpy array): bgr numpy array input img

        Returns:
            output of scrfd engine bbox (and kps)
        """
        img, det_scale = self.preprocess(input_img)
        net_outputs = self.inference_on_images([img], new_size=input_size)
        det, kps = self.postprocess(img, det_scale, net_outputs, thresh,
                                    max_num, metric)
        return det, kps


# 人脸检测 模型客户端
detect_client = ModelClient(TRITON_URL, retinaface_configs)
# 人脸特征 模型客户端
feature_client = ModelClient(TRITON_URL, arcface_configs)

class FaceDetectClient():
    def __init__(self, process_configs, reqid=None):
        self.reqid = reqid
        self.process_configs = process_configs
        self.mean = [127.5, 127.5 , 127.5]
        self.std = [127.5, 127.5 , 127.5]
        self.conf_thresh = 0.5 
        self.iou_thresh = 0.4
        self.input_facedetect_w = 640
        self.input_facedetect_h = 640
        self.input_facedetect_mean = 127.5
        self.input_facedetect_std = 128
        self.FaceDetectProprocess = ScrfdPostProcess(thresh=self.conf_thresh, nms_thresh=self.iou_thresh)
        self.FaceCropAlign_Client = FaceCropAlign_Client(FaceCropAlignProcess_configs)
    def preprocess_face_detect(self, input_img):
        """_summary_

        Args:
            input_img (numpy array): rgb numpy array input img

        Returns:
            numpy array: processed img feded into engine
        """
        img = np.array(input_img)
        # img = input_img[..., ::-1]  # rgb 2 bgr
        input_size = (self.input_facedetect_h, self.input_facedetect_w) 
        input_mean, input_std = self.input_facedetect_mean, self.input_facedetect_std
        im_ratio = float(img.shape[0]) / img.shape[1]
        model_ratio = float(input_size[1]) / input_size[0]
        if im_ratio > model_ratio:
            new_height = input_size[1]
            new_width = int(new_height / im_ratio)
        else:
            new_width = input_size[0]
            new_height = int(new_width * im_ratio)
        det_scale = float(new_height) / img.shape[0]
        resized_img = cv2.resize(img, (new_width, new_height))
        det_img = np.zeros((input_size[1], input_size[0], 3), dtype = np.float32)
        det_img[:new_height, :new_width, :] = resized_img

        det_img = np.transpose(det_img, (2, 0, 1))  # 3*112*112, RGB
        # det_imgs = np.zeros((1, 3, input_size[0], input_size[1]),
        #                     dtype=np.uint8)
        # det_imgs[0] = det_img
        # det_imgs = (det_imgs - input_mean) / input_std
        det_img = (det_img - input_mean) / input_std
        det_img = np.ascontiguousarray(det_img)
        det_scale = np.array([det_scale])
        return det_img, det_scale

    def proprocess_face_detect(self, det_scale, face_detect_results):
        in_score_8 = face_detect_results.as_numpy("score_8")
        in_score_16 = face_detect_results.as_numpy("score_16")
        in_score_32 = face_detect_results.as_numpy("score_32")
        in_bbox_8 = face_detect_results.as_numpy("bbox_8")
        in_bbox_16 = face_detect_results.as_numpy("bbox_16")
        in_bbox_32 = face_detect_results.as_numpy("bbox_32")
        in_kps_8 = face_detect_results.as_numpy("kps_8")
        in_kps_16 = face_detect_results.as_numpy("kps_16")
        in_kps_32 = face_detect_results.as_numpy("kps_32")
        det_out = []
        landmark_out=[]
        for i in range(det_scale.shape[0]):
            net_outputs = [in_score_8[i], in_score_16[i], in_score_32[i],
                        in_bbox_8[i],  in_bbox_16[i],  in_bbox_32[i],
                        in_kps_8[i],   in_kps_16[i],   in_kps_32[i]]
            det, kps = self.FaceDetectProprocess.postprocess(det_scale[i], net_outputs)
            if len(det) != 0 and len(kps) != 0:
                det = np.asarray(det)
                kps = np.asarray(kps).reshape(-1, 10)
                bbox = det[:, 0:4]
                score = det[:, 4].reshape(bbox.shape[0], -1)
                classId = np.zeros([bbox.shape[0], 1])
                batch_id =  i * np.ones([bbox.shape[0], 1])
                dets = np.hstack([bbox, classId, score, batch_id]).astype(np.float32)
                det_out.extend(dets)
                landmark_out.extend(kps.astype(np.float32))
            else:
                det_out.extend(np.ones((1, 7)) * -1)
                landmark_out.extend(np.ones((1, 10)) * -1)
        max_id = 0
        max_area = 0
        for i in range(len(det_out)):
            cur_area = det_out[i][2]*det_out[i][3]
            if cur_area > max_area:
                max_area = cur_area
                max_id = i
        max_det_out_result = det_out[max_id]
        #logger.info(max_det_out_result)
        max_landmar_out = landmark_out[max_id]
        max_det_out_result = np.expand_dims(max_det_out_result, axis=0)
        max_landmar_out = np.expand_dims(max_landmar_out, axis=0)
        # det_out_results = np.stack(det_out)
        # landmark_out_results = np.stack(landmark_out)
        return max_det_out_result, max_landmar_out
                
    # 返回值封装
    def update_recog_final_result(self, feature=[], boxes = []):

        final_result = {}

        final_result.update({"dim": 512})
        final_result.update({"feature": feature})
        final_result.update({"boxes": boxes})

        #logger.info(f'Reqid: {self.reqid}')

        return final_result, 'none'

        # 返回值封装
    def update_detect_final_result(self, features=[], boxes=[]):

        final_results = []

        for feature, box in zip(features, boxes):
            final_results.append({"dim": 512, "feature": feature, "box": box})

        #logger.info(f'Reqid: {self.reqid}')

        return final_results, 'none'

    def _batch(self, iterable, n=1):
        l = len(iterable)
        for ndx in range(0, l, n):
            yield iterable[ndx: min(ndx + n, l)]

    def run(self, img_arr, img_mat, action, boxes = None, landmarks = None):
    
        #  人脸收索
        if action == 'FaceRecog':
            # 对相应boxes和landmarks乘以相应wh
            process_boxes = []
            process_landmarks = []
            height, width = img_mat.shape[:2]
            for i in range(len(boxes)):
                x1 = boxes[i][0]*width
                y1 = boxes[i][1]*height
                w1 = boxes[i][2]*width
                h1 = boxes[i][3]*height
                process_boxes.append([x1, y1, w1, h1, 0, 0.8, 0])
                landmark_x1 = landmarks[i][0][0]*width
                landmark_y1 = landmarks[i][0][1]*height
                landmark_x2 = landmarks[i][1][0]*width
                landmark_y2 = landmarks[i][1][1]*height
                landmark_x3 = landmarks[i][2][0]*width
                landmark_y3 = landmarks[i][2][1]*height
                landmark_x4 = landmarks[i][3][0]*width
                landmark_y4 = landmarks[i][3][1]*height
                landmark_x5 = landmarks[i][4][0]*width
                landmark_y5 = landmarks[i][4][1]*height
                process_landmarks.append([landmark_x1, landmark_y1, 
                                        landmark_x2, landmark_y2, landmark_x3, landmark_y3, 
                                        landmark_x4, landmark_y4, landmark_x5, landmark_y5])
            # 人脸检测 模型推理
            #process_boxes = np.expand_dims(process_boxes, axis = 0)
            #process_landmarks = np.expand_dims(process_landmarks, axis = 0)
            process_boxes = np.array(process_boxes)
            process_landmarks = np.array(process_landmarks)
            if (np.array(boxes) == -1).all():
                raise ApiServerException(HTTPStatus.NOCHECKFACE.value,
                                        HTTPStatus.NOCHECKFACE.phrase, 
                                         HTTPStatus.NOCHECKFACE.description)
            # 人脸映射 图像转换
            corp_imgs, iboxs = self.FaceCropAlign_Client.execute(img_mat, process_boxes, process_landmarks)
            if (iboxs == -1).all():
                raise ApiServerException(HTTPStatus.NOCHECKFACE.value,
                                        HTTPStatus.NOCHECKFACE.phrase, 
                                         HTTPStatus.NOCHECKFACE.description)                
            # 人脸特征 模型推理
            # padding_input_images = []
            # for i in range(corp_imgs.shape[0]):
            #     input_img = np.transpose(corp_imgs[i], (1, 2, 0))
            #     resize_img_np = cv2.resize(input_img, (112, 112), interpolation=cv2.INTER_LINEAR)
            #     resize_img_np = (resize_img_np - self.mean) / self.std
            #     resize_img_np = np.transpose(resize_img_np, (2, 0, 1))
            #     padding_input_images.append(resize_img_np)
            # input_images = np.stack(padding_input_images)
            corp_imgs = np.float32(corp_imgs)
            features = []
            for batch_paded_images in self._batch(corp_imgs, arcface_configs["max_batch_size"]):
                result = feature_client.run(batch_paded_images)
                batch_result = result.get_result()
                features.append(batch_result.as_numpy('output'))
            features = np.concatenate(features, axis=0)
            return self.update_recog_final_result(features.tolist(), iboxs.tolist())
    
        # 人脸入库
        if action == 'FaceDetect':
            # 人脸检测 模型推理
            det_img, det_scale= self.preprocess_face_detect(img_mat)
            input_det_img = np.expand_dims(det_img, axis=0)
            #logger.info(input_det_img.shape)
            #logger.info("1--===")
            #logger.info(id(detect_client))
            face_detect_results = detect_client.run(input_det_img)
            get_face_detect_results = face_detect_results.get_result()
            max_det_out_result, max_landmar_out = self.proprocess_face_detect(det_scale, get_face_detect_results)
            if (max_det_out_result == -1).all():
                raise ApiServerException(HTTPStatus.NOCHECKFACE.value,
                                        HTTPStatus.NOCHECKFACE.phrase, 
                                         HTTPStatus.NOCHECKFACE.description)
            # 人脸映射 图像转换
            corp_img, _ = self.FaceCropAlign_Client.execute(img_mat, max_det_out_result, max_landmar_out)
            # 人脸特征 模型推理
            # corp_img = np.squeeze(corp_img, axis = 0)
            #_ ,_ , img_h, img_w = corp_img.shape
            img_h, img_w, _ = img_mat.shape
            boxes = [[0, 0, img_w, img_h]]
            # corp_img = np.transpose(corp_img, (1, 2, 0))
            # resize_img_np = cv2.resize(corp_img, (112, 112), interpolation=cv2.INTER_LINEAR)
            # resize_img_np = (resize_img_np - self.mean) / self.std
            # resize_img_np = np.transpose(resize_img_np, (2, 0, 1))
            # input_data = np.expand_dims(resize_img_np, axis=0)
            input_data = np.float32(corp_img)
            # logger.info(input_data.shape)
            #logger.info("2--==")
            feature_results = feature_client.run(input_data)
            face_detect_results = feature_results.get_result()
            return self.update_detect_final_result(face_detect_results.as_numpy('output').tolist(), boxes)


class FileFormatValidation:
    @staticmethod
    def validate(file: bytes, mime_matches):
        if file is None or len(file) <= 128:
            return False

        info = fleep.get(file[:128])
        for mime in mime_matches:
            if info.mime_matches(mime):
                return True
        return False

    @staticmethod
    def convert_to_png(self):
        im = Image.open(BytesIO(self.file))
        byte_io = BytesIO()
        im.save(byte_io, 'PNG')
        self.cleaned_image = byte_io.getvalue()


def check_params(params):
    # Action and ImageData
    #Action = params.get('Action', None)
    #ImageData = params.get('ImageData', None)
    ImageData = params["ImageData"]

    img_byte = base64.urlsafe_b64decode(ImageData)
    img_arr = np.frombuffer(img_byte, np.uint8)
    img_mat = cv2.cvtColor(cv2.imdecode(
            img_arr, cv2.IMREAD_COLOR), cv2.COLOR_BGR2RGB)
    #height, width = img_mat.shape[:2]
    return img_arr, img_mat


def run(params, box = None, landmark = None):
    # 参数校验及初始化
    time_0 = time.time()
    img_arr, img_mat = check_params(params)
    client = FaceDetectClient(process_configs)

    # 推理执行
    time_1 = time.time()
    results, _ = client.run(img_arr, img_mat, params['Action'], box, landmark)
    time_2 = time.time()

    logger.info(
        f'Reqid: check params: {(time_1 - time_0) * 1000} ms, infer time: {(time_2 - time_1) * 1000} ms')

    return results


if __name__ == '__main__':
    from icecream import ic
    logging.basicConfig(
        level=logging.INFO, format='%(asctime)s %(levelname)s %(filename)s:%(lineno)d - %(message)s')

    client = FaceDetectClient(process_configs)

    image_paths = glob.glob("../../test/*.jpg")

    for idx, image_path in enumerate(image_paths):

        with open(image_path, 'rb') as file:
            img_arr = np.frombuffer(file.read(), np.uint8)
        img_mat = cv2.cvtColor(cv2.imdecode(
            img_arr, cv2.IMREAD_COLOR), cv2.COLOR_BGR2RGB)

        results, _ = client.run(img_arr, img_mat)
        print(f'idx: {idx}, {image_path}')
        print(len(results['FaceFeatures']))
        # ic(idx, image_path, results)
