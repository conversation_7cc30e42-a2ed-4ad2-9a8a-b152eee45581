import os 
import json
import faiss 
import logging
import glob
import numpy as np 
from app.utils.http_utils import HTTPStatus
from app.utils.exception_utils import ApiServerException

logger = logging.getLogger(__name__)


class FaissBase(object):
    def __init__(self, config):
        self.config = config
        self.collection_group_name = []
        self._ndim = config.get('VECTOR_DIM', 512)
        self.cur_path = os.path.abspath(os.path.join(os.path.dirname(__file__),'data'))
        if not os.path.exists(self.cur_path):
            os.makedirs(self.cur_path)
        self.index_dict = {}

    def has_collection(self, collection_name):
        collection_path = os.path.join(self.cur_path, collection_name)
        if os.path.exists(collection_path):
            return True
        return False

    def has_partition(self, collection_name, part_name):
        cur_collection_name = os.path.join(self.cur_path, collection_name)
        if os.path.isfile(cur_collection_name + '/' + collection_name + '_' + part_name + '.index'):
            return True
        return False

    def create_collection(self, collection_name):
        try:
            if not self.has_collection(collection_name):
                logger.info(f"cur_path:{self.cur_path}")
                os.mkdir(os.path.join(self.cur_path, collection_name))
                logger.info(f"Create faiss collection dirname: {collection_name}")
            else:
                return collection_name
            return collection_name
        except Exception as ex:
            raise ApiServerException(HTTPStatus.MILVUSERR.value, 
                                     HTTPStatus.MILVUSERR.phrase, 
                                     HTTPStatus.MILVUSERR.description)
    def set_collection(self, collection_name, part_name):
        if isinstance(part_name, list):
            if len(part_name) > 0:
                part_name = part_name[0]
        elif isinstance(part_name, str):
            part_name = part_name
        else:
            return "not_list_and_str"
        try:
            if self.has_collection(collection_name):
                logger.info(f"exist collection name:{collection_name}")
            else:
                logger.info(f"There is no collection named:{collection_name}")
                return "not_exist_collection_dir"
            if part_name:
                if self.has_partition(collection_name, part_name):
                    pass
                else:
                    logger.info(f"There is no part_name :{part_name}")
                    return "not_exist_part_name"
        except Exception as e:
            logger.error(f"Failed to load data to faiss: {e}")
            raise ApiServerException(HTTPStatus.MILVUSERR.value, 
                                     HTTPStatus.MILVUSERR.phrase, 
                                     HTTPStatus.MILVUSERR.description)

    def create_collection_part(self, collection_name, part_name):
        tag = self.set_collection(collection_name, part_name)
        logger.info(f"tag:{tag}")
        if tag == "not_exist_collection_dir":
            raise ApiServerException(HTTPStatus.MILVUSERR.value, 
                                     HTTPStatus.MILVUSERR.phrase, 
                                     "{}不存在".format(collection_name))
        elif tag == "not_exist_part_name":
            cur_collection_name_path = os.path.join(self.cur_path, collection_name)
            #input_zeros = np.zeros([self._ndim])
            #idx = np.array([1.1])
            index = faiss.IndexFlatL2(self._ndim)
            #index = faiss.IndexIDMap(index)
            #index.add_with_ids(input_zeros,idx)
            faiss.write_index(index, cur_collection_name_path + "/" + collection_name + "_" + part_name + ".index")
        else:
            return "exist"
        return "success"
    
    # def rename_part_name(self, collection_name, old_part_name, new_part_name):
    #     tag = self.set_collection(collection_name, old_part_name)
    #     if tag == "not_exist_collection_dir":
    #         raise ApiServerException(HTTPStatus.MILVUSERR.value, 
    #                                  HTTPStatus.MILVUSERR.phrase, 
    #                                  "{}不存在".format(collection_name))
    #     elif tag == "not_exist_part_name":
    #         raise ApiServerException(HTTPStatus.MILVUSERR.value, 
    #                                  HTTPStatus.MILVUSERR.phrase, 
    #                                  "{}不存在".format(old_part_name))
    #     if self.has_partition(collection_name, new_part_name):
    #         raise ApiServerException(HTTPStatus.MILVUSERR.value, 
    #                                  HTTPStatus.MILVUSERR.phrase, 
    #                                  "{}存在,不能重命名".format(new_part_name))
    #     cur_collection_name_path = os.path.join(self.cur_path, collection_name)
    #     old_index_name = cur_collection_name_path + "/" + collection_name + "_" + old_part_name + ".index"
    #     new_index_name = cur_collection_name_path + "/" + collection_name + "_" + new_part_name + ".index"
    #     os.rename(old_index_name, new_index_name)
        
    def insert(self, collection_name, part_name, face_feature, face_id):
        # Batch insert vectors to faiss collection
        tag = self.set_collection(collection_name, part_name)
        if tag == "not_exist_collection_dir":
            raise ApiServerException(HTTPStatus.MILVUSERR.value, 
                                     HTTPStatus.MILVUSERR.phrase, 
                                     "{}不存在".format(collection_name))
        elif tag == "not_exist_part_name":
            raise ApiServerException(HTTPStatus.MILVUSERR.value, 
                                     HTTPStatus.MILVUSERR.phrase, 
                                     "{}不存在".format(part_name))
        face_feature = np.array(face_feature)
        face_feature = np.expand_dims(face_feature, axis=0)
        #logger.info(f"id:{face_id}")
        #logger.info(face_feature)
        face_id = np.array(face_id)
        # logger.info(face_id)
        index_name = collection_name + "_" + part_name
        cur_collection_name_path = os.path.join(self.cur_path, collection_name)
        if index_name in self.index_dict:
            index = self.index_dict[index_name]
        else:
            index = faiss.read_index(cur_collection_name_path + "/" + collection_name + "_" + part_name + ".index")
        if index.ntotal == 0:
            index = faiss.IndexIDMap(index)
        index.add_with_ids(face_feature, face_id)
        self.index_dict[index_name] = index
        faiss.write_index(index, cur_collection_name_path + "/" + collection_name + "_" + part_name + ".index")

    def delete_group(self, collection_name, group):
        # Delete faiss's group of collection_name
        tag = self.set_collection(collection_name, group)
        if tag == "not_exist_collection_dir":
            raise ApiServerException(HTTPStatus.MILVUSERR.value, 
                                     HTTPStatus.MILVUSERR.phrase, 
                                     "{}不存在".format(collection_name))
        elif tag == "not_exist_part_name":
            raise ApiServerException(HTTPStatus.MILVUSERR.value, 
                                     HTTPStatus.MILVUSERR.phrase, 
                                     "{}不存在".format(group))

        cur_collection_name_path = os.path.join(self.cur_path, collection_name)
        if not os.path.isfile(cur_collection_name_path + '/' + collection_name + '_' + group + ".index"):
            raise ApiServerException(HTTPStatus.MILVUSERR.value, 
                                     HTTPStatus.MILVUSERR.phrase, 
                                     "{}不存在".format(group))            
        os.remove(cur_collection_name_path + "/" + collection_name + "_" + group + ".index")
        index_name = collection_name + "_" + group
        if index_name in self.index_dict:
            del self.index_dict[index_name]
        # self.collection.release()
        logger.info("Successfully drop collection!")
        return "ok"

    def delete_vector(self, collection_name, part_name, faiss_idxs):
        tag = self.set_collection(collection_name, part_name)
        if tag == "not_exist_collection_dir":
            raise ApiServerException(HTTPStatus.MILVUSERR.value, 
                                     HTTPStatus.MILVUSERR.phrase, 
                                     "{}不存在".format(collection_name))
        elif tag == "not_exist_part_name":
            raise ApiServerException(HTTPStatus.MILVUSERR.value, 
                                     HTTPStatus.MILVUSERR.phrase, 
                                     "{}不存在".format(part_name))
        index_name = collection_name + "_" + part_name
        cur_collection_name_path = os.path.join(self.cur_path, collection_name)
        if index_name in self.index_dict:
            index = self.index_dict[index_name]
        else:
            index = faiss.read_index(cur_collection_name_path + "/" + collection_name + "_" + part_name + ".index")
        if isinstance(faiss_idxs, list):
            for idx in faiss_idxs:
                index.remove_ids(np.array([idx]))
        else:
            index.remove_ids(np.array([faiss_idxs]))
        if index.ntotal == 0:
            os.remove(cur_collection_name_path + '/' + collection_name + '_' + part_name + ".index")
            index = faiss.IndexFlatL2(self._ndim)
        self.index_dict[index_name] = index
        faiss.write_index(index, cur_collection_name_path + "/" + collection_name + "_" + part_name + ".index")

    def search_vectors(self, collection_name, group, vectors, top_k, start_time=None, end_time=None, part_name=None):
        tag = self.set_collection(collection_name, group)
        if isinstance(group, list):
            all_distances = []
            all_idxs = []
            select_topk_distances = []
            select_topk_idxs = []
            if tag == "not_exist_collection_dir":
                raise ApiServerException(HTTPStatus.MILVUSERR.value, 
                                        HTTPStatus.MILVUSERR.phrase,
                                        "{}不存在".format(collection_name))
            elif tag == "not_exist_part_name":
                raise ApiServerException(HTTPStatus.MILVUSERR.value,
                                        HTTPStatus.MILVUSERR.phrase,
                                        "{}不存在".format(group))
            vectors = np.array(vectors)
            vectors = np.expand_dims(vectors,axis=0)
            for i in range(len(group)):
                group_name = group[i]
                if tag == "not_exist_part_name":
                    raise ApiServerException(HTTPStatus.MILVUSERR.value,
                                            HTTPStatus.MILVUSERR.phrase,
                                            "{}不存在".format(group_name))
                index_name = collection_name + "_" + group_name
                cur_collection_name_path = os.path.join(self.cur_path, collection_name)
                if index_name in self.index_dict:
                    index = self.index_dict[index_name]
                else:
                    index = faiss.read_index(cur_collection_name_path + "/" + collection_name + "_" + group_name + ".index")
                distances, idxs = index.search(vectors, top_k)
                #logger.info(distances)
                #logger.info(idxs)
                all_distances.extend(distances[0])
                all_idxs.extend(idxs[0])
            zips = zip(all_idxs,all_distances)
            sorted_zips = sorted(zips, key=lambda x:x[1], reverse = False)
            sorted_zips_topk = sorted_zips[:top_k]
            for i in range(len(sorted_zips_topk)):
                select_topk_idxs.append(sorted_zips_topk[i][0])
                select_topk_distances.append(sorted_zips_topk[i][1])
            return [select_topk_distances], [select_topk_idxs]
        elif isinstance(group, str):
            group = group        
            if tag == "not_exist_collection_dir":
                raise ApiServerException(HTTPStatus.MILVUSERR.value, 
                                         HTTPStatus.MILVUSERR.phrase, 
                                         "{}不存在".format(collection_name))
            elif tag == "not_exist_part_name":
                raise ApiServerException(HTTPStatus.MILVUSERR.value, 
                                         HTTPStatus.MILVUSERR.phrase, 
                                         "{}不存在".format(group))
            index_name = collection_name + "_" + group
            cur_collection_name_path = os.path.join(self.cur_path, collection_name)
            if index_name in self.index_dict:
                index = self.index_dict[index_name]
            else:
                index = faiss.read_index(cur_collection_name_path + "/" + collection_name + "_" + group + ".index")
            vectors = np.array(vectors)
            vectors = np.expand_dims(vectors,axis=0)
            distances, idxs = index.search(vectors, top_k)
            return distances, idxs

    def count(self, collection_name, group):
        tag = self.set_collection(collection_name, group)
        if tag == "not_exist_collection_dir":
            raise ApiServerException(HTTPStatus.MILVUSERR.value, 
                                     HTTPStatus.MILVUSERR.phrase, 
                                     "{}不存在".format(collection_name))
        elif tag == "not_exist_part_name":
            raise ApiServerException(HTTPStatus.MILVUSERR.value, 
                                     HTTPStatus.MILVUSERR.phrase, 
                                     "{}不存在".format(group))
        cur_collection_name_path = os.path.join(self.cur_path, collection_name)
        index_paths = glob.glob(cur_collection_name_path + "/" + "*.index")
        num = 0 
        for index_path in index_paths:
            index = faiss.read_index(index_path)
            cur_num = index.ntotal
            num = num + cur_num
        return num

























