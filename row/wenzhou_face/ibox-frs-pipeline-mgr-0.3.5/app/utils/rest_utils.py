# -*- coding:utf-8 -*-
"""
提供rest接口通用函数的模块
"""
from app.utils.http_utils import HTTPStatus


def make_response(data=None, status=HTTPStatus.SUCCESS):
    if data is None:
        data = {}
    return {
        "code": status.value,
        "result": data,
        "message": status.phrase
    }


def make_exception_response(code, message, details):
    return {
        "code": code,
        "message": message,
        "details": details
    }
