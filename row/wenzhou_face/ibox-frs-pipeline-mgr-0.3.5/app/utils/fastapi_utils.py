# -*- encoding: utf-8 -*-

from pydantic import BaseModel, Field

from app.utils.http_utils import HTTPStatus


class SuccessResult(BaseModel):
    pass


class ApiServerBaseResponse(BaseModel):
    code: int = Field(HTTPStatus.SUCCESS.value, description="状态码")


class ApiServerFailedResponse(ApiServerBaseResponse):
    code: int = Field(HTTPStatus.INTERNAL_SERVER_ERROR.value, description="状态码")
    message: str = Field(HTTPStatus.INTERNAL_SERVER_ERROR.phrase, description="错误信息")
    details: str = Field(HTTPStatus.INTERNAL_SERVER_ERROR.description, description="详细的错误信息")
