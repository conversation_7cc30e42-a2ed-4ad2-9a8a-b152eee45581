# -*- coding:utf-8 -*-
"""
提供http请求通用工具的模块
"""
import logging
from enum import IntEnum

logger = logging.getLogger(__name__)


class HTTPStatus(IntEnum):
    """HTTP status codes and reason phrases
    Status codes from the following RFCs are all observed:
        * RFC 7231: Hypertext Transfer Protocol (HTTP/1.1), obsoletes 2616
        * RFC 6585: Additional HTTP Status Codes
        * RFC 3229: Delta encoding in HTTP
        * RFC 4918: HTTP Extensions for WebDAV, obsoletes 2518
        * RFC 5842: Binding Extensions to WebDAV
        * RFC 7238: Permanent Redirect
        * RFC 2295: Transparent Content Negotiation in HTTP
        * RFC 2774: An HTTP Extension Framework
    """
    def __new__(cls, value, phrase, description=''):
        obj = int.__new__(cls, value)
        obj._value_ = value

        obj.phrase = phrase
        obj.description = description
        return obj

    # informational
    SUCCESS = 0, 'OK', 'Success'
    CONTINUE = 100, 'Continue', 'Request received, please continue'
    SWITCHING_PROTOCOLS = (101, 'Switching Protocols', 'Switching to new protocol; obey Upgrade header')
    PROCESSING = 102, 'Processing'

    # success
    OK = 200, 'OK', 'Request fulfilled, document follows'
    CREATED = 201, 'Created', 'Document created, URL follows'
    ACCEPTED = (202, 'Accepted', 'Request accepted, processing continues off-line')
    NON_AUTHORITATIVE_INFORMATION = (203, 'Non-Authoritative Information', 'Request fulfilled from cache')
    NO_CONTENT = 204, 'No Content', 'Request fulfilled, nothing follows'
    RESET_CONTENT = 205, 'Reset Content', 'Clear input form for further input'
    PARTIAL_CONTENT = 206, 'Partial Content', 'Partial content follows'
    MULTI_STATUS = 207, 'Multi-Status'
    ALREADY_REPORTED = 208, 'Already Reported'
    IM_USED = 226, 'IM Used'

    # redirection
    MULTIPLE_CHOICES = (300, 'Multiple Choices', 'Object has several resources -- see URI list')
    MOVED_PERMANENTLY = (301, 'Moved Permanently', 'Object moved permanently -- see URI list')
    FOUND = 302, 'Found', 'Object moved temporarily -- see URI list'
    SEE_OTHER = 303, 'See Other', 'Object moved -- see Method and URL list'
    NOT_MODIFIED = (304, 'Not Modified', 'Document has not changed since given time')
    USE_PROXY = (305, 'Use Proxy', 'You must use proxy specified in Location to access this resource')
    TEMPORARY_REDIRECT = (307, 'Temporary Redirect', 'Object moved temporarily -- see URI list')
    PERMANENT_REDIRECT = (308, 'Permanent Redirect', 'Object moved temporarily -- see URI list')

    # client error
    BAD_REQUEST = (400, 'Bad Request', 'Bad request syntax or unsupported method')
    UNAUTHORIZED = (401, 'Unauthorized', 'No permission -- see authorization schemes')
    PAYMENT_REQUIRED = (402, 'Payment Required', 'No payment -- see charging schemes')
    FORBIDDEN = (403, 'Forbidden', 'Request forbidden -- authorization will not help')
    NOT_FOUND = (404, 'Not Found', 'Nothing matches the given URI')
    METHOD_NOT_ALLOWED = (405, 'Method Not Allowed', 'Specified method is invalid for this resource')
    NOT_ACCEPTABLE = (406, 'Not Acceptable', 'URI not available in preferred format')
    PROXY_AUTHENTICATION_REQUIRED = (407, 'Proxy Authentication Required',
                                     'You must authenticate with this proxy before proceeding')
    REQUEST_TIMEOUT = (408, 'Request Timeout', 'Request timed out; try again later')
    CONFLICT = 409, 'Conflict', 'Request conflict'
    GONE = (410, 'Gone', 'URI no longer exists and has been permanently removed')
    LENGTH_REQUIRED = (411, 'Length Required', 'Client must specify Content-Length')
    PRECONDITION_FAILED = (412, 'Precondition Failed', 'Precondition in headers is false')
    REQUEST_ENTITY_TOO_LARGE = (413, 'Request Entity Too Large', 'Entity is too large')
    REQUEST_URI_TOO_LONG = (414, 'Request-URI Too Long', 'URI is too long')
    UNSUPPORTED_MEDIA_TYPE = (415, 'Unsupported Media Type', 'Entity body in unsupported format')
    REQUESTED_RANGE_NOT_SATISFIABLE = (416, 'Requested Range Not Satisfiable', 'Cannot satisfy request range')
    EXPECTATION_FAILED = (417, 'Expectation Failed', 'Expect condition could not be satisfied')
    UNPROCESSABLE_ENTITY = 422, 'Unprocessable Entity'
    LOCKED = 423, 'Locked'
    FAILED_DEPENDENCY = 424, 'Failed Dependency'
    UPGRADE_REQUIRED = 426, 'Upgrade Required'
    PRECONDITION_REQUIRED = (428, 'Precondition Required', 'The origin server requires the request to be conditional')
    TOO_MANY_REQUESTS = (429, 'Too Many Requests',
                         'The user has sent too many requests in a given amount of time ("rate limiting")')
    REQUEST_HEADER_FIELDS_TOO_LARGE = (431, 'Request Header Fields Too Large',
                                       'The server is unwilling to process the request because its header fields are too large')

    # server errors
    INTERNAL_SERVER_ERROR = (500, 'Internal Server Error', 'Server got itself in trouble')
    NOT_IMPLEMENTED = (501, 'Not Implemented', 'Server does not support this operation')
    BAD_GATEWAY = (502, 'Bad Gateway', 'Invalid responses from another server/proxy')
    SERVICE_UNAVAILABLE = (503, 'Service Unavailable', 'The server cannot process the request due to a high load')
    GATEWAY_TIMEOUT = (504, 'Gateway Timeout', 'The gateway server did not receive a timely response')
    HTTP_VERSION_NOT_SUPPORTED = (505, 'HTTP Version Not Supported', 'Cannot fulfill request')
    VARIANT_ALSO_NEGOTIATES = 506, 'Variant Also Negotiates'
    INSUFFICIENT_STORAGE = 507, 'Insufficient Storage'
    LOOP_DETECTED = 508, 'Loop Detected'
    NOT_EXTENDED = 510, 'Not Extended'
    NETWORK_AUTHENTICATION_REQUIRED = (511, 'Network Authentication Required',
                                       'The client needs to authenticate to gain network access')

    # user defined
    # face-server 错误
    GETVECTORERR = (200001, "人脸特征提取错误", "请求获取人脸特征错误")
    NOCHECKFACE = (200002, "未检测到人脸", "未检测到人脸")
    MILVUSERR = (200003, "人脸底库操作异常", "人脸底库操作错误，底库不存在，请求创建的底库不符合要求")
    NOTFACEID = (200004, "此人脸不存在", "此人脸不存在")
    EXIST = (200005, "已存在", "请求创建的人脸底库不存在")
    KEYNAMEERR = (200006, "数据库不存在", "数据库不存在")
    SAVEIMAGEERR = (200007, "保存图片错误", "保存图片错误")
    CREATEERR = (200008, "人脸底库个数超过限制", "此app_key可以创建的底库已达上限")
    NOTEXISTENTITY = (200009, "实体不存在", "实体不存在")
    EXISTENTITY = (200010, "已存在", "实体已存在")

    # 鉴权错误码
    AUTHENERR = (300001, "鉴权认证错误", "app_key 或者token错误")
    AUTHENTIMEERR = (300002, "鉴权过期", "token已过期")
    APPKEYERR = (300003, "app_key已经被注册", "app_key已经被注册")
    # 参数错误码
    IMAGEERR = (400001, "请求体的字段类型错误", "请求体的字段类型错误")
    IMAGESIZEERR = (400002, "图片文件大小不符合要求", "该文件大小不符合要求,静态图片要求小于5M")
    IMAGEBASE64ERR = (400003, "base64数据处理异常", 
                      "客户端传递的base64格式在处理时发生异常, 可以尝试在本地转换base64是否有异常")
    BODYERR = (400004, "body内容为空", "客户端请求数据为空，没有包含内容")
    TYPEERR = (400005, "请求参数不完整", "必须的参数未传等")
    ACTIONERR = (400006, "action设置错误", "action设置错误,对应的请求操作填写有误")
    IMAGETYPEERR = (400007, "请求文件格式不合法", "仅支持 jpeg/png/jpg/bmp 格式")
    IMAGEURLERR = (400008, "通过链接获取图片错误", "可能链接有异常，或网络不通")
    BODYTYPEERR = (400009, "非json格式", "body内容需要符合json要求")
    IMAGESHAPEERR = (400010, "图片尺寸不符合要求", "分辨率长宽尺寸应不高于5000不低于32")
    TIMESTAMPERR = (400011, "时间戳格式不对", "时间戳位数不足10位或者不是正整数")
    INTERR = (400012, "参数格式不正确", "请传入正整数")

    # 请求方法错误码
    REQUESTMETHOD = (401000, "请求方法错误, 请使用POST请求", "客户端需要使用POST请求方式")
    # 请求接口不存在
    REQUESTNOTFOUND = (401001, "请求接口不存在", "请求路由错误")

    # 系统错误码
    SERVERERR = (500001, "服务接口异常,请联系管理员", "需要联系管理员处理")
    # 数据库错误码
    DBOPERATEERR = (500002, "操作数据库异常", "增删改查错误")
    # triton 错误码
    TRIONERR = (501001, "模型推理错误", "可能模型文件名错误，服务不通，格式错误等")
