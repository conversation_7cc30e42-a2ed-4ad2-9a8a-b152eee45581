# -*- encoding: utf-8 -*-
import fleep
import logging
import hashlib, time, random, datetime
import requests, base64, json
from app.utils.exception_utils import ApiServerException
from app.utils.http_utils import HTTPStatus
from app.bussiness.user import UserModule
from app.client.face_detect import run



logger = logging.getLogger(__name__)

def base64_filesize(b64):
    # base64的长度 * 3/4 是文件字节数，/1024 是kb
    length = len(b64)
    return int(length * 3 / 4 // 1024)


class FileFormatValidation:
    @staticmethod
    def validate(file: bytes, mime_matches):
        if file is None or len(file) <= 128:
            return False

        info = fleep.get(file[:128])
        for mime in mime_matches:
            if info.mime_matches(mime):
                return True
        return False


# 通过url获取图片的base64
def get_base64(url):
    try:
        r = requests.get(url, timeout=5)
    except Exception as ex:
        return None
    if r.status_code != 200:
        return None
    base_data = base64.encodebytes(r.content).decode()
    return base_data


def check_is_empty(data):
    if len(data) == 0:
        return True
    else:
        return False


def check_is_none(data):
    if data is None:
        return True
    else:
        return False


def check_is_not_none(data):
    if data is not None:
        return True
    else:
        return False


def check_is_not_str(data):
    if isinstance(data, str):
        return False
    else:
        return True


def check_is_not_int(data):
    if isinstance(data, int):
        return False
    else:
        return True


def check_is_not_dict(data):
    if isinstance(data, dict):
        return False
    else:
        return True


def check_request_body_type(data):
    if check_is_not_dict(data):
        raise ApiServerException(HTTPStatus.BODYTYPEERR.value, 
                                 HTTPStatus.BODYTYPEERR.phrase, 
                                 HTTPStatus.BODYTYPEERR.description)


def base64_to_bytes(base64_str):
    return base64.urlsafe_b64decode(base64_str)


def user_collection_authen(app_key, group):
    user_module = UserModule.get_instance()
    collection_name = user_module.get_group_name(app_key, group)
    if not collection_name:
        raise ApiServerException(HTTPStatus.KEYNAMEERR.value, 
                                 HTTPStatus.KEYNAMEERR.phrase, 
                                 HTTPStatus.KEYNAMEERR.phrase)
    else:
        return collection_name


# 通用图片获取接口
def get_image_by_url(pic_url):
    image_data = get_base64(pic_url)
    if image_data:
        return image_data
    else:
        return None


def check_add_face_params(params, app_key):
    image_data = params.get("ImageData", None)
    image_url = params.get("ImageUrl", None)
    group = params.get("DbName", None)
    entity_id = params.get("EntityId", None)
    extra_data = params.get("ExtraData", None)
    time_stamp = params.get("TimeStamp", int(time.time()))
    # 校验参数
    if check_is_none(image_data) and check_is_none(image_url):
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    if (check_is_not_none(image_url) and check_is_not_str(image_url)) or (check_is_not_none(image_data) and check_is_not_str(image_data)):
        raise ApiServerException(HTTPStatus.IMAGEERR.value, 
                                 HTTPStatus.IMAGEERR.phrase, 
                                 HTTPStatus.IMAGEERR.description)
    tag = None
    if check_is_not_none(image_url) and not check_is_not_str(image_url):
        if check_is_empty(image_url):
            pass
        else:
            tag = "url"
    if tag is None and check_is_not_none(image_data) and not check_is_not_str(image_data):
        if check_is_empty(image_data):
            raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                     HTTPStatus.TYPEERR.phrase, 
                                     HTTPStatus.TYPEERR.description)
    if tag is None and check_is_none(image_data):
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    if check_is_none(group) or check_is_none(entity_id):
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    if check_is_not_str(group) or check_is_not_str(entity_id):
        raise ApiServerException(HTTPStatus.IMAGEERR.value, 
                                 HTTPStatus.IMAGEERR.phrase, 
                                 HTTPStatus.IMAGEERR.description)
    if check_is_empty(group) or check_is_empty(entity_id):
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    if check_is_not_none(extra_data) and check_is_not_str(extra_data):
        raise ApiServerException(HTTPStatus.IMAGEERR.value, 
                                 HTTPStatus.IMAGEERR.phrase, 
                                 HTTPStatus.IMAGEERR.description)
    if check_is_not_none(time_stamp) and check_is_not_int(time_stamp):
        raise ApiServerException(HTTPStatus.IMAGEERR.value, 
                                 HTTPStatus.IMAGEERR.phrase, 
                                 HTTPStatus.IMAGEERR.description)
    # 判断时间戳的位数
    if len(str(time_stamp)) != 10 or time_stamp <= 0:
        raise ApiServerException(HTTPStatus.TIMESTAMPERR.value, 
                                 HTTPStatus.TIMESTAMPERR.phrase, 
                                 HTTPStatus.TIMESTAMPERR.description)
    # 判断app_key是否有此group的权限
    collection_name = user_collection_authen(app_key, group)
    if tag == "url":
        image = get_image_by_url(image_url)
        if not image:
            logger.error("通过链接获取图片失败")
            raise ApiServerException(HTTPStatus.IMAGEURLERR.value, 
                                     HTTPStatus.IMAGEURLERR.phrase, 
                                     HTTPStatus.IMAGEURLERR.description)
    else:
        image = image_data
    # 校验图片数据
    if not image:
        # no image
        raise ApiServerException(HTTPStatus.IMAGEERR.value, 
                                 HTTPStatus.IMAGEERR.phrase, 
                                 HTTPStatus.IMAGEERR.description)
    try:
        img = base64_to_bytes(image)
    except Exception as e:
        logger.error(str(e))
        raise ApiServerException(HTTPStatus.IMAGEBASE64ERR.value, 
                                 HTTPStatus.IMAGEBASE64ERR.phrase, 
                                 HTTPStatus.IMAGEBASE64ERR.description)
    if not FileFormatValidation.validate(img, ["image/jpeg", "image/png", "image/bmp"]):
        logger.error("图片类型错误")
        raise ApiServerException(HTTPStatus.IMAGETYPEERR.value, 
                                 HTTPStatus.IMAGETYPEERR.phrase, 
                                 HTTPStatus.IMAGETYPEERR.description)
    if base64_filesize(image) > 5120:
        # size error
        logger.error("图片大小错误")
        raise ApiServerException(HTTPStatus.IMAGESIZEERR.value, 
                                 HTTPStatus.IMAGESIZEERR.phrase, 
                                 HTTPStatus.IMAGESIZEERR.description)
    return image, collection_name


def check_params(params, app_key):
    image_data = params.get("ImageData", None)
    image_url = params.get("ImageUrl", None)
    group = params.get("DbName", None)
    top_k = params.get("TopK", 1)
    start_time = params.get("StartTime", None)
    end_time = params.get("EndTime", None)
    # 参数完整性校验
    if check_is_none(group):
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    if not isinstance(group, list):
        raise ApiServerException(HTTPStatus.IMAGEERR.value, 
                                 HTTPStatus.IMAGEERR.phrase, 
                                 HTTPStatus.IMAGEERR.description)
    if len(group) == 0:
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    if check_is_none(image_data) and check_is_none(image_url):
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    if (check_is_not_none(image_url) and check_is_not_str(image_url)) or (check_is_not_none(image_data) and check_is_not_str(image_data)):
        raise ApiServerException(HTTPStatus.IMAGEERR.value, 
                                 HTTPStatus.IMAGEERR.phrase, 
                                 HTTPStatus.IMAGEERR.description)
    if check_is_not_none(top_k) and check_is_not_int(top_k):
        raise ApiServerException(HTTPStatus.IMAGEERR.value, 
                                 HTTPStatus.IMAGEERR.phrase, 
                                 HTTPStatus.IMAGEERR.description)
    if check_is_not_none(start_time) and check_is_not_int(start_time):
        raise ApiServerException(HTTPStatus.IMAGEERR.value, 
                                 HTTPStatus.IMAGEERR.phrase, 
                                 HTTPStatus.IMAGEERR.description)
    if check_is_not_none(end_time) and check_is_not_int(end_time):
        raise ApiServerException(HTTPStatus.IMAGEERR.value, 
                                 HTTPStatus.IMAGEERR.phrase, 
                                 HTTPStatus.IMAGEERR.description)
    if check_is_not_none(start_time) and (len(str(start_time)) != 10 or start_time <= 0):
        raise ApiServerException(HTTPStatus.TIMESTAMPERR.value, 
                                 HTTPStatus.TIMESTAMPERR.phrase, 
                                 HTTPStatus.TIMESTAMPERR.description)
    if check_is_not_none(end_time) and (len(str(end_time)) != 10 or end_time <= 0):
        raise ApiServerException(HTTPStatus.TIMESTAMPERR.value, 
                                 HTTPStatus.TIMESTAMPERR.phrase, 
                                 HTTPStatus.TIMESTAMPERR.description)
    if top_k <= 0:
        raise ApiServerException(HTTPStatus.INTERR.value, 
                                 HTTPStatus.INTERR.phrase, 
                                 HTTPStatus.INTERR.description)
    tag = None
    if check_is_not_none(image_url) and not check_is_not_str(image_url):
        if check_is_empty(image_url):
            pass
        else:
            tag = "url"
    if tag is None and check_is_not_none(image_data) and not check_is_not_str(image_data):
        if check_is_empty(image_data):
            raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                     HTTPStatus.TYPEERR.phrase, 
                                     HTTPStatus.TYPEERR.description)
    if tag is None and check_is_none(image_data):
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    # 判断app_key是否有此group的权限
    collection_name = user_collection_authen(app_key, group[0])
    if tag == "url":
        image = get_image_by_url(image_url)
        if not image:
            logger.error("通过链接获取图片失败")
            raise ApiServerException(HTTPStatus.IMAGEURLERR.value, 
                                     HTTPStatus.IMAGEURLERR.phrase, 
                                     HTTPStatus.IMAGEURLERR.description)
    else:
        image = image_data
    # 校验图片数据
    if not image:
        # no image
        raise ApiServerException(HTTPStatus.IMAGEERR.value, 
                                 HTTPStatus.IMAGEERR.phrase, 
                                 HTTPStatus.IMAGEERR.description)
    try:
        img = base64_to_bytes(image)
    except Exception as e:
        logger.error(str(e))
        raise ApiServerException(HTTPStatus.IMAGEBASE64ERR.value, 
                                 HTTPStatus.IMAGEBASE64ERR.phrase, 
                                 HTTPStatus.IMAGEBASE64ERR.description)
    if not FileFormatValidation.validate(img, ["image/jpeg", "image/png", "image/bmp"]):
        logger.error("图片类型错误")
        raise ApiServerException(HTTPStatus.IMAGETYPEERR.value, 
                                 HTTPStatus.IMAGETYPEERR.phrase, 
                                 HTTPStatus.IMAGETYPEERR.description)
    if base64_filesize(image) > 5120:
        # size error
        logger.error("图片大小错误")
        raise ApiServerException(HTTPStatus.IMAGESIZEERR.value, 
                                 HTTPStatus.IMAGESIZEERR.phrase, 
                                 HTTPStatus.IMAGESIZEERR.description)
    return image, collection_name


def check_add_face_entity_params(params, app_key):
    db_name = params.get("DbName", None)
    entity_id = params.get("EntityId", None)
    labels = params.get("Labels", None)
    # 参数完整性校验
    if check_is_none(db_name) or check_is_none(entity_id):
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    if check_is_not_str(db_name) or check_is_not_str(entity_id):
        raise ApiServerException(HTTPStatus.IMAGEERR.value, 
                                 HTTPStatus.IMAGEERR.phrase, 
                                 HTTPStatus.IMAGEERR.description)
    if check_is_not_none(labels) and check_is_not_str(labels):
        raise ApiServerException(HTTPStatus.IMAGEERR.value, 
                                 HTTPStatus.IMAGEERR.phrase, 
                                 HTTPStatus.IMAGEERR.description)
    if check_is_empty(db_name) or check_is_empty(entity_id):
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    collection_name = user_collection_authen(app_key, db_name)
    return collection_name


def check_list_face_entities_params(params, app_key):
    db_name = params.get("DbName", None)
    page = params.get("Page", 1)
    page_size = params.get("PageSize", 10)
    order = params.get("Order", "asc")
    labels = params.get("Labels", None)
    entity_id_prefix = params.get("EntityIdPrefix", None)
    # 参数完整性校验
    if check_is_none(db_name):
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    if check_is_not_str(db_name) or check_is_not_str(order):
        raise ApiServerException(HTTPStatus.IMAGEERR.value, 
                                 HTTPStatus.IMAGEERR.phrase, 
                                 HTTPStatus.IMAGEERR.description)
    if (check_is_not_none(labels) and check_is_not_str(labels)) or (check_is_not_none(entity_id_prefix) and check_is_not_str(entity_id_prefix)):
        raise ApiServerException(HTTPStatus.IMAGEERR.value, 
                                 HTTPStatus.IMAGEERR.phrase, 
                                 HTTPStatus.IMAGEERR.description)
    if check_is_not_int(page) or check_is_not_int(page_size):
        raise ApiServerException(HTTPStatus.IMAGEERR.value, 
                                 HTTPStatus.IMAGEERR.phrase, 
                                 HTTPStatus.IMAGEERR.description)
    if page <= 0 or page_size <= 0:
        raise ApiServerException(HTTPStatus.INTERR.value, 
                                 HTTPStatus.INTERR.phrase, 
                                 HTTPStatus.INTERR.description)
    if check_is_empty(db_name):
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    collection_name = user_collection_authen(app_key, db_name)
    return collection_name


def check_update_face_entity(params, app_key):
    db_name = params.get("DbName", None)
    entity_id = params.get("EntityId", None)
    new_entity_id = params.get("NewEntityId", None)
    labels = params.get("Labels", None)
    if check_is_none(db_name) or check_is_none(entity_id) or check_is_none(new_entity_id):
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    if check_is_not_str(db_name) or check_is_not_str(entity_id) or check_is_not_str(new_entity_id):
        raise ApiServerException(HTTPStatus.IMAGEERR.value, 
                                 HTTPStatus.IMAGEERR.phrase, 
                                 HTTPStatus.IMAGEERR.description)
    if check_is_not_none(labels) and check_is_not_str(labels):
        raise ApiServerException(HTTPStatus.IMAGEERR.value, 
                                 HTTPStatus.IMAGEERR.phrase, 
                                 HTTPStatus.IMAGEERR.description)
    if check_is_empty(db_name) or check_is_empty(entity_id) or check_is_empty(new_entity_id):
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    collection_name = user_collection_authen(app_key, db_name)
    return collection_name


def check_update_face_db(params, app_key):
    db_name = params.get("DbName", None)
    new_db_name = params.get("DbName", None)
    if check_is_none(db_name) or check_is_none(new_db_name):
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    if check_is_not_str(db_name) or check_is_not_str(new_db_name):
        raise ApiServerException(HTTPStatus.IMAGEERR.value, 
                                 HTTPStatus.IMAGEERR.phrase, 
                                 HTTPStatus.IMAGEERR.description)
    if check_is_empty(db_name) or check_is_empty(new_db_name):
        raise ApiServerException(HTTPStatus.TYPEERR.value, 
                                 HTTPStatus.TYPEERR.phrase, 
                                 HTTPStatus.TYPEERR.description)
    collection_name = user_collection_authen(app_key, db_name)
    return collection_name


def data_md5(data):
    md5_obj = hashlib.md5()
    md5_obj.update(data)
    code = md5_obj.hexdigest()
    return str(code).lower()


def tid_maker():
    return '{0:%Y%m%d%H%M%S%f}'.format(datetime.datetime.now()) + ''.join(
        [str(random.randint(1, 10)) for i in range(5)])


def process(data, url, boxes = None, landmarks = None):
    data_dict = data
    try:
        results = run(data_dict, boxes, landmarks)
        if isinstance(results, dict):
            if len(results["feature"]) == 0:
                raise ApiServerException(HTTPStatus.NOCHECKFACE.value, 
                                        HTTPStatus.NOCHECKFACE.phrase, 
                                        HTTPStatus.NOCHECKFACE.description)
            else:
                return check_process_data(results)
        elif isinstance(results, list):
            if len(results) == 0:
                raise ApiServerException(HTTPStatus.NOCHECKFACE.value,
                                        HTTPStatus.NOCHECKFACE.phrase,
                                        HTTPStatus.NOCHECKFACE.description)
            else:
                return check_process_data(results)
        else:
            raise ApiServerException(HTTPStatus.GETVECTORERR.value, 
                                     HTTPStatus.GETVECTORERR.phrase, 
                                     HTTPStatus.GETVECTORERR.description)
    except Exception as e:
        logger.error(str(e))
        raise ApiServerException(HTTPStatus.GETVECTORERR.value,
                                 HTTPStatus.GETVECTORERR.phrase, 
                                 HTTPStatus.GETVECTORERR.description)                                            

# 处理人脸特征数据
def check_process_data(data):
    if isinstance(data, dict):
        return data["feature"], data["boxes"]
    victor = {"box": 0, "feature": []}
    for i in data:
        box = (i["box"][3] - i["box"][1]) * (i["box"][2] - i["box"][0])
        if box > victor["box"]:
            victor["box"] = box
            victor["feature"] = i["feature"]
    return victor["feature"]
