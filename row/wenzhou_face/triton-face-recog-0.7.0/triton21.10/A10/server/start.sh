#!/bin/bash

HTTP_PORT=7020
MODEL_DIR=$(dirname $(pwd))/models
DOCKER_IMAGE=harbor.ctyuncdn.cn/ai-service/ibox/l4t-tritonserver:r2.17-min
LOG_VERBOSE=1
LOG_DIR="/world/logs/"
NAME="trtis-face-recog-docker"

sudo nvidia-docker run \
	--name ${NAME} \
	--rm --shm-size=1g --ulimit memlock=-1 --ulimit stack=67108864 \
	--network=host \
	-v${MODEL_DIR}:/models \
	${DOCKER_IMAGE} \
	/opt/tritonserver/bin/tritonserver --model-repository=/models \
	--http-port=${HTTP_PORT} \
	--allow-grpc=false \
	--allow-metrics=false \
	--log-verbose=${LOG_VERBOSE}

#--log-verbose=${LOG_VERBOSE} >> $LOG_DIR${NAME}'.log'  2>&1 # 写入日志
# --gpus '"device=1"' # 不使用模型配置文件指定GPU
# -e NVIDIA_VISIBLE_DEVICES="1" \
# --rm # 退出时清除
# --restart=always # 重启
