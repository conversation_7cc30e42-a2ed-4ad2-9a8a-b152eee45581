#!/bin/bash

HTTP_PORT=7010
NAME=$(cat ../__NAME)
DOCKER_IMAGE=harbor.ctyuncdn.cn/ai-service/ibox/${NAME}
#DOCKER_IMAGE=************:8025/ai-service/ibox/${NAME}
DOCKER_VERSION=$(cat ../__VERSION)

sudo nvidia-docker run \
	--name ${NAME}-${DOCKER_VERSION} \
	--network=host \
  -e NVIDIA_VISIBLE_DEVICES="2" \
	--rm --shm-size=1g --ulimit memlock=-1 --ulimit stack=67108864 \
	${DOCKER_IMAGE}:${DOCKER_VERSION} \
	tritonserver --model-repository=/models \
	--http-port=${HTTP_PORT} \
	--allow-grpc=false \
	--allow-metrics=false \
	--log-verbose=1

# -e NVIDIA_VISIBLE_DEVICES=1 和 --gpus '"device=1"' 等同
# --cpus=4 \
# -m 8192m
