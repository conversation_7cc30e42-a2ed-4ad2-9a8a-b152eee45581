#!/bin/bash

NAME=$(cat __NAME)
DOCKER_IMAGE=harbor.ctyuncdn.cn/ai-service/ibox/${NAME}
DOCKER_VERSION=$(cat __VERSION)

sudo docker run -it --rm \
        --name ${NAME}-${DOCKER_VERSION} \
        -e NVIDIA_VISIBLE_DEVICES="0" \
        --network=host \
        --shm-size=1g --ulimit memlock=-1 --ulimit stack=67108864 \
        ${DOCKER_IMAGE}:${DOCKER_VERSION} \
        bash server.sh 7010 false

# -e NVIDIA_VISIBLE_DEVICES=0 和 --gpus '"device=0"' 等价
# --cpus=4 \
# -m 8192m \
# $1 http-port default 8000
# $2 log-verbose default true
