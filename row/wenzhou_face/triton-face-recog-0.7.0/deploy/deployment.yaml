---
## service
kind: Service
apiVersion: v1
metadata:
  name: {{DEPLOYMENT_NAME}}
  namespace: {{NAMESPACE}}
spec:
  selector:
    app: {{DEPLOYMENT_NAME}}
  ports:
    - protocol: TCP
      port: 80
      targetPort: {{SERVICE_TARGET_PORT}}

---
## deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: {{DEPLOYMENT_NAME}}
  name: {{DEPLOYMENT_NAME}}
  namespace: {{NAMESPACE}}
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: {{DEPLOYMENT_NAME}}
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: {{DEPLOYMENT_NAME}}
    spec:
      dnsConfig:
        options:
          - name: single-request-reopen
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: gpu
                    operator: In
                    values:
                      - enable
      containers:
        - image: {{imageUrl}}
          imagePullPolicy: IfNotPresent
          name: {{DEPLOYMENT_NAME}}
          resources:
            requests:
              cpu: "1"
              memory: 1Gi
              ideal.com/gpu: 1
              ideal.com/vcuda-core: 50
              ideal.com/vcuda-memory: 6000
            limits:
              cpu: "16"
              memory: 16Gi
              ideal.com/gpu: 1
              ideal.com/vcuda-core: 50
              ideal.com/vcuda-memory: 6000
          volumeMounts:
            - mountPath: /dev/shm
              name: cache-volume                 
          # 等分： 全额 / 四分之三 / 二等分 / 四等分 / 六等分
          # vcuda-core: 100 / 75 / 50 / 25 / 15
          # vcuda-memory: 12000 / 9000 / 6000 / 3000 / 2000
          livenessProbe:
            tcpSocket:
              port: {{SERVICE_TARGET_PORT}}
            initialDelaySeconds: 300
            timeoutSeconds: 1
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            tcpSocket:
              port: {{SERVICE_TARGET_PORT}}
            initialDelaySeconds: 5
            timeoutSeconds: 1
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 3
          ports:
          - containerPort: {{SERVICE_TARGET_PORT}}
      volumes:
      - emptyDir:
          medium: Memory
          sizeLimit: 4Gi
        name: cache-volume          
      restartPolicy: Always

