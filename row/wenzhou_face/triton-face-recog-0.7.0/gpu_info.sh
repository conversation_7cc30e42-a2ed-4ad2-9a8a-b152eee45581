gpu_T4="Tesla T4"
gpu_A10="A10"

# T4 显卡判断
result=$(echo `nvidia-smi -L | head -1` | grep "${gpu_T4}")
if [[ "$result" != "" ]]
then
    echo "[gpu info] exist T4 GPU" 
    mv triton21.06/T4/models models
else
    echo "[gpu info] not belong to T4 GPU"
fi

# A10 显卡判断
result=$(echo `nvidia-smi -L | head -1` | grep "${gpu_A10}")
if [[ "$result" != "" ]]
then
    echo "[gpu info] exist A10 GPU"
    mv triton21.06/A10/models models
else
    echo "[gpu info] not belong to A10 GPU"
fi
