#include "sac_custom_probe_impl.h"
#include "sacmsg_custom_meta.h"
#include "nvds_analytics_meta.h"
#include "nvbufsurface.h"
#include "nvdsmeta_schema.h"
#include "nvds_obj_encode.h"
#include "deepstream_app.h"
#include "deepstream_app_config_parser_ext.h"
#include "lrucache.hpp"
#include <experimental/filesystem>
#include <iostream>
#include <string>
#include <memory>
#include <vector>
#include <json-glib/json-glib.h>
#include <fstream>
#include <unordered_map>
#include <algorithm>
#include <cmath>
#include <queue>
#include "lrucache.hpp"
#include "gst-nvdssr.h"
#include "opencv2/imgproc/imgproc.hpp"
#include "opencv2/highgui/highgui.hpp"
#include <cuda.h>
#include <cuda_runtime.h>



typedef struct _sac_msg {
    std::vector<float> bbox;
    float score;
    int track_id;
    float track_score;
    std::string class_name;
} sac_msg;

extern SACProbeConfig sac_probe_config;
static std::unordered_map<std::string, std::pair<guint64, guint64>> roi_pts;
static std::unordered_map<std::string, guint64> ouput_pts;
static std::unordered_map<int, gint16> source_threshold_map;

static std::unordered_map<int, std::unordered_map<std::string, std::vector<float>>> rois;
static std::unordered_map<int, std::queue<std::pair<guint64, std::vector<std::string>>>> seq_pts_roi;
static std::unordered_map<int, std::unordered_map<std::string, guint64>> roi_appear_count;
static std::unordered_map<int, guint64> seq_count;
static std::unordered_map<int, std::unordered_map<std::string, guint64>> roi_ouput_pts;
static std::unordered_map<int, std::unordered_map<std::string, guint64>> roi_ouput_times;

std::string get_FileSuffix(std::string path){
    std::string suffix;
	for (int i = path.size() - 1; i > 0; i--){
		if (path[i] == '/'){
			suffix = path.substr(i + 1);
			return suffix;
		}
	}
    return suffix;
}

int get_source_id(const std::string& str) {
	char delim = '-';
	std::stringstream ss(str);
	std::string result;
	std::string item;
    // "line-crossing-stream-0"
	while (std::getline(ss, item, delim)) {
		if (!item.empty()) {
			result = item;
		}
	}
	return std::stoi(result);
} 
std::string get_roi_id(const std::string& str) {
	char delim = '-';
	std::stringstream ss(str);
	std::string result;
	std::string item;
	while (std::getline(ss, item, delim)) {
		if (!item.empty()) {
			result = item;
		}
	}
	return result;  // r0 r1
}

std::vector<std::string> stringSplit(const std::string& str) {
	char delim = '=';
	std::stringstream ss(str);
	std::vector<std::string> result;
	std::string item;
	while (std::getline(ss, item, delim)) {
		if (!item.empty()) {
			std::string new_result;
			for (auto r : item) {
				if (r != ' ') {
					new_result += r;
				}
			}
			result.emplace_back(new_result);
		}
	}
	return result;
}


std::vector<float> stringSplit_bbox(const std::string& str) {
	char delim = ';';
	std::stringstream ss(str);
	std::vector<float> result;
	std::string item;
	while (std::getline(ss, item, delim)) {
		if (!item.empty()) {
			result.emplace_back((float)std::stoi(item));
		}
	}
	return result;
}

float get_intersec_area(std::vector<float> big_box, std::vector<float> small_box) {
	// std::cout<<"big_box.size(): "<<big_box.size()<<std::endl;
	std::vector<cv::Point> contour_big;
	std::vector<cv::Point> contour_small; 
	std::vector<cv::Point> intersectionPolygon;
	for (int i = 0; i < big_box.size() / 2; i++) {
		cv::Point point;
		point.x = big_box[2 * i];
		point.y = big_box[2 * i + 1];
		contour_big.emplace_back(point);
	}
	std::vector<float> new_small_box; 
	new_small_box.emplace_back(small_box[0]); 
	new_small_box.emplace_back(small_box[1]);
	new_small_box.emplace_back(small_box[2]);
	new_small_box.emplace_back(small_box[1]);
	new_small_box.emplace_back(small_box[2]);
	new_small_box.emplace_back(small_box[3]);
	new_small_box.emplace_back(small_box[0]);
	new_small_box.emplace_back(small_box[3]);
	for (int i = 0; i < new_small_box.size() / 2; i++) {
		cv::Point point;
		point.x = new_small_box[2 * i];
		point.y = new_small_box[2 * i + 1];
		contour_small.emplace_back(point);
	}
	float intersectArea = cv::intersectConvexConvex(contour_big, contour_small, intersectionPolygon, true);
	float box_area = (small_box[3] - small_box[1]) * (small_box[2] - small_box[0]);
	float roi_sensitivity = intersectArea / box_area; 
	return roi_sensitivity;

}

/**
 * 计算obj_box区域与检测区域的交叠比例
 * @param roi
 * @param obj_box
 * @param roi_sensitivity
 * @return cur_sensitivity 如果不相交，返回0.0
 * **/
float get_roi_sensitivity(std::vector<float> roi, std::vector<float> obj_box, float roi_sensitivity) {
	float cur_sensitivity = get_intersec_area(roi, obj_box);
	if (cur_sensitivity >= roi_sensitivity) {
		return cur_sensitivity;
	}
	return 0.0;
}

/**
 * 检测目标在哪个区域中
 * @param cur_rois 每个区域的bbox
 * @param obj_box std::vector<float> 检测到物体的左上，宽高信息
 * @param roi_sensitivity
 * @return roi_name 不在指定的区域中，则返回"none"
 * **/
std::string in_which_roi(std::unordered_map<std::string, std::vector<float>> cur_rois, std::vector<float> obj_box, float roi_sensitivity) {
	std::string roi_name = "none";
	float max_sensitivity = 0.0;
	for (auto cur_roi : cur_rois) {
		std::vector<float> roi_box = cur_roi.second;
		float cur_sensitivity = get_roi_sensitivity(roi_box, obj_box, roi_sensitivity);
		if (cur_sensitivity > max_sensitivity) {
			max_sensitivity = cur_sensitivity;
			roi_name = cur_roi.first;
		}
	}
	return roi_name;
}

void get_config_info(std::unordered_map<int, gint16>& threshold_map, std::string dsanalytics_config_file_path)
{
	int last_source_id = -1;
	std::ifstream file(dsanalytics_config_file_path);
	if (file) { // 如果成功打开了文件
		std::string line;
		while (std::getline(file, line)) { // 逐行读取文本内容
			if (line[0] == '#')
				continue;
			std::cout << line << std::endl;
			if (line.find("overcrowding-stream") != std::string::npos) {
				last_source_id = get_source_id(line);
			}
			else if (line.find("object-threshold") != std::string::npos) {
				std::vector<std::string> r = stringSplit(line);
				int threshold = std::stoi(r[1]);  // 
                threshold_map[last_source_id] = threshold;
			}
		}
		file.close();
	}
	else {
		std::cout << "unable to open config file" << std::endl;
	}
}


std::unordered_map<int, std::unordered_map<std::string, std::vector<float>>> get_roi_box(std::string filename) {
	std::unordered_map<int, std::unordered_map<std::string, std::vector<float>>> result;
	int last_source_id = -1;
	std::ifstream file(filename);
	if (file) {
		std::string line;
		while (std::getline(file, line)) {
			if (line[0] == '#') continue;
			// std::cout << line << std::endl;
			if (line.find("overcrowding-stream") != std::string::npos) {
				last_source_id = get_source_id(line);
			}
			else if (line.find("roi-") != std::string::npos) {
				std::vector<std::string> r = stringSplit(line);
				std::string roi_name = get_roi_id(r[0]);
				std::vector<float> roi_bbox = stringSplit_bbox(r[1]);
				if (result.find(last_source_id) == result.end()) {
					std::unordered_map<std::string, std::vector<float>> id_info;
					id_info[roi_name] = roi_bbox;
					result[last_source_id] = id_info;
				}
				else {
					std::unordered_map<std::string, std::vector<float>> id_info = result[last_source_id];
					id_info[roi_name] = roi_bbox;
					result[last_source_id] = id_info;
				}
			}
		}
		file.close();
	}
	else {
		std::cout << "unable to open config file" << std::endl;
	}
	return result;
}

//录制视频
void smart_record_handle(
    NvDsSrcBin *src_bin,
    guint source_id,
    std::string &video_path)
{
    NvDsSRSessionId sessId = 0;
    NvDsSRStatus st;
    guint startTime = 10;
    guint duration = 10;
    gchar *filename;
    if (src_bin->config->smart_rec_duration >= 0){
        duration = src_bin->config->smart_rec_duration;
    }
    if (src_bin->config->smart_rec_start_time >= 0){
        startTime = src_bin->config->smart_rec_start_time;
    }
    std::cout<<"startTime: "<<startTime<<std::endl;
    std::cout<<"duration: "<<duration<<std::endl;
    if (src_bin->recordCtx && !src_bin->reconfiguring){
        NvDsSRContext *ctx = (NvDsSRContext *)src_bin->recordCtx;
        if (!ctx->recordOn){
            g_print("Recording started..\n");
            st = NvDsSRStart(ctx, &sessId, startTime, duration, NULL);
            g_object_get(G_OBJECT(ctx->filesink), "location", &filename, NULL);
            if (st != NVDSSR_STATUS_OK)
                g_printerr("Unable to start recording, status=%d, recordOn=%d, resetDone=%d\n", st, ctx->recordOn, ctx->resetDone);
        }
        else{
            // NvDsSRStop (ctx, 0);
            g_object_get(G_OBJECT(ctx->filesink), "location", &filename, NULL);
        }
        video_path = filename; // It will do copy
    }
    else{
        video_path = "";
    }
}

/**
 * a custom probe function to generate an event message and attach to the buffer
 *
 * @param user_data   a pointer used to pass AppCtx which is defined in deepstream_app.h
 *
 * @param buf   a GstBuffer struct used to get nvbuffer and picture meta
 *
 * @param batch_meta   used to get nvidia defined batch meta, frame meta, obj meta...
 *
 * @param index   an index representing the index of a source
 *
 * @return void
 */
extern "C"
void SACCustomProbeMessageSender(void *user_data, GstBuffer *buf,
                                 NvDsBatchMeta *batch_meta, guint index) {
    
    // int running_time_start = sac_probe_config.running_time_start;
    // int running_time_end = sac_probe_config.running_time_end;
    // time_t nowtime;
	// time(&nowtime); //获取1970年1月1日0点0分0秒到现在经过的秒数
	// tm* p = localtime(&nowtime); //将秒数转换为本地时间,年从1900算起,需要+1900,月为0-11,所以要+1
    
    // // std::cout<<"seconds: "<<seconds<<std::endl;
	// int cur_hour = p->tm_hour + 8;
	// int cur_min = p->tm_min;
	// int cur_sec = p->tm_sec;
    // // std::cout<<"cur_hour: "<<cur_hour<<" cur_min: "<<cur_min<<" cur_sec: "<<cur_sec<<std::endl;
    // if(running_time_start > running_time_end){
    //     std::cout<<"start time > end time"<<std::endl;
    //     return;
    // }
    // if((running_time_start >= 0 && cur_hour<running_time_start) || (running_time_end >= 0 && cur_hour>running_time_end)){
    //     return;
    // }
    auto appCtx = static_cast<AppCtx *>(user_data);
    NvDsSrcParentBin *bin = &appCtx->pipeline.multi_src_bin;
    auto streammux_width = appCtx->config.streammux_config.pipeline_width;
    auto streammux_height = appCtx->config.streammux_config.pipeline_height;

    int record = sac_probe_config.record;
    int image_encoding_quality = sac_probe_config.image_encoding_quality; 

    NvDsFrameMeta *frame_meta;
    NvDsObjectMeta *obj_meta;
    NvDsClassifierMeta *cls_meta;
    NvDsUserMeta *user_meta;
    NvDsMetaList *l_frame, *l_obj, *l_cls, *l_user;
    NvDsAnalyticsFrameMeta *anl_meta;
    NvDsInferTensorMeta *tensor_meta;
    NvDsLabelInfo *label_meta;

    static NvDsObjEncCtxHandle ctx_enc_handle = nvds_obj_enc_create_context();
    //nvds_obj_enc_destroy_context(ctx_enc_handle);

    GstMapInfo inmap = GST_MAP_INFO_INIT;
    NvBufSurface *ip_surf;

    if (!gst_buffer_map(buf, &inmap, GST_MAP_READ)) {
        std::cerr << "input buffer mapinfo failed!" << std::endl;
        return;
    }
    ip_surf = (NvBufSurface *) inmap.data;
    gst_buffer_unmap(buf, &inmap);

    if (source_threshold_map.empty()){
        std::string dsanalytics_config_file_path = appCtx->config.dsanalytics_config.config_file_path;
        get_config_info(source_threshold_map, dsanalytics_config_file_path);
    }

    static std::unordered_map<int, std::unordered_map<std::string, std::vector<float>>>  rois;
    std::unordered_map<std::string, std::vector<sac_msg>>  rois_vehicles;

    std::vector<gchar *> events_message;

    std::string common_store_path = "/data/";
    std::string image_store_path = common_store_path + "data/";
    std::experimental::filesystem::path temp_img_path(image_store_path);
    if(!std::experimental::filesystem::exists(temp_img_path)){
        std::experimental::filesystem::create_directories(temp_img_path);
    }
    std::string msg_local_store_path = common_store_path + "meta/";
    std::experimental::filesystem::path temp_msg_path(msg_local_store_path);
    if(!std::experimental::filesystem::exists(temp_msg_path)){
        std::experimental::filesystem::create_directories(temp_msg_path);
    }

    // json对象
    JsonNode *rootNode;  // json根节点
    JsonObject *rootObj; // 根对象
    JsonObject *taskObj; // 任务信息对象
    JsonObject *eventsObj; // 事件对象
    JsonObject *imageObj; // 图片对象
    JsonArray *objectArray; // 目标对象数组
    JsonObject *objectObj; // 目标对象
    JsonObject *boxObj; // box对象
    JsonArray *attrArray; // 目标属性数组
    JsonObject *attrObj; // 目标属性对象
    JsonObject *featureObj; // 特征向量对象
    JsonArray *featureArray; // 特征向量数组
    JsonObject *videoObj; // 图片对象

    for (l_frame = batch_meta->frame_meta_list; l_frame != nullptr; l_frame = l_frame->next) {
        frame_meta = (NvDsFrameMeta *) l_frame->data;
        guint source_id = frame_meta->source_id;  // 
        gint frame_num = frame_meta->frame_num;
        guint64 buf_pts = frame_meta->buf_pts;  // 该frame的时间戳
        guint frame_width = frame_meta->source_frame_width;
        guint frame_height = frame_meta->source_frame_height;
        std::string source_uri(appCtx->config.multi_source_config[source_id].uri);
        std::string camera_id = std::to_string(appCtx->config.multi_source_config[source_id].camera_id);

        std::string image_store_name =  std::to_string(source_id) + '_' + std::to_string(frame_num) + '_'
                                        + std::to_string(frame_width) + 'x' + std::to_string(frame_height) + '_'
                                        + std::to_string(frame_meta->ntp_timestamp) + ".png";
        std::string frame_name = image_store_path + image_store_name;

        if(frame_num==0)
            rois = get_roi_box(appCtx->config.dsanalytics_config.config_file_path);

        // init
        if (seq_pts_roi.find(source_id) == seq_pts_roi.end())
            seq_pts_roi[source_id] = { };
        if (roi_appear_count.find(source_id) == roi_appear_count.end())
            roi_appear_count[source_id] = { };
        if (seq_count.find(source_id) == seq_count.end())
            seq_count[source_id] = 0;
        if (roi_ouput_pts.find(source_id) == roi_ouput_pts.end())
            roi_ouput_pts[source_id] = { };
        if (roi_ouput_times.find(source_id) == roi_ouput_times.end())
            roi_ouput_times[source_id] = { };
        
        seq_count[source_id] = seq_count[source_id] + 1;
        std::vector<std::string> all_roi;  // 

        guint obj_index = 0;
        objectArray = json_array_new();

        for (l_user = frame_meta->frame_user_meta_list; l_user != nullptr; l_user = l_user->next)
        {
            user_meta = (NvDsUserMeta *) l_user->data;
            if(user_meta->base_meta.meta_type != NVDS_USER_FRAME_META_NVDSANALYTICS)
                continue;
            anl_meta = (NvDsAnalyticsFrameMeta *) user_meta->user_meta_data;
            for (auto& kv : anl_meta->ocStatus)
            {
                std::string roi_id = kv.first;
                if(kv.second){
                    if (roi_appear_count[source_id].find(roi_id) == roi_appear_count[source_id].end())
                        roi_appear_count[source_id][roi_id] = 0;
                    roi_appear_count[source_id][roi_id] = roi_appear_count[source_id][roi_id] + 1;
                    all_roi.emplace_back(roi_id);
                }

                // if (!kv.second)
                // {  
                //     /*此区域的数量小于阈值*/
                //     printf("source_id:%d, roi_id:%s, pts:%ld, not congestion.\n", source_id, roi_id.c_str(), buf_pts);
                // }
                // else
                // {
                //     if (roi_appear_count[source_id].find(roi_id) == roi_appear_count[source_id].end())
                //         roi_appear_count[source_id][roi_id] = 0;
                //     roi_appear_count[source_id][roi_id] = roi_appear_count[source_id][roi_id] + 1;
                //     all_roi.emplace_back(roi_id);

                //     printf("source_id:%d, roi_id:%s, buf_pts:%ld, obj_appear_count:%ld.\n",
                //     source_id, roi_id.c_str(), buf_pts, roi_appear_count[source_id][roi_id]);

                // }

            }
        }
        seq_pts_roi[source_id].emplace(std::make_pair(buf_pts, all_roi));  // 
        if (buf_pts - seq_pts_roi[source_id].front().first < sac_probe_config.detect_duration * 1000 * 1000 * 1000)
        {
            // printf("source_id:%d, roi_id: all, buf_pts:%ld, front_pts:%ld, skip too short.\n",
            //     source_id, buf_pts, seq_pts_roi[source_id].front().first);
            continue;
        }
        while (true)
        {
            for (int i = 0; i < seq_pts_roi[source_id].front().second.size(); i++)
            {
                std::string roi_id = seq_pts_roi[source_id].front().second[i];  // 
                roi_appear_count[source_id][roi_id] = roi_appear_count[source_id][roi_id] - 1;

                // printf("source_id:%d, roi_id:%s, buf_pts:%ld, front_pts:%ld, roi_appear_count:%ld.\n",
                //     source_id, roi_id.c_str(), buf_pts, seq_pts_roi[source_id].front().first, roi_appear_count[source_id][roi_id]);
            }
            seq_count[source_id] = seq_count[source_id] - 1;
            seq_pts_roi[source_id].pop();

            // printf("source_id:%d, roi_id: all, buf_pts:%ld, front_pts:%ld, seq_count:%ld.\n",
            //     source_id, buf_pts, seq_pts_roi[source_id].front().first, seq_count[source_id]);
            if (buf_pts - seq_pts_roi[source_id].front().first <= sac_probe_config.detect_duration * 1000 * 1000 * 1000)
                break;
        }
        std::unordered_map<std::string, std::vector<float>> cur_rois = rois[source_id];
        for (l_obj = frame_meta->obj_meta_list; l_obj != nullptr; l_obj = l_obj->next) { //保存所有的脸
            obj_meta = (NvDsObjectMeta *) l_obj->data;
            if (obj_meta == NULL){
                continue;
            }
            std::vector<float> obj_box;
            obj_box.emplace_back(obj_meta->rect_params.left);
            obj_box.emplace_back(obj_meta->rect_params.top);
            obj_box.emplace_back(obj_meta->rect_params.left + obj_meta->rect_params.width);
            obj_box.emplace_back(obj_meta->rect_params.top + obj_meta->rect_params.height);
            std::string cur_roi_name = in_which_roi(cur_rois, obj_box, 0.01);
            // std::cout<<"cur_roi_name: "<<cur_roi_name<<std::endl;
            if (cur_roi_name=="none"){//此目标不在任何区域里面 不统计
                // printf("source_id:%d, track_id:%d, not in any rois.\n", source_id, track_id);
                continue;
            }

            // typedef struct _sac_msg {
            //     std::vector<float> bbox;
            //     float score;
            //     int track_id;
            //     float track_score;
            //     std::string class_name;
            // } sac_msg;


            int track_id = obj_meta->object_id;
            std::string obj_label = obj_meta->obj_label;
            float score = obj_meta->confidence;
            float track_conf = obj_meta->tracker_confidence;
            sac_msg __sac_msg = {};
            __sac_msg.bbox = obj_box;
            __sac_msg.score = score;
            __sac_msg.track_id = track_id;
            __sac_msg.track_score = track_conf;
            __sac_msg.class_name = obj_label;

            if(rois_vehicles.find(cur_roi_name)==rois_vehicles.end()){
                std::vector<sac_msg> info;
                info.emplace_back(__sac_msg);
                rois_vehicles[cur_roi_name] = info;
            } else {
                rois_vehicles[cur_roi_name].emplace_back(__sac_msg);
            }

        }

        for (l_user = frame_meta->frame_user_meta_list; l_user != nullptr; l_user = l_user->next)
        {
            user_meta = (NvDsUserMeta *) l_user->data;
            if(user_meta->base_meta.meta_type != NVDS_USER_FRAME_META_NVDSANALYTICS)
                continue;
            anl_meta = (NvDsAnalyticsFrameMeta *) user_meta->user_meta_data;
            for (auto& kv : anl_meta->ocStatus)
            {
                std::string roi_id = kv.first;
                if (roi_ouput_times[source_id].find(roi_id) == roi_ouput_times[source_id].end())
                    roi_ouput_times[source_id][roi_id] = 0;
                
                // printf("source_id:%d, roi_id:%s, buf_pts:%ld, roi_ouput_times:%ld.\n",
                // source_id, roi_id.c_str(), buf_pts, roi_ouput_times[source_id][roi_id]);
                if (0 <= sac_probe_config.warning_times && sac_probe_config.warning_times <= roi_ouput_times[source_id][roi_id])
                    continue;
                
                float this_detect_sensitivity = float(roi_appear_count[source_id][roi_id]) / float(seq_count[source_id]);
                // printf("source_id:%d, roi_id:%s, buf_pts:%ld, this_detect_sensitivity:%0.2f.\n",
                    // source_id, roi_id.c_str(), buf_pts, this_detect_sensitivity);
                if (this_detect_sensitivity < sac_probe_config.detect_sensitivity)
                    continue;
                
                if (roi_ouput_pts[source_id].find(roi_id) != roi_ouput_pts[source_id].end()){
                    if (buf_pts - roi_ouput_pts[source_id][roi_id] < sac_probe_config.warning_interval * 1000 * 1000 * 1000)
                        continue;
                }

                // if (roi_ouput_pts[source_id].find(roi_id) == roi_ouput_pts[source_id].end())
                // {
                //     printf("source_id:%d, roi_id:%s, buf_pts:%ld, roi_ouput_pts: none.\n",
                //         source_id, roi_id.c_str(), buf_pts);
                // }
                // else
                // {
                //     printf("source_id:%d, roi_id:%s, buf_pts:%ld, roi_ouput_pts:%ld.\n",
                //         source_id, roi_id.c_str(), buf_pts, roi_ouput_pts[source_id][roi_id]);
                //     // 
                //     if (buf_pts - roi_ouput_pts[source_id][roi_id] < sac_probe_config.warning_interval * 1000 * 1000 * 1000)
                //         continue;
                // }
                roi_ouput_pts[source_id][roi_id] = buf_pts; 
                roi_ouput_times[source_id][roi_id] = roi_ouput_times[source_id][roi_id] + 1; 
                printf("source_id:%d, roi_id:%s, buf_pts:%ld, output.\n", source_id, roi_id.c_str(), buf_pts);
                // 4. then output
                objectObj = json_object_new();
                json_object_set_string_member(objectObj, "roi_label", roi_id.c_str());
                json_object_set_double_member(objectObj, "start_time", static_cast<double>(seq_pts_roi[source_id].front().first) / 1000 / 1000 / 1000);
                json_object_set_double_member(objectObj, "end_time", static_cast<double>(buf_pts) / 1000 / 1000 / 1000);
                json_array_add_object_element(objectArray, objectObj);
                std::vector<sac_msg> cur_roi_info = rois_vehicles[roi_id];
                // std::cout<<"cur_roi_info.size(): "<<cur_roi_info.size()<<std::endl;
                for(auto info : cur_roi_info){
                    objectObj = json_object_new();
                    json_object_set_string_member(objectObj, "object_label", info.class_name.c_str());
                    json_object_set_double_member(objectObj, "detect_conf", info.score);
                    if (info.track_id + 1 > 0) {
                        json_object_set_string_member(objectObj, "track_id", std::to_string(info.track_id).c_str());
                        json_object_set_double_member(objectObj, "track_conf", info.track_score);
                    }
                    boxObj = json_object_new();
                    float x = info.bbox[0] / streammux_width;
                    float y = info.bbox[1] / streammux_height;
                    if (info.bbox[0] < 1) {
                        x = 0.000001;
                    }
                    if (info.bbox[1] < 1) {
                        y = 0.000001;
                    }
                    float width = (info.bbox[2] - info.bbox[0]) / streammux_width;
                    float height = (info.bbox[3] - info.bbox[1]) / streammux_height;
                    json_object_set_double_member(boxObj, "x", x);
                    json_object_set_double_member(boxObj, "y", y);
                    json_object_set_double_member(boxObj, "width", width);
                    json_object_set_double_member(boxObj, "height", height);
                    json_object_set_object_member(objectObj, "object_box", boxObj);
                    json_array_add_object_element(objectArray, objectObj);
                }
                

                obj_index++;
            }
        }

        if (obj_index > 0) {
            NvDsObjEncUsrArgs userData{};
            userData.saveImg = true;
            strncpy(userData.fileNameImg, frame_name.c_str(), frame_name.size());
            userData.attachUsrMeta = false;
            userData.objNum = 1;
            NvDsObjectMeta obj_frame;
            obj_frame.rect_params.left = 0;
            obj_frame.rect_params.top = 0;
            obj_frame.rect_params.width = streammux_width;
            obj_frame.rect_params.height = streammux_height;
            nvds_obj_enc_process(ctx_enc_handle, &userData, ip_surf, &obj_frame, frame_meta);

            rootObj = json_object_new();
            // version node
            json_object_set_string_member(rootObj, "version", "1.0");
            // task node
            taskObj = json_object_new();
            if (sac_probe_config.workspace_id) {
                json_object_set_string_member(taskObj, "workspace_id", sac_probe_config.workspace_id);
            }
            if (sac_probe_config.zone_id) {
                json_object_set_string_member(taskObj, "zone_id", sac_probe_config.zone_id);
            }
            if (sac_probe_config.box_id) {
                json_object_set_string_member(taskObj, "box_id", sac_probe_config.box_id);
            }
            if (sac_probe_config.model_name) {
                json_object_set_string_member(taskObj, "model_name", sac_probe_config.model_name);
            }
            if (sac_probe_config.task_id) {
                json_object_set_string_member(taskObj, "task_id", sac_probe_config.task_id);
            }
            if (sac_probe_config.id) {
                json_object_set_string_member(taskObj, "id", sac_probe_config.id);
            }
            if (sac_probe_config.model_id) {
                json_object_set_string_member(taskObj, "model_id", sac_probe_config.model_id);
            }
            if (sac_probe_config.evt_info) {
                json_object_set_string_member(taskObj, "message", sac_probe_config.evt_info);
            }
            // if (sac_probe_config.congestion_times) {
            //     json_object_set_string_member(taskObj, "congestion_times", std::to_string(sac_probe_config.congestion_times).c_str());
            // }
            if (sac_probe_config.object_threshold) {
                json_object_set_string_member(taskObj, "object_threshold", std::to_string(source_threshold_map[source_id]).c_str());
                // json_object_set_string_member(taskObj, "object_threshold", std::to_string(sac_probe_config.object_threshold).c_str());
            }
            if (sac_probe_config.warning_interval) {
                json_object_set_string_member(taskObj, "warning_interval", std::to_string(sac_probe_config.warning_interval).c_str());
            }
            if (sac_probe_config.detect_duration) {
                json_object_set_string_member(taskObj, "detect_duration", std::to_string(sac_probe_config.detect_duration).c_str());
            }
            if (sac_probe_config.detect_sensitivity) {
                json_object_set_string_member(taskObj, "detect_sensitivity", std::to_string(sac_probe_config.detect_sensitivity).c_str());
            }
            json_object_set_object_member(rootObj, "task", taskObj);
            // events node
            eventsObj = json_object_new();
            json_object_set_string_member(eventsObj, "camera_id", camera_id.c_str());
            struct timespec ts{};
            clock_gettime(CLOCK_REALTIME, &ts);
            guint64 timestamp = ts.tv_sec * 1000 + static_cast<uint>(ts.tv_nsec/1000000);
            json_object_set_int_member(eventsObj, "timestamp", timestamp);
            imageObj = json_object_new();
            json_object_set_string_member(imageObj, "path", image_store_name.c_str());
            json_object_set_int_member(imageObj, "width", streammux_width);
            json_object_set_int_member(imageObj, "height", streammux_height);
            json_object_set_object_member(eventsObj, "image", imageObj);

            if(record==1){
                std::string video_path;
                NvDsSrcBin *src_bin = &bin->sub_bins[frame_meta->source_id];
                smart_record_handle(src_bin, frame_meta->source_id, video_path);
                video_path = get_FileSuffix(video_path);
                videoObj = json_object_new();
                json_object_set_string_member(videoObj, "path", video_path.c_str());
                json_object_set_int_member(videoObj, "width", streammux_width);
                json_object_set_int_member(videoObj, "height", streammux_height);
                json_object_set_object_member(eventsObj, "video", videoObj);
            }

            json_object_set_array_member(eventsObj, "objects", objectArray);
            json_object_set_object_member(rootObj, "events", eventsObj);
            rootNode = json_node_new(JSON_NODE_OBJECT);
            json_node_set_object(rootNode, rootObj);

            gchar* event_msg = (gchar*)json_to_string(rootNode, TRUE);
            events_message.emplace_back(event_msg);
            json_node_free(rootNode);
            json_object_unref(rootObj);
        } else {
            json_array_unref(objectArray);
        }
    }

    nvds_obj_enc_finish(ctx_enc_handle);

    if (events_message.size() > 0) {
        for (auto &event_msg : events_message) { 

            struct timespec temp_ts{};
            clock_gettime(CLOCK_REALTIME, &temp_ts);
            std::string time_slice = std::to_string(temp_ts.tv_sec*1000000000 + temp_ts.tv_nsec);
            std::string tmp_msg_local_store_name = msg_local_store_path + time_slice + ".tmp";
            std::string msg_local_store_name = msg_local_store_path + time_slice + ".txt";
            
            std::ofstream outfile;
            outfile.open(tmp_msg_local_store_name, std::ios::out|std::ios::app);
            outfile << event_msg << std::endl;
            outfile.close();
            g_free(event_msg);
            if (std::rename(tmp_msg_local_store_name.c_str(), msg_local_store_name.c_str())) {
                std::cout << "Error renaming file from " << tmp_msg_local_store_name <<  " to " << msg_local_store_name << " !!!" << std::endl;
            }

        }
    }


}
/**
 * check the defined function
 * @param func the funtion name which defined above
 */
CHECK_SAC_CUSTOM_PROBE_FUNC_PROTOTYPE(SACCustomProbeMessageSender);
