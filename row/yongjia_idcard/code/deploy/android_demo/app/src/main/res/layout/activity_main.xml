<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
                                             xmlns:app="http://schemas.android.com/apk/res-auto"
                                             xmlns:tools="http://schemas.android.com/tools"
                                             android:layout_width="match_parent"
                                             android:layout_height="match_parent"
                                             tools:context=".MainActivity">

    <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

        <LinearLayout
                android:id="@+id/v_input_info"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:orientation="vertical">

            <LinearLayout
                android:id="@+id/btn_layout"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">


                <Button
                    android:id="@+id/btn_run_model"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:onClick="btn_run_model_click"
                    android:text="运行模型" />
                <Button
                    android:id="@+id/btn_take_photo"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:onClick="btn_take_photo_click"
                    android:text="拍照识别" />
                <Button
                    android:id="@+id/btn_choice_img"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:onClick="btn_choice_img_click"
                    android:text="选取图片" />

                <Button
                    android:id="@+id/btn_reset_img"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:onClick="btn_reset_img_click"
                    android:text="清空绘图" />

            </LinearLayout>
            <LinearLayout
                android:id="@+id/run_mode_layout"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <CheckBox
                    android:id="@+id/cb_opencl"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"
                    android:text="开启OPENCL"
                    android:onClick="cb_opencl_click"
                    android:visibility="gone"/>
                <TextView
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:layout_height="wrap_content"
                    android:text="运行模式:"/>

                <Spinner
                    android:id="@+id/sp_run_mode"
                    android:layout_width="0dp"
                    android:layout_weight="1.5"
                    android:layout_height="wrap_content"
                    android:entries="@array/run_Model"
                    />

            </LinearLayout>

            <TextView
                    android:id="@+id/tv_input_setting"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:scrollbars="vertical"
                    android:layout_marginLeft="12dp"
                    android:layout_marginRight="12dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:lineSpacingExtra="4dp"
                    android:singleLine="false"
                    android:maxLines="6"
                    android:text=""/>
            <TextView
                android:id="@+id/tv_model_img_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:scrollbars="vertical"
                android:layout_marginLeft="12dp"
                android:layout_marginRight="12dp"
                android:layout_marginTop="-5dp"
                android:layout_marginBottom="5dp"
                android:lineSpacingExtra="4dp"
                android:singleLine="false"
                android:maxLines="6"
                android:text="STATUS: ok"/>

        </LinearLayout>

        <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/v_output_info"
                android:layout_below="@+id/v_input_info">

            <ImageView
                    android:id="@+id/iv_input_image"
                    android:layout_width="400dp"
                    android:layout_height="400dp"
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="12dp"
                    android:layout_marginRight="12dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:adjustViewBounds="true"
                    android:scaleType="fitCenter"/>
        </RelativeLayout>


        <RelativeLayout
                android:id="@+id/v_output_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true">

            <TextView
                    android:id="@+id/tv_output_result"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"
                    android:scrollbars="vertical"
                    android:layout_marginLeft="12dp"
                    android:layout_marginRight="12dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:textAlignment="center"
                    android:lineSpacingExtra="5dp"
                    android:singleLine="false"
                    android:maxLines="5"
                    android:text=""/>

            <TextView
                    android:id="@+id/tv_inference_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tv_output_result"
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"
                    android:textAlignment="center"
                    android:layout_marginLeft="12dp"
                    android:layout_marginRight="12dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="10dp"
                    android:text=""/>

        </RelativeLayout>

    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
