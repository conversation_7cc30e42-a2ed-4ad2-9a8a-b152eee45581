# -*- coding: utf-8 -*-

import cv2
import os
import threading
from shutil import copyfile

def video_to_frames(video_path, outPutDirName):
    times = 0
    
    # 提取视频的频率，每1帧提取一个
    frame_frequency = 1
    
	# 如果文件目录不存在则创建目录
    if not os.path.exists(outPutDirName):
        os.makedirs(outPutDirName)
        
    # 读取视频帧
    camera = cv2.VideoCapture(video_path)
    
    while True:
        times = times + 1
        res, image = camera.read()
        
        #cv2.imwrite('1.jpg', image)
        #break
       
        if not res:
            print('not res , not image')
            break
        
        if times % frame_frequency == 0:
            #print(times)
            print( str(times)+'.jpg')
            #cv2.imwrite(str(times)+'.jpg', image)
            cv2.imwrite(outPutDirName + '\\frame_' + str(times - 1).zfill(6) +'.PNG', image)
            
    print('图片提取结束')
    camera.release()





def rename():
    root = 'D:/data/city_data/trash_data/val_trash'
    pro = os.listdir(root)
    for p in pro:
        print(p)
        id = p.split('_')[-1]
        for txt in os.listdir(os.path.join(root, p, 'Annotations_txts')):
            print(txt)
            src_file = os.path.join(root, p, 'Annotations_txts', txt)
            dst_file = os.path.join(root, p, 'Annotations_txts', str(id)+'_'+txt)
            os.rename(src_file, dst_file)
        for txt in os.listdir(os.path.join(root, p, 'JPEGImages')):
            print(txt)
            src_file = os.path.join(root, p, 'JPEGImages', txt)
            dst_file = os.path.join(root, p, 'JPEGImages', str(id)+'_'+txt)
            os.rename(src_file, dst_file)
        

def copy_img_label_by_cache():
    train_txt = r'D:\data\city_data\trash_data\train_train\dataSet_path\val.txt'
    image_path = r'D:\data\city_data\trash_data\train_train\images'
    label_path = r'D:\data\city_data\trash_data\train_train\labels'

    save_img_path = r'D:\data\city_data\trash_data\val_trash\images\train'
    save_txt_path = r'D:\data\city_data\trash_data\val_trash\labels\train'
    
    os.makedirs(save_img_path, exist_ok=True)
    os.makedirs(save_txt_path, exist_ok=True)

    with open(train_txt, 'r', encoding='utf-8') as f:
        data = f.readlines()
    for d in data:
        print(d)
        name = os.path.basename(d.strip('\n'))
        names = name.split('.')
        basename = '.'.join(names[0:len(names)-1])
        
        src_img_file = os.path.join(image_path, basename+'.jpg')
        src_txt_file = os.path.join(label_path, basename+'.txt')
        
        dst_img_path = os.path.join(save_img_path, basename+'.jpg')
        dst_txt_path = os.path.join(save_txt_path, basename+'.txt')
        try:
            copyfile(src_img_file, dst_img_path)
            copyfile(src_txt_file, dst_txt_path)
        except:
            continue


if __name__ == "__main__":

    
    # rename()
    copy_img_label_by_cache()










    # input_dir = r'C:\Users\<USER>\Desktop\zxyu\ibox项目测试用例\vedio'#r'D:\datasets\cow_dataset'       # 输入的video文件夹位置
    
    # save_dir = r'C:\Users\<USER>\Desktop\zxyu\ibox项目测试用例\save_img'# r'E:\relate_code\dataset'         # 输出图片到当前目录video文件夹下

    # input_dir = r'D:/datasets/trash/shujutang_test/video'#r'D:\datasets\cow_dataset'       # 输入的video文件夹位置
    
    # save_dir = r'D:/datasets/trash/shujutang_test/frames'# r'E:\relate_code\dataset'         # 输出图片到当前目录video文件夹下
    
    # count = 0   # 视频数
    # for video_name in os.listdir(input_dir):
    #     #print('1')
    #     #print(video_name)
    #     video_path = os.path.join(input_dir, video_name)
    #     outPutDirName = os.path.join(save_dir, video_name[:-4])
    #     #print(outPutDirName)
    #     threading.Thread(target=video_to_frames, args=(video_path, outPutDirName)).start()
    #     count = count + 1
    #     print("%s th video has been finished!" % count)



# import os
# import cv2

# def decode_video(video_path, save_dir, target_num=None):
#     '''
#     video_path: 待解码的视频
#     save_dir: 抽帧图片的保存文件夹
#     target_num: 抽帧的数量, 为空则解码全部帧, 默认抽全部帧
#     '''
#     if not os.path.exists(save_dir):
#         os.makedirs(save_dir)
#     video = cv2.VideoCapture()
#     if not video.open(video_path):
#         print("can not open the video")
#         exit(1)
#     print('ahhahaahh')
#     count = 0
#     index = 0
#     frames_num = video.get(7)
#     # 如果target_num为空就全部抽帧,不为空就抽target_num帧
#     if target_num is None:
#         step = 1
#         print('all frame num is {}, decode all'.format(int(frames_num)))
#     else:
#         step = int(frames_num/target_num)
#         print('all frame num is {}, decode sample num is {}'.format(int(frames_num), int(target_num)))
#     while True:
#         _, frame = video.read()
#         if frame is None:
#             break
#         if count % step == 0:
#             save_path = "{}/{:>04d}.png".format(save_dir, index)
#             cv2.imwrite(save_path, frame)
#             index += 1
#         count += 1
#         if index == frames_num and target_num is None:
#             # 如果全部抽，抽到所有帧的最后一帧就停止
#             break
#         elif  index == target_num and target_num is not None: 
#             # 如果采样抽，抽到target_num就停止
#             break
#         else:
#             pass
#     video.release()
    

# if __name__ == '__main__':
#     video_path = r'C:\Users\<USER>\Desktop\zxyu\ibox项目测试用例\vedio\1.mp4'
#     save_dir_1 = r'C:\Users\<USER>\Desktop\zxyu\ibox项目测试用例\images_all'
#     save_dir_2 = r'C:\Users\<USER>\Desktop\zxyu\ibox项目测试用例\images_sample'
    
#     decode_video(video_path, save_dir_1)
#     #decode_video(video_path, save_dir_2, 20)



