#define _CRT_SECURE_NO_WARNINGS //VS中必须定义,否则报错


#include <dlfcn.h>
#include <stdlib.h>
#include "deepstream_c2d_msg.h"
#include "deepstream_c2d_msg_util.h"
#include "deepstream_common.h"
#include "deepstream_sources.h"
#include "gst-nvdssr.h"

#include "sac_custom_probe_impl.h"
#include "sacmsg_custom_meta.h"
#include "nvds_analytics_meta.h"
#include "nvbufsurface.h"
#include "nvdsmeta_schema.h"
#include "nvds_obj_encode.h"
#include "deepstream_app.h"
#include "deepstream_app_config_parser_ext.h"
#include <experimental/filesystem>
#include <iostream>
#include <string>
#include <memory>
#include <vector>
#include <json-glib/json-glib.h>
#include "opencv2/imgproc/imgproc.hpp"
#include "opencv2/highgui/highgui.hpp"
#include <fstream>
#include <unordered_map>
#include <algorithm>
#include <cuda.h>
#include <cuda_runtime.h>
#include <math.h>
#include <assert.h>
#include <stdio.h> 
#include <opencv2/opencv.hpp>
#include <ctime>
#include <time.h>

#define th_velocity_a 10
#define th_velocity_b 20
#define th_count_a 100
// #define th_count_b 8
#define th_person_count 6
// #define t1 30
// #define t2 20


extern SACProbeConfig sac_probe_config;

typedef struct _sac_msg_roi {
    std::unordered_map<std::string, std::vector<int>> static_info;
    std::unordered_map<std::string, std::vector<int>> move_info;
    std::unordered_map<std::string, int> detect_info;
    std::unordered_map<std::string, int> detect_info_classify;
    int warn_time;
    int last_warn_time;
} sac_msg_roi;


typedef struct _sac_msg_static {
    std::vector<float> box;
    float score;
    std::string class_name;
    int track_id;
} sac_msg_static;

typedef struct _sac_msg_person {
    std::unordered_map<std::string, int> detect_info;
    int track_id;
    std::unordered_map<std::string, std::vector<float>> bbox;
    std::string class_name;
    float score;
    std::string roi_name;
    int source_id;
} sac_msg_person;


typedef struct _sac_msg_vehicle {
    std::unordered_map<std::string, int> detect_info;
    std::unordered_map<std::string, std::vector<float>> bbox;
    std::vector<float> first_center;
    // std::vector<float> bbox;
    float score;
    int track_id;
    int class_id;
    int first_buf_pts;
    std::unordered_map<std::string, int> velocity;
    std::string class_name;
    std::string roi_name;
    int source_id;
} sac_msg_vehicle;

typedef struct _sac_msg_vehicle_classify {
    int track_id;
    std::unordered_map<std::string, int> detect_info;
    float score;
    // int accident;
    std::vector<float> bbox;
    std::string class_name;
} sac_msg_vehicle_classify;


float iou(std::vector<float> lbox, std::vector<float> rbox) {
    float interBox[] = {
        std::max(lbox[0], rbox[0]), //left
        std::min(lbox[2], rbox[2]), //right
        std::max(lbox[1], rbox[1]), //top
        std::min(lbox[3], rbox[3]), //bottom
    };

    if(interBox[2] > interBox[3] || interBox[0] > interBox[1])
        return 0.0f;

    float interBoxS =(interBox[1]-interBox[0])*(interBox[3]-interBox[2]);
    return interBoxS/((lbox[2]-lbox[0])*(lbox[3]-lbox[1]) + (rbox[2]-rbox[0])*(rbox[3]-rbox[1]) -interBoxS);
}

float get_intersec_area(std::vector<float> big_box, std::vector<float> small_box, int track_id){
    std::vector<cv::Point> contour_big;
    std::vector<cv::Point> contour_small;
    std::vector<cv::Point> intersectionPolygon;
    // std::cout<<"big_box.size(): "<<big_box.size()<<std::endl;
    for(int i = 0; i < big_box.size()/2; i++){
        cv::Point point;
        point.x = big_box[2*i];
        point.y = big_box[2*i+1];
        contour_big.emplace_back(point);
        // std::cout<<"x: "<<big_box[2*i]<<" y: "<<big_box[2*i+1]<<std::endl;
    }
    std::vector<float> new_small_box;
    new_small_box.emplace_back(small_box[0]);
    new_small_box.emplace_back(small_box[1]);
    new_small_box.emplace_back(small_box[2]);
    new_small_box.emplace_back(small_box[1]);
    new_small_box.emplace_back(small_box[2]);
    new_small_box.emplace_back(small_box[3]);
    new_small_box.emplace_back(small_box[0]);
    new_small_box.emplace_back(small_box[3]);
    for(int i = 0; i < new_small_box.size()/2; i++){
        cv::Point point;
        point.x = new_small_box[2*i];
        point.y = new_small_box[2*i+1];
        contour_small.emplace_back(point);
    }
    float intersectArea = cv::intersectConvexConvex(contour_big, contour_small, intersectionPolygon, true);
    // std::cout<<"contour_big: "<<contour_big<<std::endl;
    // std::cout<<"contour_small: "<<contour_small<<std::endl;
    // std::cout<<"intersectionPolygon: "<<intersectionPolygon<<std::endl;
    float box_area = (small_box[3]-small_box[1]) * (small_box[2] - small_box[0]);
    // std::cout<<"track_id: "<<track_id<<" intersectArea: "<<intersectArea<<" box_area: "<<box_area<<std::endl;
    float roi_sensitivity = intersectArea / box_area;
    return roi_sensitivity;

}

float get_roi_sensitivity(std::vector<float> roi, std::vector<float> obj_box, float roi_sensitivity, int track_id){
    float cur_sensitivity = get_intersec_area(roi, obj_box, track_id);
    // std::cout<<"track_id: "<<track_id<<" cur_sensitivity: "<<cur_sensitivity<<std::endl;
    if(cur_sensitivity>=roi_sensitivity){
        return cur_sensitivity;
    }
    return 0.0;
}

std::string in_which_roi(std::unordered_map<std::string, std::vector<float>> cur_rois, std::vector<float> obj_box, float roi_sensitivity, int track_id){
    std::string roi_name = "none";
    float max_sensitivity = 0.0;
    for(auto cur_roi : cur_rois){
        std::vector<float> roi_box = cur_roi.second;
        float cur_sensitivity = get_roi_sensitivity(roi_box, obj_box, roi_sensitivity, track_id);
        if(cur_sensitivity > max_sensitivity){
           max_sensitivity = cur_sensitivity;
           roi_name = cur_roi.first;
        }
    }
    return roi_name;
}

std::string get_FileSuffix(std::string path){
    std::string suffix;
	for (int i = path.size() - 1; i > 0; i--){
		if (path[i] == '/'){
			suffix = path.substr(i + 1);
			return suffix;
		}
	}
    return suffix;
}

int get_num(std::unordered_map<std::string, int> detect_info, int duration, int cur_frame_num, int source_id){
    int start = cur_frame_num - duration + 1;
    start = std::max(start, 0);
    int num=0;
    for(start; start<cur_frame_num; start++){
        std::string start_obj = std::to_string(source_id) + '_' + std::to_string(start);
        if(detect_info.find(start_obj)!=detect_info.end()){
            if(detect_info[start_obj]==1){
                num++;
            }
        }
    }
    // std::cout<<"num: "<<num<<std::endl;
    // std::cout<<"duration: "<<duration<<std::endl;
    return num;
}


bool get_static_vehicle_num(std::unordered_map<std::string, int> detect_info, 
                            std::unordered_map<std::string, int> velocity, 
                            std::unordered_map<std::string, std::vector<float>> bbox, 
                            int duration, 
                            int cur_frame_num, 
                            int velocity_filter,
                            float rate,
                            int source_id){
    int start = cur_frame_num - duration + 1;
    start = std::max(start, 0);
    int num=0;
    std::vector<float> first_bbox, last_bbox;
    for(int i = start; i<cur_frame_num; i++){
        std::string start_obj = std::to_string(source_id) + '_' + std::to_string(i);
        if(bbox.find(start_obj)!=bbox.end()){
            first_bbox = bbox[start_obj];
            break;
        }
    }

    for(start; start<cur_frame_num; start++){
        std::string start_obj = std::to_string(source_id) + '_' + std::to_string(start);
        if(detect_info.find(start_obj)!=detect_info.end() && velocity.find(start_obj)!=velocity.end() && bbox.find(start_obj)!=bbox.end()){
            last_bbox = bbox[start_obj];
            if(detect_info[start_obj]==1 && velocity[start_obj]<(velocity_filter/2)){
                num++;
            }
        }
    }
    float cur_iou = 0.0;
    if(first_bbox.size()>0 && last_bbox.size()>0)
        cur_iou = iou(first_bbox, last_bbox);
    // std::cout<<"cur_iou: "<<cur_iou<<std::endl;
    // std::cout<<"num: "<<num<<std::endl;
    if(num >= duration*rate && cur_iou>=0.35)
        return true;
    return false;
}

//录制视频
void smart_record_handle(
    NvDsSrcBin *src_bin,
    guint source_id,
    std::string &video_path)
{
    NvDsSRSessionId sessId = 0;
    NvDsSRStatus st;
    guint startTime = 10;
    guint duration = 10;
    gchar *filename;
    if (src_bin->config->smart_rec_duration >= 0){
        duration = src_bin->config->smart_rec_duration;
    }
    if (src_bin->config->smart_rec_start_time >= 0){
        startTime = src_bin->config->smart_rec_start_time;
    }
    if (src_bin->recordCtx && !src_bin->reconfiguring){
        NvDsSRContext *ctx = (NvDsSRContext *)src_bin->recordCtx;
        if (!ctx->recordOn){
            g_print("Recording started..\n");
            st = NvDsSRStart(ctx, &sessId, startTime, duration, NULL);
            g_object_get(G_OBJECT(ctx->filesink), "location", &filename, NULL);
            if (st != NVDSSR_STATUS_OK)
                g_printerr("Unable to start recording, status=%d, recordOn=%d, resetDone=%d\n", st, ctx->recordOn, ctx->resetDone);
        }
        else{
            // NvDsSRStop (ctx, 0);
            g_object_get(G_OBJECT(ctx->filesink), "location", &filename, NULL);
        }
        video_path = filename; // It will do copy
    }
    else{
        video_path = "";
    }
}

int get_source_id(const std::string& str) {
    char delim  = '-';
    std::stringstream ss(str);
    std::string result;
    std::string item;
    while (std::getline(ss, item, delim)) {
        if (!item.empty()) {
            result = item;
        }
    }
    return std::stoi(result);
}

std::string get_roi_id(const std::string& str) {
    char delim  = '-';
    std::stringstream ss(str);
    std::string result;
    std::string item;
    while (std::getline(ss, item, delim)) {
        if (!item.empty()) {
            result = item;
        }
    }
    return result;
}

std::vector<std::string> stringSplit(const std::string& str){
    char delim  = '=';
    std::stringstream ss(str);
    std::vector<std::string> result;
    std::string item;

    while (std::getline(ss, item, delim)) {
        if (!item.empty()) {
            std::string new_result;
            for(auto r : item){
                if(r!=' '){
                    new_result += r;
                }
            }
            result.emplace_back(new_result);
        }
    }
    return result;
}

std::vector<float> stringSplit_bbox(const std::string& str){
    char delim  = ';';
    std::stringstream ss(str);
    std::vector<float> result;
    std::string item;
    while (std::getline(ss, item, delim)) {
        if (!item.empty()) {
            result.emplace_back((float)std::stoi(item));
        }
    }
    return result;
}

std::unordered_map<int, std::unordered_map<std::string, std::vector<float>>> get_roi_box(std::string filename){
    std::unordered_map<int, std::unordered_map<std::string, std::vector<float>>> result;
    int last_source_id = -1;
    std::ifstream file(filename); // 创建输入流对象并打开文件
    if (file) { // 如果成功打开了文件
        std::string line;
        while (std::getline(file, line)) { // 逐行读取文本内容
            if(line[0]=='#') continue;
            // std::cout << line << std::endl; // 将每行内容输出到控制台
            if(line.find("roi-filtering-stream") != std::string::npos){
                last_source_id = get_source_id(line);
            } else if(line.find("roi-") != std::string::npos){
                std::vector<std::string> r = stringSplit(line);
                std::string roi_name = get_roi_id(r[0]);
                std::vector<float> roi_bbox = stringSplit_bbox(r[1]);
                if(result.find(last_source_id)==result.end()){
                    std::unordered_map<std::string, std::vector<float>> id_info;
                    id_info[roi_name] = roi_bbox;
                    result[last_source_id] = id_info;
                } else {
                    std::unordered_map<std::string, std::vector<float>> id_info = result[last_source_id];
                    id_info[roi_name] = roi_bbox;
                    result[last_source_id] = id_info;
                }
            }
        }
        file.close(); // 关闭文件
    } else {
        std::cout << "unable to open config file" << std::endl; // 若无法打开文件则输出错误信息
    }
    return result;
}


int Congestion_num(std::unordered_map<int, std::vector<int>> detect_info, int time_duration, int cur_frame_num){
    int start = cur_frame_num - time_duration + 1;
    std::unordered_map<int, bool> obj_output_id;
    start = std::max(start, 0);
    for(start; start<cur_frame_num; start++){
        if(detect_info.find(start)!=detect_info.end()){
            std::vector<int> ids = detect_info[start];
            for(auto track_id : ids){
                if(obj_output_id.find(track_id)==obj_output_id.end()){
                    obj_output_id[track_id] = true;
                }
            }
        }
    }
    int num = obj_output_id.size();
    return num;
}

int Congestion_num_1(std::unordered_map<std::string, std::vector<int>> static_info, 
                     std::unordered_map<std::string, std::vector<int>> move_info, 
                     int time_duration, int cur_frame_num, 
                     int need_num_static, int need_num_move,
                     int source_id){
    int start = cur_frame_num - time_duration + 1;
    start = std::max(start, 0);
    int result = 0;
    for(start; start<cur_frame_num; start++){
        std::string start_obj = std::to_string(source_id) + '_' + std::to_string(start);
        if(static_info.find(start_obj)!=static_info.end() && move_info.find(start_obj)!=move_info.end()){
            std::vector<int> ids_static = static_info[start_obj];
            std::vector<int> ids_move = move_info[start_obj];
            // std::cout<<"frame num: "<<start<<std::endl;
            // std::cout<<"static num: "<<ids_static.size()<<std::endl;
            // for(auto s : ids_static){
            //     std::cout<<s<<" ";
            // }
            // std::cout<<std::endl;
            // std::cout<<"move num: "<<ids_move.size()<<std::endl;
            // for(auto s : ids_move){
            //     std::cout<<s<<" ";
            // }
            // if(ids_move.size()>0)
            //     std::cout<<std::endl;
            if(ids_static.size() >= need_num_static && ids_move.size() <= need_num_move)
                result++;
        }
    }
    return result;
}

std::unordered_map<int, sac_msg_static> is_car_car_accident(std::vector<sac_msg_static> bboxs){
    std::unordered_map<int, sac_msg_static> result;
    for(int i=0;i<bboxs.size();i++){
        for(int j=0;j<bboxs.size();j++){
            if(i==j) continue;
            if(iou(bboxs[i].box, bboxs[j].box) > 0) {
                int track_id_i = bboxs[i].track_id;
                int track_id_j = bboxs[j].track_id;
                if(result.find(track_id_i)==result.end()){
                    result[track_id_i] = bboxs[i];
                }
                if(result.find(track_id_j)==result.end()){
                    result[track_id_j] = bboxs[j];
                }
                // g_print("is_car_car_accident\n");
            }
        }
    }
    return result;
}


std::unordered_map<int, sac_msg_static> is_car_person_accident(std::vector<sac_msg_static> bboxs_car, std::vector<sac_msg_static> bboxs_person){
    std::unordered_map<int, sac_msg_static> result;
    for(int i=0;i<bboxs_car.size();i++){
        for(int j=0;j<bboxs_person.size();j++){
            if(iou(bboxs_car[i].box, bboxs_person[j].box) > 0) {
                int track_id_i = bboxs_car[i].track_id;
                int track_id_j = bboxs_person[j].track_id;
                if(result.find(track_id_i)==result.end()){
                    result[track_id_i] = bboxs_car[i];
                }
                if(result.find(track_id_j)==result.end()){
                    result[track_id_j] = bboxs_person[j];
                }
                // g_print("is_car_person_accident\n");
            }
        }
    }
    return result;
}


std::unordered_map<int, sac_msg_static> is_car_accident(std::vector<sac_msg_static> bboxs_car, std::unordered_map<std::string, sac_msg_vehicle_classify> vehicle_classify, int frame_num, int source_id){
    std::unordered_map<int, sac_msg_static> result;
    for(int i=0;i<bboxs_car.size();i++){
        std::string sensor_obj = std::to_string(source_id) + '_' + std::to_string(bboxs_car[i].track_id);
        std::string frame_obj = std::to_string(source_id) + '_' + std::to_string(frame_num);
        if(vehicle_classify.find(sensor_obj)!=vehicle_classify.end()){
            if(vehicle_classify[sensor_obj].detect_info[frame_obj]==1){
                int track_id = bboxs_car[i].track_id;
                result[track_id] = bboxs_car[i];
            }
        }
    }

    return result;
}

int select_sutable_fps(int fps){
    std::vector<int> normal_fps {15,25,30,60};
    int final_fps;
    int min_gap=100;
    for(auto n_fps : normal_fps){
        int gap = abs(n_fps-fps);
        if(gap<min_gap && gap<5){
            min_gap = gap;
            final_fps = n_fps;
        }
    }
    return final_fps;
}

/**
 * a custom probe function to generate an event message and attach to the buffer
 *
 * @param user_data   a pointer used to pass AppCtx which is defined in deepstream_app.h
 *
 * @param buf   a GstBuffer struct used to get nvbuffer and picture meta
 *
 * @param batch_meta   used to get nvidia defined batch meta, frame meta, obj meta...
 *
 * @param index   an index representing the index of a source
 *
 * @return void
 */
extern "C"
void SACCustomProbeMessageSender(void *user_data, GstBuffer *buf, NvDsBatchMeta *batch_meta, guint index) {
    
    // int running_time_start = sac_probe_config.running_time_start;
    // int running_time_end = sac_probe_config.running_time_end;
    // time_t nowtime;
	// time(&nowtime); //获取1970年1月1日0点0分0秒到现在经过的秒数
	// tm* p = localtime(&nowtime); //将秒数转换为本地时间,年从1900算起,需要+1900,月为0-11,所以要+1
	// int cur_hour = p->tm_hour + 8;
	// int cur_min = p->tm_min;
	// int cur_sec = p->tm_sec;
    // if(running_time_start > running_time_end){
    //     std::cout<<"start time > end time"<<std::endl;
    //     return;
    // }
    // if((running_time_start >= 0 && cur_hour<running_time_start) || (running_time_end >= 0 && cur_hour>running_time_end)){
    //     return;
    // }


    auto appCtx = static_cast<AppCtx *>(user_data);

    NvDsSrcParentBin *bin = &appCtx->pipeline.multi_src_bin;

    auto streammux_width = appCtx->config.streammux_config.pipeline_width;
    auto streammux_height = appCtx->config.streammux_config.pipeline_height;
    std::string config_file_path = appCtx->config.dsanalytics_config.config_file_path;
    static NvDsObjEncCtxHandle ctx_enc_handle = nvds_obj_enc_create_context();
    GstMapInfo inmap = GST_MAP_INFO_INIT;
    NvBufSurface *ip_surf;
    if (!gst_buffer_map(buf, &inmap, GST_MAP_READ)) {
        std::cerr << "input buffer mapinfo failed!" << std::endl;
        return;
    }
    ip_surf = (NvBufSurface *) inmap.data;
    gst_buffer_unmap(buf, &inmap);
    std::vector<gchar *> events_message;
    std::string common_store_path = "/data/";
    std::string image_store_path = common_store_path + "data/";
    std::experimental::filesystem::path temp_img_path(image_store_path);

    if(!std::experimental::filesystem::exists(temp_img_path)){
        std::experimental::filesystem::create_directories(temp_img_path); 
    }
    std::string msg_local_store_path = common_store_path + "meta/";
    std::experimental::filesystem::path temp_msg_path(msg_local_store_path);
    if(!std::experimental::filesystem::exists(temp_msg_path)){
        std::experimental::filesystem::create_directories(temp_msg_path);
    }
    
    NvDsFrameMeta *frame_meta;
    NvDsLabelInfo *label_meta;
    NvDsObjectMeta *obj_meta;
    NvDsClassifierMeta *cls_meta;
    NvDsUserMeta *user_meta;
    NvDsAnalyticsObjInfo *anl_meta;
    NvDsMetaList *l_frame, *l_obj, *l_cls, *l_user;
    NvOSD_RectParams obj_box;

    JsonNode *rootNode;  // json根节点
    JsonObject *rootObj; // 根对象
    JsonObject *taskObj; // 任务信息对象
    JsonObject *eventsObj; // 事件对象
    JsonObject *imageObj; // 图片对象
    JsonObject *videoObj; // 图片对象
    JsonArray *objectArray; // 目标对象数组
    JsonObject *objectObj; // 目标对象
    JsonObject *boxObj; // box对象

    static std::unordered_map<int, std::unordered_map<std::string, std::vector<float>>>  rois;
    static std::unordered_map<std::string, sac_msg_roi> roi_info; //每个roi的信息 以roi为单位进行告警
    static std::unordered_map<std::string, sac_msg_person> person_info; //每个roi的信息 以roi为单位进行告警
    static std::unordered_map<std::string, sac_msg_vehicle> vehicle_info; //每个roi的信息 以roi为单位进行告警
    static std::unordered_map<std::string, sac_msg_vehicle_classify> vehicle_classify_info; //每辆车的分类信息
    static std::unordered_map<int, float> source_avg_pts;
    static std::unordered_map<int, int> source_fps;
    static std::unordered_map<int, int> source_new_t1;
    static std::unordered_map<int, int> source_new_t2;
    static std::unordered_map<int, int> source_time_duration;
    static std::unordered_map<int, int> source_filter_times;


    std::unordered_map<std::string, sac_msg_vehicle_classify> traffic_classify_result;

    float time_duration = sac_probe_config.detect_duration;
    float roi_sensitivity = sac_probe_config.roi_sensitivity;
    int image_encoding_quality = sac_probe_config.image_encoding_quality;
    int warning_interval = sac_probe_config.warning_interval;
    int warning_times = sac_probe_config.warning_times;
    float warning_sensitivity = sac_probe_config.detect_sensitivity;
    float filter_times = time_duration * (1 - warning_sensitivity);
    int record = sac_probe_config.record;
    int t2 = sac_probe_config.t2;
    int t1 = sac_probe_config.t1;
    // int th_count_a = sac_probe_config.th_counta;
    int th_count_b = sac_probe_config.th_countb;

    if(warning_times==0){
        return;
    }

    for (l_frame = batch_meta->frame_meta_list; l_frame != nullptr; l_frame = l_frame->next) { //针对batch中每一帧
        frame_meta = (NvDsFrameMeta *) l_frame->data;
        guint source_id = frame_meta->source_id;
        gint frame_num = frame_meta->frame_num;
        guint64 buf_pts = frame_meta->buf_pts;
        guint frame_width = frame_meta->source_frame_width;
        guint frame_height = frame_meta->source_frame_height;
        std::string source_uri(appCtx->config.multi_source_config[source_id].uri);
        std::string camera_id = std::to_string(appCtx->config.multi_source_config[source_id].camera_id);
        guint drop_frame_interval = appCtx->config.multi_source_config[source_id].drop_frame_interval;

        if(frame_num==0){
            rois = get_roi_box(config_file_path);
            source_avg_pts[source_id] = buf_pts;
            source_fps[source_id] = 0;
        } else if(frame_num==1){
            int time_diff = buf_pts - source_avg_pts[source_id];
            // std::cout<<"source_id: " << source_id<< " time_diff: "<<time_diff<<std::endl;
            int cur_fps = ceil(1000000000 / time_diff) * drop_frame_interval;
            source_avg_pts[source_id] = buf_pts;
            source_fps[source_id] = std::max(source_fps[source_id], cur_fps);
            std::cout<<"source_id: " << source_id<< " fps: "<<source_fps[source_id]<<std::endl;
            source_new_t1[source_id] = t1*source_fps[source_id];
            source_new_t2[source_id] = t2*source_fps[source_id];
            source_time_duration[source_id] = time_duration*source_fps[source_id];
            source_filter_times[source_id] = ceil(filter_times*source_fps[source_id]);
        }
        // if(frame_num%100==0){
        //     std::cout<<"source_id: "<<source_id<<" frame_num: "<<frame_num<<std::endl;
        // }
        // 基于frame产生 msg_mata告警
        std::cout<<"source_id: "<<source_id<<" frame_num: "<<frame_num<<std::endl;
        
        
        if (drop_frame_interval) {
            // time_duration = (time_duration + (drop_frame_interval-1)) / drop_frame_interval;
            // filter_times = (filter_times + (drop_frame_interval-1)) / drop_frame_interval;
            if(frame_num==1){
                source_new_t1[source_id] = (t1*source_fps[source_id] + drop_frame_interval-1) / drop_frame_interval;
                source_new_t2[source_id] = (t2*source_fps[source_id] + drop_frame_interval-1) / drop_frame_interval;
                source_time_duration[source_id] = (time_duration*source_fps[source_id] + drop_frame_interval-1) / drop_frame_interval;
                source_filter_times[source_id] = ceil((filter_times*source_fps[source_id] + drop_frame_interval-1) / drop_frame_interval);
                std::cout<<"source_new_t1[source_id]: "<<source_new_t1[source_id]<<std::endl;
                std::cout<<"source_new_t2[source_id]: "<<source_new_t2[source_id]<<std::endl;
                std::cout<<"source_time_duration[source_id]: "<<source_time_duration[source_id]<<std::endl;
                std::cout<<"source_filter_times[source_id]: "<<source_filter_times[source_id]<<std::endl;
            }
        }

        //存储坐标框
        objectArray = json_array_new();
        guint obj_index = 0;
        std::vector<int> cur_vehicles;
        std::unordered_map<std::string, std::vector<float>> cur_rois = rois[source_id];
        int static_car = 0;
        std::string frame_obj = std::to_string(source_id) + "_" + std::to_string(frame_num);
        for (l_obj = frame_meta->obj_meta_list; l_obj != nullptr; l_obj = l_obj->next) {
            obj_meta = (NvDsObjectMeta *) l_obj->data;
            if(obj_meta==nullptr){
                continue;
            }
            auto track_id = obj_meta->object_id;
            float score = obj_meta->confidence;
            gint class_id = obj_meta->class_id;
            std::string class_name =  obj_meta->obj_label;
            std::vector<float> obj_box;
            obj_box.emplace_back(obj_meta->rect_params.left);
            obj_box.emplace_back(obj_meta->rect_params.top);
            obj_box.emplace_back(obj_meta->rect_params.left + obj_meta->rect_params.width);
            obj_box.emplace_back(obj_meta->rect_params.top + obj_meta->rect_params.height);
            
            std::string sensor_obj = std::to_string(source_id) + "_" + std::to_string(track_id);
            std::string cur_roi_name = in_which_roi(cur_rois, obj_box, roi_sensitivity, track_id);
            // 人、机动车、非机动车、车牌、人头、跌倒
            if (class_id == 0 || class_id == 5){
                if (cur_roi_name=="none"){//此目标不在任何区域里面 不统计
                    // printf("source_id:%d, track_id:%d, not in any rois.\n", source_id, track_id);
                    continue;
                } else {
                    if(person_info.find(sensor_obj)==person_info.end()){
                        sac_msg_person __sac_msg_person = {};
                        std::unordered_map<std::string, int> info {{frame_obj, 1}};
                        std::unordered_map<std::string, std::vector<float>> bbox {{frame_obj, obj_box}};
                        __sac_msg_person.detect_info = info;
                        __sac_msg_person.bbox = bbox;
                        __sac_msg_person.source_id = source_id;
                        __sac_msg_person.roi_name = cur_roi_name;
                        __sac_msg_person.track_id = track_id;
                        __sac_msg_person.class_name = class_name;
                        __sac_msg_person.score = score;
                        person_info[sensor_obj] = __sac_msg_person;
                        continue;
                    } else {
                        sac_msg_person tmp_sac_msg_person = person_info[sensor_obj];
                        if(tmp_sac_msg_person.roi_name != cur_roi_name){ //不在之前的区域了
                            sac_msg_person __sac_msg_person = {};
                            std::unordered_map<std::string, int> info {{frame_obj, 1}};
                            std::unordered_map<std::string, std::vector<float>> bbox {{frame_obj, obj_box}};
                            __sac_msg_person.detect_info = info;
                            __sac_msg_person.track_id = track_id;
                            __sac_msg_person.score = score;
                            __sac_msg_person.class_name = class_name;
                            __sac_msg_person.bbox = bbox;
                            __sac_msg_person.source_id = source_id;
                            __sac_msg_person.roi_name = cur_roi_name;
                            person_info[sensor_obj] = __sac_msg_person;
                        } else {
                            tmp_sac_msg_person.detect_info[frame_obj] = 1;
                            tmp_sac_msg_person.score = score;
                            tmp_sac_msg_person.bbox[frame_obj] = obj_box;
                            person_info[sensor_obj] = tmp_sac_msg_person;
                        }
                    }
                }
            }else if (class_id == 1 || class_id == 2) {
                // std::cout<<"track id: "<<track_id<<" class_id: "<<class_id<< std::endl;
                static_car += 1;
                float center_x = obj_meta->rect_params.left + 0.5 * obj_meta->rect_params.width;
                float center_y = obj_meta->rect_params.top + 0.5 * obj_meta->rect_params.height;
                // std::cout<<"track id: "<<track_id<<" width: "<<obj_meta->rect_params.width<<" height: "<<obj_meta->rect_params.height<<" score: "<<score<<std::endl;
                // std::string cur_roi_name = in_which_roi(cur_rois, obj_box, roi_sensitivity, track_id);
                if (cur_roi_name=="none"){//此目标不在任何区域里面 不统计
                    printf("source_id:%d, track_id:%d, not in any rois.\n", source_id, track_id);
                    continue;
                } else {
                    if(class_id==1){
                        for (l_cls = obj_meta->classifier_meta_list; l_cls != nullptr; l_cls = l_cls->next) { //针对每一个目标的second gie
                            cls_meta = (NvDsClassifierMeta *) l_cls->data;
                            for (NvDsLabelInfoList *ll = cls_meta->label_info_list; ll != nullptr; ll = ll->next) {
                                label_meta = (NvDsLabelInfo *)ll->data;
                                float classify_score = label_meta->result_prob;
                                // std::cout<<"track id: "<<track_id<<  "result_class_id: "<<label_meta->result_class_id<<" classify_score: "<<classify_score<<std::endl;
                                // std::string sensor_obj = std::to_string(source_id) + '_' + std::to_string(track_id);
                                cur_vehicles.emplace_back(track_id);
                                sac_msg_vehicle_classify __msg = {};
                                std::unordered_map<std::string, int> detect_info;
                                __msg.track_id = track_id;
                                __msg.score = score;
                                __msg.class_name = class_name;
                                __msg.bbox = obj_box;
                                if (label_meta->result_class_id == 0){ //accident
                                    detect_info[frame_obj]=1;
                                    // g_print("111\n");
                                } else{
                                    detect_info[frame_obj]=0;
                                }
                                __msg.detect_info = detect_info;
                                traffic_classify_result[sensor_obj] = __msg;


                                // if(traffic_classify_result.find(sensor_obj)==traffic_classify_result.end()){
                                //     sac_msg_vehicle_classify __msg = {};
                                //     std::unordered_map<std::string, int> detect_info;
                                //     __msg.track_id = track_id;
                                //     __msg.score = score;
                                //     __msg.class_name = class_name;
                                //     __msg.bbox = obj_box;
                                //     if (label_meta->result_class_id == 0){ //accident
                                //         // detect_info[frame_obj] = 1;
                                //         __msg.accident=1;
                                //         // g_print("111\n");
                                //     } else{
                                //         // detect_info[frame_obj] = 0;
                                //         __msg.accident=0;
                                //     }
                                //     __msg.detect_info = detect_info;
                                //     traffic_classify_result[sensor_obj] = __msg;
                                // } else {
                                //     sac_msg_vehicle_classify tmp_msg = traffic_classify_result[sensor_obj];
                                //     tmp_msg.score = score;
                                //     tmp_msg.bbox = obj_box;
                                //     if (label_meta->result_class_id == 0){ //accident
                                //         tmp_msg.detect_info[frame_obj] = 1;
                                //         // g_print("111\n");
                                //     } else{
                                //         tmp_msg.detect_info[frame_obj] = 0;
                                //     }
                                //     traffic_classify_result[sensor_obj] = tmp_msg;
                                // }
                            }
                        }
                    }
                    if(vehicle_info.find(sensor_obj)==vehicle_info.end()){
                        sac_msg_vehicle __sac_msg_vehicle = {};
                        std::unordered_map<std::string, int> info {{frame_obj, 1}};
                        std::unordered_map<std::string, int> velocity {{frame_obj, 0}};
                        std::unordered_map<std::string, std::vector<float>> bboxs {{frame_obj, obj_box}};
                        std::vector<float> center = {center_x, center_y};
                        __sac_msg_vehicle.detect_info = info;
                        __sac_msg_vehicle.first_buf_pts = buf_pts;
                        __sac_msg_vehicle.first_center = center;
                        __sac_msg_vehicle.bbox = bboxs;
                        __sac_msg_vehicle.roi_name = cur_roi_name;
                        __sac_msg_vehicle.velocity = velocity;
                        __sac_msg_vehicle.score = score;
                        __sac_msg_vehicle.source_id = source_id;
                        __sac_msg_vehicle.class_name = class_name;
                        __sac_msg_vehicle.track_id = track_id;
                        __sac_msg_vehicle.class_id = class_id;
                        vehicle_info[sensor_obj] = __sac_msg_vehicle;
                        continue;
                    } else {
                        sac_msg_vehicle tmp_sac_msg_vehiclce = vehicle_info[sensor_obj];
                        guint64 delta_time = buf_pts - tmp_sac_msg_vehiclce.first_buf_pts;
                        float distance = pow(center_x - tmp_sac_msg_vehiclce.first_center[0], 2) + pow(center_y - tmp_sac_msg_vehiclce.first_center[1], 2);
                        distance = sqrt(distance);
                        float this_velocity = distance / (float(delta_time) / 1000 / 1000 / 1000);
                        // std::cout<<"trackid: "<<track_id<<" this_velocity: "<<this_velocity<<std::endl;
                        if(tmp_sac_msg_vehiclce.roi_name != cur_roi_name){ //不在之前的区域了
                            sac_msg_vehicle __sac_msg_vehicle = {};
                            std::unordered_map<std::string, int> info {{frame_obj, 1}};
                            std::unordered_map<std::string, int> velocity {{frame_obj, this_velocity}};
                            std::unordered_map<std::string, std::vector<float>> bboxs {{frame_obj, obj_box}};
                            std::vector<float> center = {center_x, center_y};
                            __sac_msg_vehicle.detect_info = info;
                            __sac_msg_vehicle.first_buf_pts = buf_pts;
                            __sac_msg_vehicle.first_center = center;
                            __sac_msg_vehicle.bbox = bboxs;
                            __sac_msg_vehicle.roi_name = cur_roi_name;
                            __sac_msg_vehicle.velocity = velocity;
                            __sac_msg_vehicle.track_id = track_id;
                            __sac_msg_vehicle.source_id = source_id;
                            __sac_msg_vehicle.class_name = class_name;
                            __sac_msg_vehicle.score = score;
                            __sac_msg_vehicle.class_id = class_id;
                            vehicle_info[sensor_obj] = __sac_msg_vehicle;
                        } else {
                            tmp_sac_msg_vehiclce.detect_info[frame_obj] = 1;
                            tmp_sac_msg_vehiclce.velocity[frame_obj] = this_velocity;
                            tmp_sac_msg_vehiclce.bbox[frame_obj] = obj_box;
                            tmp_sac_msg_vehiclce.score = score;
                            vehicle_info[sensor_obj] = tmp_sac_msg_vehiclce;
                        }
                    }
                }
            }
        }
        std::cout<<"static_car: "<<static_car<<std::endl;
        // std::cout<<"source_id: "<<source_id<<<<" car num: "<<vehicle_info.size()<<std::endl;
        for(auto roi : cur_rois){ //判断每个区域的情况
            std::string roi_name = roi.first;
            std::string sensor_obj_roi = std::to_string(source_id)+"_"+roi_name;
            if(roi_info.find(sensor_obj_roi) != roi_info.end()){
                int seconds = time((time_t*)NULL);
                if(roi_info[sensor_obj_roi].warn_time > 0 && seconds - roi_info[sensor_obj_roi].last_warn_time < warning_interval){
                    continue;
                }
            }
            std::vector<int> move_count;
            std::vector<int> static_count;
            for(auto v_info : vehicle_info){
                if(v_info.second.roi_name!=roi_name || v_info.second.class_id != 1 || v_info.second.source_id != source_id){
                    continue;
                } else {
                    if(v_info.second.velocity.find(frame_obj)!= v_info.second.velocity.end() && v_info.second.velocity[frame_obj] < th_velocity_a){
                        static_count.emplace_back(v_info.second.track_id);
                    } else if(v_info.second.velocity.find(frame_obj)!= v_info.second.velocity.end() && v_info.second.velocity[frame_obj] > th_velocity_b){
                        move_count.emplace_back(v_info.second.track_id);
                    }
                }
            }
            // std::cout<<"static_count.size(): "<<static_count.size()<<std::endl;
            // std::cout<<"move_count.size(): "<<move_count.size()<<std::endl;
            if(roi_info.find(sensor_obj_roi)==roi_info.end()){
                sac_msg_roi __sac_msg_roi = {};
                std::unordered_map<std::string, std::vector<int>> static_info;
                std::unordered_map<std::string, std::vector<int>> move_info;
                std::unordered_map<std::string, int> info {{frame_obj, 0}};
                std::unordered_map<std::string, int> classify_info {{frame_obj, 0}};
                static_info[frame_obj] = static_count;
                move_info[frame_obj] = move_count;
                __sac_msg_roi.detect_info = info;
                __sac_msg_roi.detect_info_classify = classify_info;
                __sac_msg_roi.static_info = static_info;
                __sac_msg_roi.move_info = move_info;
                __sac_msg_roi.warn_time = 0;
                __sac_msg_roi.last_warn_time = 0;
                roi_info[sensor_obj_roi] = __sac_msg_roi;
            } else {
                sac_msg_roi tmp_sac_msg_roi = roi_info[sensor_obj_roi];
                tmp_sac_msg_roi.static_info[frame_obj] = static_count;
                tmp_sac_msg_roi.move_info[frame_obj] = move_count;
                roi_info[sensor_obj_roi] = tmp_sac_msg_roi;
                if(tmp_sac_msg_roi.move_info.size() < source_new_t1[source_id]){ //没到t1时间判断是否拥堵
                    continue;
                }
                if(tmp_sac_msg_roi.move_info.size() >= source_new_t1[source_id] 
                    && Congestion_num_1(tmp_sac_msg_roi.static_info, tmp_sac_msg_roi.move_info, source_new_t1[source_id], frame_num, th_count_b, th_count_a, source_id) >= source_new_t1[source_id]*0.8){ 
                    // && Congestion_num_1(tmp_sac_msg_roi.static_info, source_new_t1[source_id], frame_num, th_count_b) >= source_new_t1[source_id]*0.8 ){//拥堵路段 清除历史缓存
                    // std::cout<<"source_id: "<<source_id<<" yongdu num: "<<Congestion_num_1(tmp_sac_msg_roi.static_info, tmp_sac_msg_roi.move_info, source_new_t1[source_id], frame_num, th_count_b, th_count_a)<<std::endl;
                    std::cout<<"source_id: "<<source_id<< " Congestion road, not traffic accident..."<<std::endl;
                    tmp_sac_msg_roi.detect_info.clear();
                    tmp_sac_msg_roi.detect_info_classify.clear();
                    tmp_sac_msg_roi.static_info.clear();
                    tmp_sac_msg_roi.move_info.clear();
                    roi_info[sensor_obj_roi] = tmp_sac_msg_roi;
                    continue;
                } 
                //判断是否发生交通事故

                float rate = 0.8;
                // std::cout<<"source_id: "<<source_id<< " not a Congestion road...."<<std::endl;
                std::vector<sac_msg_static> static_vehicle;
                std::vector<sac_msg_static> static_person;
                for(auto p_info : person_info){ //获取过去时间段内静止的行人框
                    if(p_info.second.roi_name!=roi_name || p_info.second.source_id != source_id){
                        continue;
                    } else if(p_info.second.detect_info.size() >= (source_new_t2[source_id]*rate) 
                        && get_num(p_info.second.detect_info, source_new_t2[source_id], frame_num, source_id) >= (source_new_t2[source_id]*rate)
                        && p_info.second.bbox.find(frame_obj)!=p_info.second.bbox.end()){
                        sac_msg_static __sac_msg_static = {};
                        __sac_msg_static.box = p_info.second.bbox[frame_obj];
                        __sac_msg_static.score = p_info.second.score;
                        __sac_msg_static.class_name = p_info.second.class_name;
                        __sac_msg_static.track_id = p_info.second.track_id;
                        static_person.emplace_back(__sac_msg_static);
                        // std::cout<<"source_id: "<<source_id<<" static_person: "<<p_info.second.track_id<<std::endl;
                    }
                }
                for(auto v_info : vehicle_info){//获取过去时间段内静止的机动车、非机动车框
                    if(v_info.second.roi_name == roi_name && v_info.second.source_id == source_id){
                        // std::cout<<"num: "<<get_static_vehicle_num(v_info.second.detect_info, v_info.second.velocity, source_new_t2[source_id], frame_num, th_velocity_a)<<std::endl;
                        if(v_info.second.detect_info.size() >= (source_new_t2[source_id]*rate) 
                        && get_static_vehicle_num(v_info.second.detect_info, v_info.second.velocity, v_info.second.bbox, source_new_t2[source_id], frame_num, th_velocity_a, rate, source_id)
                        && v_info.second.bbox.find(frame_obj) != v_info.second.bbox.end()){
                            if (find(cur_vehicles.begin(), cur_vehicles.end(), v_info.second.track_id) != cur_vehicles.end()|| v_info.second.class_id==2){
                                sac_msg_static __sac_msg_static = {};
                                __sac_msg_static.box = v_info.second.bbox[frame_obj];
                                __sac_msg_static.score = v_info.second.score;
                                __sac_msg_static.class_name = v_info.second.class_name;
                                __sac_msg_static.track_id = v_info.second.track_id;
                                static_vehicle.emplace_back(__sac_msg_static);
                            }
                            // std::cout<<"source_id: "<<source_id<<" static_vehicle: "<<v_info.second.track_id<<std::endl;
                        }
                    }
                }
                
                std::cout<<"source_id: "<<source_id<<" static_vehicle.size(): "<<static_vehicle.size()<<std::endl;
                std::cout<<"source_id: "<<source_id<<" static_person.size(): "<<static_person.size()<<std::endl;
                // std::cout<<"source_id: "<<source_id<<" num: "<<get_num(tmp_sac_msg_roi.detect_info, source_time_duration[source_id], frame_num)<<std::endl;


                if(static_vehicle.size()==0) 
                    continue; 
                std::unordered_map<int, sac_msg_static> result_car_car = is_car_car_accident(static_vehicle);
                std::unordered_map<int, sac_msg_static> result_car_person = is_car_person_accident(static_vehicle, static_person);
                
                std::unordered_map<int, sac_msg_static> result_car_accdent = is_car_accident(static_vehicle, traffic_classify_result, frame_num, source_id);

                std::cout<<"result_car_car.size(): "<<result_car_car.size()<<std::endl;
                std::cout<<"result_car_person.size(): "<<result_car_person.size()<<std::endl;
                std::cout<<"result_car_accdent.size(): "<<result_car_accdent.size()<<std::endl;
                std::cout<<"num: "<<get_num(tmp_sac_msg_roi.detect_info, source_time_duration[source_id], frame_num, source_id)<<std::endl;
                // std::cout<<"filter num: "<<source_filter_times[source_id]<<std::endl;
                int seconds = time((time_t*)NULL);
                if((static_person.size() >= th_person_count && static_vehicle.size()>0)
                    || result_car_car.size()>0
                    || result_car_person.size()>0){//满足交通事故条件 1、区域内行人数量大于等于6且存在静止车辆 2、静止车辆之间有交叉、3、静止行人与静止车辆之间有交叉
                    tmp_sac_msg_roi.detect_info[frame_obj] = 1;
                    if((warning_times == -1 || tmp_sac_msg_roi.warn_time < warning_times)
                    && (tmp_sac_msg_roi.warn_time==0 || (tmp_sac_msg_roi.warn_time > 0 && seconds - tmp_sac_msg_roi.last_warn_time >= warning_interval))
                    && tmp_sac_msg_roi.detect_info.size() >= source_filter_times[source_id]
                    && get_num(tmp_sac_msg_roi.detect_info, source_time_duration[source_id], frame_num, source_id)>=source_filter_times[source_id]){
                        std::cout<<"source_id: "<<source_id<<" ROI: "<<roi_name<<"  traffic accident happen at frame: "<<frame_num<<std::endl;
                        // std::cout<<"static_vehicle.size(): "<<static_vehicle.size()<<std::endl;
                        // std::cout<<"static_person.size(): "<<static_person.size()<<std::endl;
                        // std::cout<<"result_car_car.size(): "<<result_car_car.size()<<std::endl;
                        // std::cout<<"result_car_person.size(): "<<result_car_person.size()<<std::endl;
                        std::unordered_map<int, int> output_ids;
                        if((static_person.size() >= th_person_count && static_vehicle.size()>0)){
                            for(auto s_person : static_person){
                                output_ids[s_person.track_id] = 1;
                                objectObj = json_object_new();
                                json_object_set_string_member(objectObj, "roi_label", (roi_name).c_str());
                                json_object_set_int_member(objectObj, "roi_point_num", cur_rois[roi_name].size());
                                boxObj = json_object_new();
                                for(int j=0; j<cur_rois[roi_name].size(); j++){
                                    json_object_set_int_member(boxObj, ("roi_point_" + std::to_string(j)).c_str(), cur_rois[roi_name][j]);
                                }
                                json_object_set_object_member(objectObj, "roi_box", boxObj);
                                json_object_set_string_member(objectObj, "object_label", (s_person.class_name).c_str());
                                json_object_set_double_member(objectObj, "detect_conf", s_person.score);
                                if (s_person.track_id + 1 > 0) {
                                    json_object_set_string_member(objectObj, "track_id", std::to_string(s_person.track_id).c_str());
                                }
                                boxObj = json_object_new();
                                std::vector<float> obj_box = s_person.box;
                                float x = obj_box[0] / streammux_width;
                                float y = obj_box[1] / streammux_height;
                                if (obj_box[0] < 1) {
                                    x = 0.000001;
                                }
                                if (obj_box[1] < 1) {
                                    y = 0.000001;
                                }
                                float width = (obj_box[2] - obj_box[0]) / streammux_width;
                                float height = (obj_box[3] - obj_box[1]) / streammux_height;
                                json_object_set_double_member(boxObj, "x", x);
                                json_object_set_double_member(boxObj, "y", y);
                                json_object_set_double_member(boxObj, "width", width);
                                json_object_set_double_member(boxObj, "height", height);
                                json_object_set_object_member(objectObj, "object_box", boxObj);;
                                json_array_add_object_element(objectArray, objectObj);
                                obj_index++;
                            }
                            for(auto s_vehicle : static_vehicle){
                                output_ids[s_vehicle.track_id] = 1;
                                objectObj = json_object_new();
                                json_object_set_string_member(objectObj, "roi_label", (roi_name).c_str());
                                json_object_set_int_member(objectObj, "roi_point_num", cur_rois[roi_name].size());
                                boxObj = json_object_new();
                                for(int j=0; j<cur_rois[roi_name].size(); j++){
                                    json_object_set_int_member(boxObj, ("roi_point_" + std::to_string(j)).c_str(), cur_rois[roi_name][j]);
                                }
                                json_object_set_object_member(objectObj, "roi_box", boxObj);
                                json_object_set_string_member(objectObj, "object_label", (s_vehicle.class_name).c_str());
                                json_object_set_double_member(objectObj, "detect_conf", s_vehicle.score);
                                if (s_vehicle.track_id + 1 > 0) {
                                    json_object_set_string_member(objectObj, "track_id", std::to_string(s_vehicle.track_id).c_str());
                                }
                                boxObj = json_object_new();
                                std::vector<float> obj_box = s_vehicle.box;
                                float x = obj_box[0] / streammux_width;
                                float y = obj_box[1] / streammux_height;
                                if (obj_box[0] < 1) {
                                    x = 0.000001;
                                }
                                if (obj_box[1] < 1) {
                                    y = 0.000001;
                                }
                                float width = (obj_box[2] - obj_box[0]) / streammux_width;
                                float height = (obj_box[3] - obj_box[1]) / streammux_height;
                                json_object_set_double_member(boxObj, "x", x);
                                json_object_set_double_member(boxObj, "y", y);
                                json_object_set_double_member(boxObj, "width", width);
                                json_object_set_double_member(boxObj, "height", height);
                                json_object_set_object_member(objectObj, "object_box", boxObj);;
                                json_array_add_object_element(objectArray, objectObj);
                                obj_index++;
                            }
                        }
                        if(result_car_car.size()>0){
                            for(auto r : result_car_car){
                                if(output_ids.find(r.first)==output_ids.end()){//没有写过这个obj
                                    output_ids[r.first] = 1;
                                    sac_msg_static s_vehicle = r.second;
                                    objectObj = json_object_new();
                                    json_object_set_string_member(objectObj, "roi_label", (roi_name).c_str());
                                    json_object_set_int_member(objectObj, "roi_point_num", cur_rois[roi_name].size());
                                    boxObj = json_object_new();
                                    for(int j=0; j<cur_rois[roi_name].size(); j++){
                                        json_object_set_int_member(boxObj, ("roi_point_" + std::to_string(j)).c_str(), cur_rois[roi_name][j]);
                                    }
                                    json_object_set_object_member(objectObj, "roi_box", boxObj);
                                    json_object_set_string_member(objectObj, "object_label", (s_vehicle.class_name).c_str());
                                    json_object_set_double_member(objectObj, "detect_conf", s_vehicle.score);
                                    if (s_vehicle.track_id + 1 > 0) {
                                        json_object_set_string_member(objectObj, "track_id", std::to_string(s_vehicle.track_id).c_str());
                                    }
                                    boxObj = json_object_new();
                                    std::vector<float> obj_box = s_vehicle.box;
                                    float x = obj_box[0] / streammux_width;
                                    float y = obj_box[1] / streammux_height;
                                    if (obj_box[0] < 1) {
                                        x = 0.000001;
                                    }
                                    if (obj_box[1] < 1) {
                                        y = 0.000001;
                                    }
                                    float width = (obj_box[2] - obj_box[0]) / streammux_width;
                                    float height = (obj_box[3] - obj_box[1]) / streammux_height;
                                    json_object_set_double_member(boxObj, "x", x);
                                    json_object_set_double_member(boxObj, "y", y);
                                    json_object_set_double_member(boxObj, "width", width);
                                    json_object_set_double_member(boxObj, "height", height);
                                    json_object_set_object_member(objectObj, "object_box", boxObj);;
                                    json_array_add_object_element(objectArray, objectObj);
                                    obj_index++;
                                }
                            }
                        }
                        if(result_car_person.size()>0){
                            for(auto r : result_car_person){
                                if(output_ids.find(r.first)==output_ids.end()){//没有写过这个obj
                                    output_ids[r.first] = 1;
                                    sac_msg_static s_vehicle = r.second;
                                    objectObj = json_object_new();
                                    json_object_set_string_member(objectObj, "roi_label", (roi_name).c_str());
                                    json_object_set_int_member(objectObj, "roi_point_num", cur_rois[roi_name].size());
                                    boxObj = json_object_new();
                                    for(int j=0; j<cur_rois[roi_name].size(); j++){
                                        json_object_set_int_member(boxObj, ("roi_point_" + std::to_string(j)).c_str(), cur_rois[roi_name][j]);
                                    }
                                    json_object_set_object_member(objectObj, "roi_box", boxObj);
                                    json_object_set_string_member(objectObj, "object_label", (s_vehicle.class_name).c_str());
                                    json_object_set_double_member(objectObj, "detect_conf", s_vehicle.score);
                                    if (s_vehicle.track_id + 1 > 0) {
                                        json_object_set_string_member(objectObj, "track_id", std::to_string(s_vehicle.track_id).c_str());
                                    }
                                    boxObj = json_object_new();
                                    std::vector<float> obj_box = s_vehicle.box;
                                    float x = obj_box[0] / streammux_width;
                                    float y = obj_box[1] / streammux_height;
                                    if (obj_box[0] < 1) {
                                        x = 0.000001;
                                    }
                                    if (obj_box[1] < 1) {
                                        y = 0.000001;
                                    }
                                    float width = (obj_box[2] - obj_box[0]) / streammux_width;
                                    float height = (obj_box[3] - obj_box[1]) / streammux_height;
                                    json_object_set_double_member(boxObj, "x", x);
                                    json_object_set_double_member(boxObj, "y", y);
                                    json_object_set_double_member(boxObj, "width", width);
                                    json_object_set_double_member(boxObj, "height", height);
                                    json_object_set_object_member(objectObj, "object_box", boxObj);;
                                    json_array_add_object_element(objectArray, objectObj);
                                    obj_index++;
                                }
                            }
                        }

                        tmp_sac_msg_roi.warn_time += 1;
                        tmp_sac_msg_roi.last_warn_time = seconds;
                        tmp_sac_msg_roi.detect_info.clear();
                        tmp_sac_msg_roi.detect_info_classify.clear();
                        tmp_sac_msg_roi.static_info.clear();
                        tmp_sac_msg_roi.move_info.clear();
                   }
                    roi_info[sensor_obj_roi] = tmp_sac_msg_roi;
                } else if(result_car_accdent.size()>0){
                        tmp_sac_msg_roi.detect_info_classify[frame_obj] = 1;
                        bool output = false;
                        if((warning_times == -1 || tmp_sac_msg_roi.warn_time < warning_times)
                        && (tmp_sac_msg_roi.warn_time==0 || (tmp_sac_msg_roi.warn_time > 0 && seconds - tmp_sac_msg_roi.last_warn_time >= warning_interval))
                        && tmp_sac_msg_roi.detect_info_classify.size() >= source_filter_times[source_id]
                        && get_num(tmp_sac_msg_roi.detect_info_classify, source_time_duration[source_id], frame_num, source_id)>=source_filter_times[source_id]){
                            output = true;
                            std::cout<<"source_id: "<<source_id<<" ROI: "<<roi_name<<"  traffic accident happen at frame:  "<<frame_num<<" with condition 4..."<<std::endl;
                            if(result_car_accdent.size()>0){
                                for(auto r : result_car_accdent){
                                    sac_msg_static s_vehicle = r.second;
                                    objectObj = json_object_new();

                                    json_object_set_string_member(objectObj, "roi_label", (roi_name).c_str());
                                    json_object_set_int_member(objectObj, "roi_point_num", cur_rois[roi_name].size());
                                    boxObj = json_object_new();
                                    for(int j=0; j<cur_rois[roi_name].size(); j++){
                                        json_object_set_int_member(boxObj, ("roi_point_" + std::to_string(j)).c_str(), cur_rois[roi_name][j]);
                                    }
                                    json_object_set_object_member(objectObj, "roi_box", boxObj);
                                    json_object_set_string_member(objectObj, "object_label", (s_vehicle.class_name).c_str());
                                    json_object_set_string_member(objectObj, "warning_type", "accident classify");
                                    json_object_set_double_member(objectObj, "detect_conf", s_vehicle.score);
                                    if (s_vehicle.track_id + 1 > 0) {
                                        json_object_set_string_member(objectObj, "track_id", std::to_string(s_vehicle.track_id).c_str());
                                    }
                                    boxObj = json_object_new();
                                    std::vector<float> obj_box = s_vehicle.box;
                                    float x = obj_box[0] / streammux_width;
                                    float y = obj_box[1] / streammux_height;
                                    if (obj_box[0] < 1) {
                                        x = 0.000001;
                                    }
                                    if (obj_box[1] < 1) {
                                        y = 0.000001;
                                    }
                                    float width = (obj_box[2] - obj_box[0]) / streammux_width;
                                    float height = (obj_box[3] - obj_box[1]) / streammux_height;
                                    json_object_set_double_member(boxObj, "x", x);
                                    json_object_set_double_member(boxObj, "y", y);
                                    json_object_set_double_member(boxObj, "width", width);
                                    json_object_set_double_member(boxObj, "height", height);
                                    json_object_set_object_member(objectObj, "object_box", boxObj);;
                                    json_array_add_object_element(objectArray, objectObj);
                                    obj_index++;
                                }
                            }
                        }
                        if(output){
                            tmp_sac_msg_roi.warn_time += 1;
                            tmp_sac_msg_roi.last_warn_time = seconds;
                            tmp_sac_msg_roi.detect_info.clear();
                            tmp_sac_msg_roi.detect_info_classify.clear();
                            tmp_sac_msg_roi.static_info.clear();
                            tmp_sac_msg_roi.move_info.clear();
                        }
                        roi_info[sensor_obj_roi] = tmp_sac_msg_roi;
                }
            }
        }
        // std::cout<<"obj_index: "<<obj_index<<std::endl;
        // json对象
        if(obj_index>0){
            void* src_data = nullptr;
            src_data = malloc(ip_surf->surfaceList[frame_meta->batch_id].dataSize);
            if (src_data == nullptr) {
                g_print("Error: failed to malloc src_data \n");
                return;
            }
            cudaMemcpy(src_data,  
                (void *)ip_surf->surfaceList[frame_meta->batch_id].dataPtr, 
                ip_surf->surfaceList[frame_meta->batch_id].dataSize,
                cudaMemcpyDeviceToHost);
            size_t frame_pitch = ip_surf->surfaceList[frame_meta->batch_id].pitch;
            cv::Mat msg_frame = cv::Mat(streammux_height * 3 / 2, streammux_width, CV_8UC1,  (char *)src_data, frame_pitch);
            std::string store_name =  std::to_string(source_id) + '_' + std::to_string(frame_num) + '_'
                                        + std::to_string(frame_width) + 'x' + std::to_string(frame_height) + '_'
                                        + std::to_string(frame_meta->ntp_timestamp);
            std::string frame_name = image_store_path + store_name + ".png";
            std::string tmp_msg_local_store_name = msg_local_store_path + store_name + ".tmp";
            std::string msg_local_store_name = msg_local_store_path + store_name + ".txt";
            //保存图片
            cv::Mat out_mat;
            cv::cvtColor(msg_frame, out_mat, cv::COLOR_YUV2BGR_NV12);
            // std::vector<int> compression_params;
            // compression_params.emplace_back(1);  //选择jpeg
            // compression_params.emplace_back(image_encoding_quality); //在这个填入你要的图片质量
            // cv::imwrite(frame_name, out_mat, compression_params);           
            cv::imwrite(frame_name, out_mat);           

            rootObj = json_object_new();
            // version node
            json_object_set_string_member(rootObj, "version", "1.0");
            // task node
            taskObj = json_object_new();
            // if (sac_probe_config.workspace_id) {
            //     json_object_set_string_member(taskObj, "workspace_id", sac_probe_config.workspace_id);
            // }
            // if (sac_probe_config.zone_id) {
            //     json_object_set_string_member(taskObj, "zone_id", sac_probe_config.zone_id);
            // }
            // if (sac_probe_config.box_id) {
            //     json_object_set_string_member(taskObj, "box_id", sac_probe_config.box_id);
            // }
            if (sac_probe_config.model_name) {
                json_object_set_string_member(taskObj, "model_name", sac_probe_config.model_name);
            }
            if (sac_probe_config.task_id) {
                json_object_set_string_member(taskObj, "task_id", sac_probe_config.task_id);
            }
            // if (sac_probe_config.model_id) {
            //     json_object_set_string_member(taskObj, "model_id", sac_probe_config.model_id);
            // }
            // if (sac_probe_config.evt_info) {
            //     json_object_set_string_member(taskObj, "message", sac_probe_config.evt_info);
            // }
            json_object_set_object_member(rootObj, "task", taskObj);
            // events node
            eventsObj = json_object_new();
            json_object_set_string_member(eventsObj, "camera_id", camera_id.c_str());
            struct timespec ts{};
            clock_gettime(CLOCK_REALTIME, &ts);
            guint64 timestamp = ts.tv_sec * 1000 + static_cast<uint>(ts.tv_nsec/1000000);
            json_object_set_int_member(eventsObj, "timestamp", timestamp);
            imageObj = json_object_new();
            json_object_set_string_member(imageObj, "path", (store_name+".png").c_str());
            json_object_set_string_member(imageObj, "uri", source_uri.c_str());
            json_object_set_int_member(imageObj, "width", streammux_width);
            json_object_set_int_member(imageObj, "height", streammux_height);
            json_object_set_object_member(eventsObj, "image", imageObj);
            if(record==1){
                std::string video_path;
                NvDsSrcBin *src_bin = &bin->sub_bins[frame_meta->source_id];
                smart_record_handle(src_bin, frame_meta->source_id, video_path);
                video_path = get_FileSuffix(video_path);
                videoObj = json_object_new();
                json_object_set_string_member(videoObj, "path", video_path.c_str());
                json_object_set_int_member(videoObj, "width", streammux_width);
                json_object_set_int_member(videoObj, "height", streammux_height);
                json_object_set_object_member(eventsObj, "video", videoObj);
            }
            json_object_set_array_member(eventsObj, "objects", objectArray);
            json_object_set_object_member(rootObj, "events", eventsObj);
            rootNode = json_node_new(JSON_NODE_OBJECT);
            json_node_set_object(rootNode, rootObj);
            gchar* event_msg = (gchar*)json_to_string(rootNode, TRUE);
            json_node_free(rootNode);
            json_object_unref(rootObj);

            std::ofstream outfile;
            outfile.open(tmp_msg_local_store_name, std::ios::out|std::ios::app);
            outfile << event_msg << std::endl;
            outfile.close();
            g_free(event_msg);
            if (std::rename(tmp_msg_local_store_name.c_str(), msg_local_store_name.c_str())) {
                std::cout << "Error renaming file from " << tmp_msg_local_store_name <<  " to " << msg_local_store_name << " !!!" << std::endl;
            }
            if (src_data) {
                free(src_data);
                src_data = nullptr;
            }
        } else {
            json_array_unref(objectArray);
        }
    }
}
/**
 * check the defined function
 * @param func the funtion name which defined above
 */
CHECK_SAC_CUSTOM_PROBE_FUNC_PROTOTYPE(SACCustomProbeMessageSender);
