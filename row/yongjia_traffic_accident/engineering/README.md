traffic_accident(交通事故 pipeline)



编译和启动方式

    1. 编译告警逻辑插件
        sh make_probe.sh && cmake .. && make
        生成deepstream-app-ext和libs目录中的动态库
    2. 编译跌倒检测后处理插件
        cd export && sh make_probe.sh && cd yolov5s6.0_detect
        mkdir build && cmake .. && make
        拷贝build下面的 libnvds_yolov5_detect.so 至根目录下build目录下的libs即可
    3. 模型
        [车辆检测模型地址]
        http://lingchengkun_data.gz4oss.xstore.ctyun.cn/永嘉温州项目模型/rcfphf0105_best.plan
        [交通事故分类模型地址]
        http://lingchengkun_data.gz4oss.xstore.ctyun.cn/永嘉温州项目模型/accident_resnet50_b8_fp16_20240411.plan
        将上面车辆检测模型和交通事故分类模型添加到build/config目录下。
    4. 配置文件的相关信息
        [application]
        enable-perf-measurement=1
        perf-measurement-interval-sec=5

        [source0]
        camera-id=588
        select-rtp-protocol=4
        uri=file:/root/ibox/deepstream-app/video/VIDEO/positive_new/source_14.mp4
        enable=1
        type=2
        gpu-id=0
        cudadec-memtype=0
        num-sources=1
        rtsp-reconnect-interval-sec=5
        rtsp-reconnect-attempts=-1
        #drop-frame-interval=5
        smart-record=1
        smart-rec-cache=25
        smart-rec-duration=10
        smart-rec-start-time=10
        smart-rec-container=0
        smart-rec-file-prefix=fall
        smart-rec-dir-path=/data

        [sink0]
        enable=1
        type=1
        sync=0
        source-id=0
        gpu-id=0
        nvbuf-memory-type=0

        [streammux]
        gpu-id=0
        live-source=1
        batch-size=1
        batched-push-timeout=40000
        width=1920
        height=1080
        enable-padding=0
        nvbuf-memory-type=0
        attach-sys-ts-as-ntp=1

        [primary-gie]
        enable=1
        gpu-id=0
        batch-size=8
        bbox-border-color0=1;0;0;1
        bbox-border-color1=0;1;1;1
        bbox-border-color2=0;0;1;1
        bbox-border-color3=0;1;0;1
        bbox-border-color4=1;1;0;1
        interval=0
        gie-unique-id=1
        nvbuf-memory-type=0
        config-file=config_infer_primary_car_detect.txt

        [tracker]
        enable=1
        tracker-width=640
        tracker-height=384
        ll-config-file=config_tracker_NvDCF_perf.yml
        ll-lib-file=/opt/nvidia/deepstream/deepstream/lib/libnvds_nvmultiobjecttracker.so
        enable-batch-process=1
        enable-past-frame=0
        display-tracking-id=1

        [custom-probe]
        enable=1
        debug=0
        custom-func=SACCustomProbeMessageSender
        custom-lib=../libs/libsac_custom_probe_message_sender.so
        #区域灵敏度
        roi-sensitivity=0.6
        #检测持续时间
        detect-duration=15
        #检测比例灵敏度
        detect-sensitivity=0.7
        #运行时间(开始)
        running-time-start=-1
        #运行时间(结束)
        running-time-end=-1
        #图片编码质量
        image-encoding-quality=75
        #报警间隔
        warning-interval=300
        #报警次数
        warning-times=-1
        #是否开启录制
        record=1
        #判断是否是拥堵时长
        t1=30
        #判断目标是否是疑似交通事故时长
        t2=60
        #静止的机动车数量 用来判定是否拥堵
        th-countb=5
        task-id=142
        model-name=traffic_accident

        [nvds-analytics]
        enable=1
        config-file=config_nvdsanalytics.txt
    5. 配置感兴趣区域
        [property]
        enable            = 1
        config-width      = 1920
        config-height     = 1080
        osd-mode          = 2
        display-font-size = 20

        [roi-filtering-stream-0]
        enable = 1
        #需至少三个点 顺时针逆时针顺序均可
        roi-0   = 0;0;0;1079;1919;1079;1919;0
        inverse-roi=0
        class-id = -1
    6. 启动
        # 启动之前需要自己配置视频流地址
        sh docker_run.sh && ./deepstream-app-ext -c config/deepstream_app_ext.txt
        输出消息demo
         {
              "version" : "1.0",
              "task" : {
                "model_name" : "traffic_accident",
                "task_id" : "142"
              },
              "events" : {
                "camera_id" : "588",
                "timestamp" : 1714030394780,
                "image" : {
                  "path" : "0_160_1920x1080_1714030393261355000.png",
                  "uri" : "file:/root/ibox/deepstream-app/video/0050.mp4",
                  "width" : 1920,
                  "height" : 1080
                },
                "video" : {
                  "path" : "",
                  "width" : 1920,
                  "height" : 1080
                },
                "objects" : [
                  {
                    "roi_label" : "0",
                    "roi_point_num" : 12,
                    "roi_box" : {
                      "roi_point_0" : 344,
                      "roi_point_1" : 395,
                      "roi_point_2" : 84,
                      "roi_point_3" : 901,
                      "roi_point_4" : 1007,
                      "roi_point_5" : 920,
                      "roi_point_6" : 1019,
                      "roi_point_7" : 534,
                      "roi_point_8" : 1232,
                      "roi_point_9" : 426,
                      "roi_point_10" : 1083,
                      "roi_point_11" : 256
                    },
                    "object_label" : "vehicle",
                    "detect_conf" : 0.93644899129867554,
                    "track_id" : "2",
                    "object_box" : {
                      "x" : 0.35237720608711243,
                      "y" : 0.33770382404327393,
                      "width" : 0.12385069578886032,
                      "height" : 0.26632934808731079
                    }
                  },
                  {
                    "roi_label" : "0",
                    "roi_point_num" : 12,
                    "roi_box" : {
                      "roi_point_0" : 344,
                      "roi_point_1" : 395,
                      "roi_point_2" : 84,
                      "roi_point_3" : 901,
                      "roi_point_4" : 1007,
                      "roi_point_5" : 920,
                      "roi_point_6" : 1019,
                      "roi_point_7" : 534,
                      "roi_point_8" : 1232,
                      "roi_point_9" : 426,
                      "roi_point_10" : 1083,
                      "roi_point_11" : 256
                    },
                    "object_label" : "vehicle",
                    "detect_conf" : 0.95258253812789917,
                    "track_id" : "3",
                    "object_box" : {
                      "x" : 0.46362733840942383,
                      "y" : 0.32569313049316406,
                      "width" : 0.14663591980934143,
                      "height" : 0.20893910527229309
                    }
                  },
                  {
                    "roi_label" : "0",
                    "roi_point_num" : 12,
                    "roi_box" : {
                      "roi_point_0" : 344,
                      "roi_point_1" : 395,
                      "roi_point_2" : 84,
                      "roi_point_3" : 901,
                      "roi_point_4" : 1007,
                      "roi_point_5" : 920,
                      "roi_point_6" : 1019,
                      "roi_point_7" : 534,
                      "roi_point_8" : 1232,
                      "roi_point_9" : 426,
                      "roi_point_10" : 1083,
                      "roi_point_11" : 256
                    },
                    "object_label" : "vehicle",
                    "detect_conf" : 0.96821147203445435,
                    "track_id" : "4",
                    "object_box" : {
                      "x" : 0.53820222616195679,
                      "y" : 0.40235495567321777,
                      "width" : 0.30830848217010498,
                      "height" : 0.46361520886421204
                    }
                  }
                ]
              }
            }


