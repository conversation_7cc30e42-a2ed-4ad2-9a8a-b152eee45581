import glob
import os
import sys
import time
from io import BytesIO
import logging
import cv2
import gevent
import gevent.ssl
import numpy as np
import pybase64 as base64
import tritonclient.http as httpclient
from PIL import Image
import yaml
from teleexception import StatusException, HTTPStatus
import fleep
from icecream import ic

logger = logging.getLogger(__name__)

CONFIG_YAML_PATH = "../../config.yaml"
LOCK_PATH = f"/tmp/init_logger"

with open(
    os.path.join(os.path.dirname(__file__), CONFIG_YAML_PATH), "r", encoding="utf-8"
) as f:
    configs = yaml.load(f, Loader=yaml.FullLoader)
    if not os.path.exists(LOCK_PATH) or not os.path.isdir(LOCK_PATH):
        os.makedirs(LOCK_PATH, exist_ok=True)
        logger.info(f"Init, {configs}")

action_configs = configs["action"]  # ACTION
port_configs = configs["ports"]  # 端口配置
models_configs = configs["models"]  # 模型参数配置
process_configs = configs["process"]  # 运行参数配置
params_configs = configs["params"]  # 系统参数配置

# 垃圾检测 端口
TRITON_URL = os.environ.get(
    "TRITON_URL", "localhost:{}".format(port_configs["TRITON_PORT"])
)

# 垃圾检测 模型配置
rubbish_config = models_configs["rubbish"]


class DetectClient:
    def __init__(self, url, model_config):
        self.model_config = model_config
        try:
            if self.model_config["ssl"]:
                self.triton_client = httpclient.InferenceServerClient(
                    url=url,
                    verbose=self.model_config["verbose"],
                    ssl=True,
                    ssl_context_factory=gevent.ssl._create_unverified_context,
                    insecure=True,
                )
            else:
                self.triton_client = httpclient.InferenceServerClient(
                    url=url, verbose=self.model_config["verbose"]
                )
        except Exception as e:
            logger.info("channel creation failed: " + str(e))
            sys.exit(1)
        if self.model_config["http_headers"] is not None:
            self.headers = {
                l.split(":")[0]: l.split(":")[1]
                for l in self.model_config["http_headers"]
            }
        else:
            self.headers = None
        self.model_name = self.model_config["model_name"]
        self.request_compression_algorithm = self.model_config[
            "request_compression_algorithm"
        ]
        self.response_compression_algorithm = self.model_config[
            "response_compression_algorithm"
        ]

    def run(self, input_data):
        inputs = [
            httpclient.InferInput(
                input_info["name"], input_data.shape, input_info["dtype"]
            )
            for input_info in self.model_config["input"]
        ]
        inputs[0].set_data_from_numpy(input_data, binary_data=True)

        outputs = [
            httpclient.InferRequestedOutput(output_info["name"], binary_data=True)
            for output_info in self.model_config["output"]
        ]
        query_params = {"test_1": 1, "test_2": 2}
        results = self.triton_client.async_infer(
            self.model_name,
            inputs,
            outputs=outputs,
            query_params=query_params,
            headers=self.headers,
            request_compression_algorithm=self.request_compression_algorithm,
            response_compression_algorithm=self.response_compression_algorithm,
        )
        return results


# 垃圾检测 模型客户端
rubbish_client = DetectClient(TRITON_URL, rubbish_config)


class RubbishDetectionClient:
    def __init__(self, process_configs, reqid=None):
        self.process_configs = process_configs
        self.save_rule = params_configs["SAVE_RULE"]  # 默认保存规则 all
        self.reqid = reqid

    def letterbox(
        self,
        img,
        new_shape=(640, 640),
        color=(0, 0, 0),
        auto=False,
        scaleFill=False,
        scaleup=True,
        stride=32,
    ):
        # Resize and pad image while meeting stride-multiple constraints
        shape = img.shape[:2]  # current shape [height, width]

        if isinstance(new_shape, int):
            new_shape = (new_shape, new_shape)

        # Scale ratio (new / old)
        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
        # only scale down, do not scale up (for better test mAP)
        if not scaleup:
            r = min(r, 1.0)

        # Compute padding
        ratio = r, r  # width, height ratios
        new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
        dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # wh padding
        if auto:  # minimum rectangle
            dw, dh = np.mod(dw, stride), np.mod(dh, stride)  # wh padding
        elif scaleFill:  # stretch
            dw, dh = 0.0, 0.0
            new_unpad = (new_shape[1], new_shape[0])
            ratio = (
                new_shape[1] / shape[1],
                new_shape[0] / shape[0],
            )  # width, height ratios

        dw /= 2  # divide padding into 2 sides
        dh /= 2

        if shape[::-1] != new_unpad:  # resize
            img = cv2.resize(img, new_unpad, interpolation=cv2.INTER_LINEAR)
        top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
        left, right = int(round(dw - 0.1)), int(round(dw + 0.1))

        img = cv2.copyMakeBorder(
            img, 0, bottom + top, 0, right + left, cv2.BORDER_CONSTANT, value=color
        )  # add border

        return img, ratio, (0, 0)
    
    def patch(self, bboxes, scores, classes):
        x1 = bboxes[:, 0]
        y1 = bboxes[:, 1]
        x2 = bboxes[:, 2]
        y2 = bboxes[:, 3]
        scores = np.array(scores)
        order = scores.argsort()[::-1]  # 0,1,2,3
        areas = (x2 - x1 + 1) * (y2 - y1 + 1)
        keep = []

        while order.size > 0:
            i = order[0]
            keep.append(i)
            xx1 = np.maximum(x1[i], x1[order[1:]])
            yy1 = np.maximum(y1[i], y1[order[1:]])
            xx2 = np.minimum(x2[i], x2[order[1:]])
            yy2 = np.minimum(y2[i], y2[order[1:]])
            inter = np.maximum(0.0, xx2 - xx1 + 1) * np.maximum(0.0, yy2 - yy1 + 1)
            iou = inter / (areas[i] + areas[order[1:]] - inter)
            inds = np.where(iou <= process_configs["IOU_THRESH_ALL"])[0]
            order = order[inds + 1]

        bboxes = bboxes[keep]
        scores = scores[keep]
        classes = classes[keep]
        count = len(keep)

        return count, bboxes, scores, classes

        # box_dict = {}
        # score_dict = {}
        # for cls in process_configs["KEEP_CLS_LIST"]:
        #     score_dict[cls] = []
        #     box_dict[cls] = []

        # for idx, cls in enumerate(classes):
        #     box_dict.get(cls).append(bboxes[idx])
        #     score_dict.get(cls).append(scores[idx])
            
        # for cls in process_configs["KEEP_CLS_LIST"]:
        #     box_dict[cls] = np.array(box_dict.get(cls))
        #     score_dict[cls] = np.array(score_dict.get(cls))
        
        # bbox_res = []
        # score_res = []
        # class_res = []
        # for cls in process_configs["KEEP_CLS_LIST"]:
        #     bbox_cls = box_dict.get(cls)
        #     score_cls = score_dict.get(cls)
        #     if len(bbox_cls) > 0 and len(score_cls) > 0:

        #         x1 = bbox_cls[:, 0]
        #         y1 = bbox_cls[:, 1]
        #         x2 = bbox_cls[:, 2]
        #         y2 = bbox_cls[:, 3]

        #         score_cls = np.array(score_cls)
        #         order = score_cls.argsort()[::-1]  # 按照分数排序
        #         areas = (x2 - x1 + 1) * (y2 - y1 + 1)
        #         keep = []

        #         while order.size > 0:
        #             i = order[0]
        #             keep.append(i)
        #             xx1 = np.maximum(x1[i], x1[order[1:]])
        #             yy1 = np.maximum(y1[i], y1[order[1:]])
        #             xx2 = np.minimum(x2[i], x2[order[1:]])
        #             yy2 = np.minimum(y2[i], y2[order[1:]])
        #             inter = np.maximum(0.0, xx2 - xx1 + 1) * np.maximum(0.0, yy2 - yy1 + 1)
        #             iou = inter / (areas[i] + areas[order[1:]] - inter)
        #             inds = np.where(iou <= process_configs["IOU_THRESH"][int(cls)])[0]
        #             order = order[inds + 1]
                    
        #         # logger.info(f"keep: {keep}")

        #         for kp in keep:
        #             bbox_res.append(bbox_cls[kp])
        #             score_res.append(score_cls[kp])
        #             class_res.append(cls)

        #         # bbox_cls += bbox_cls[keep]
        #         # score_cls += score_cls[keep]
        #         # class_res += [cls]
            
        # bboxes = np.array(bbox_res)
        # scores = np.array(score_res)
        # classes = np.array(class_res)
        # # logger.info("filtering")
        # logger.info(f"bboxes: {bboxes}")
        # logger.info(f"scores: {scores}")
        # logger.info(f"classes: {classes}")


        # x1 = bboxes[:, 0]
        # y1 = bboxes[:, 1]
        # x2 = bboxes[:, 2]
        # y2 = bboxes[:, 3]
        # # scores = np.array(scores)
        # order = scores.argsort()[::-1]  # 按照分数排序
        # areas = (x2 - x1 + 1) * (y2 - y1 + 1)
        # keep = []

        # while order.size > 0:
        #     i = order[0]
        #     keep.append(i)
        #     xx1 = np.maximum(x1[i], x1[order[1:]])
        #     yy1 = np.maximum(y1[i], y1[order[1:]])
        #     xx2 = np.minimum(x2[i], x2[order[1:]])
        #     yy2 = np.minimum(y2[i], y2[order[1:]])
        #     inter = np.maximum(0.0, xx2 - xx1 + 1) * np.maximum(0.0, yy2 - yy1 + 1)
        #     iou = inter / (areas[i] + areas[order[1:]] - inter)
        #     inds = np.where(iou <= process_configs["IOU_THRESH_ALL"])[0]
        #     order = order[inds + 1]

        # bboxes = bboxes[keep]
        # scores = scores[keep]
        # classes = classes[keep]
        # count = len(keep)

        # return count, bboxes, scores, classes

    def _preprocess(self, input_image):
        img = self.letterbox(input_image)[0]

        # img = img[:, :, ::-1].transpose(2, 0, 1)  # BGR to RGB
        img = img.transpose(2, 0, 1)  # 已经是 RGB
        img = np.ascontiguousarray(img)

        img = img.astype(np.float32)
        # img /= 255.0  # 0 - 255 to 0.0 - 1.0

        if len(img) == 3:
            img = np.expand_dims(img, axis=0)

        return img

    def _postprocess(self, detect_results, image_h, image_w, input_h, input_w):
        counts = detect_results.as_numpy("count")[0]
        boxes = detect_results.as_numpy("box")[0]
        scores = detect_results.as_numpy("score")[0]
        classes = detect_results.as_numpy("class")[0]

        num_dets = counts.squeeze()
        boxes = boxes[:num_dets]
        scores = scores[:num_dets]
        classes = classes[:num_dets]

        # IOU 过滤
        counts, boxes, scores, classes = self.patch(boxes, scores, classes)

        ratio = (
            image_h / input_h
            if image_h / input_h > image_w / input_w
            else image_w / input_w
        )
        boxes *= ratio
        boxes = boxes.astype(np.int32)
        classes = classes.astype(np.int32)

        filter_boxes = []
        filter_scores = []
        filter_classes = []
        for i in range(len(classes)):
            # 类别过滤
            if classes[i] not in process_configs["KEEP_CLS_LIST"]:
                logger.info(
                    f"Reqid: {self.reqid}, rubbish is filtered by class: {classes[i]}"
                )
                continue

            # 阈值过滤
            THRESH = float(process_configs["CONF_THRESH"][int(classes[i])])
            if scores[i] < THRESH:
                logger.info(
                    f"Reqid: {self.reqid}, score: {scores[i]:.3f}, rubbish is filtered by thresh: {THRESH}"
                )
                continue

            # 检测框比例过滤
            box_w = (boxes[i][2] - boxes[i][0]) / self.image_w
            box_h = (boxes[i][3] - boxes[i][1]) / self.image_h
            min_w = process_configs["BOX_THRESH_RATE_DICT"]["BOX_MIN_W_RATE"]
            max_w = process_configs["BOX_THRESH_RATE_DICT"]["BOX_MAX_W_RATE"]
            min_h = process_configs["BOX_THRESH_RATE_DICT"]["BOX_MIN_H_RATE"]
            max_h = process_configs["BOX_THRESH_RATE_DICT"]["BOX_MAX_H_RATE"]
            if not (min_w < box_w < max_w and min_h < box_h < max_h):
                logger.info(
                    f"Reqid: {self.reqid}, box is filtered by box rate, box_w: {box_w:.3f}, min_w: {min_w:.3f}, max_w: {max_w:.3f}, box_h: {box_h:.3f}, min_h: {min_h:.3f}, max_h: {max_h:.3f}"
                )
                continue

            filter_boxes.append(boxes[i])
            filter_scores.append(scores[i])
            filter_classes.append(classes[i])

        return filter_boxes, filter_scores, filter_classes

    def update_final_result(self, boxes, scores, classes):
        fix_boxes = []
        for box in boxes:
            x1, y1, x2, y2 = [int(v) if int(v) > 0 else 0 for v in box]
            new_box = [x1, y1, x2 - x1, y2 - y1]  # x, y, w, h
            fix_boxes.append(new_box)
        
        fix_scores = [round(float(score), 4) for score in scores]

        cls_map = process_configs["CLS_MAP_CN"]
        fix_classes = [cls_map[int(cls)] for cls in classes]

        final_result = {
            "DetectCount": len(boxes),
            "DetectBoxes": fix_boxes,
            "DetectScores": fix_scores,
            "DetectClses": fix_classes,
        }

        # 检测框 <= 0 不保存
        if len(boxes) <= params_configs["SAVE_NUM"]:
            self.save_rule = "none"

        logger.info(
            f"Reqid: {self.reqid}, DetectCount:{len(boxes)}, SaveRule:{self.save_rule}"
        )

        return final_result, self.save_rule

    def run(self, img_arr, img_mat):
        logger.info('start process')
        logger.info(f'{type(img_mat)}')
        # 参数初始化
        self.image_h, self.image_w = img_mat.shape[:2]  # 图片长宽 opencv
        logger.info(
            f"Reqid: {self.reqid}, image_h: {self.image_h}, image_w: {self.image_w}"
        )
        
        # 模型推理
        input_data = self._preprocess(img_mat)
        rubbish_results = rubbish_client.run(input_data)
        rubbish_detect_results = rubbish_results.get_result()
        # 后处理
        boxes, scores, classes = self._postprocess(
            rubbish_detect_results,
            self.image_h,
            self.image_w,
            rubbish_config["input_shape"][0]["input_h"],
            rubbish_config["input_shape"][0]["input_w"],
        )

        return self.update_final_result(boxes, scores, classes)


class FileFormatValidation:
    @staticmethod
    def validate(file: bytes, mime_matches):
        if file is None or len(file) <= 128:
            return False

        info = fleep.get(file[:128])
        for mime in mime_matches:
            if info.mime_matches(mime):
                return True
        return False

    @staticmethod
    def convert_to_png(self):
        im = Image.open(BytesIO(self.file))
        byte_io = BytesIO()
        im.save(byte_io, "PNG")
        self.cleaned_image = byte_io.getvalue()


def check_params(reqid, params):
    # 请求体字典
    if not isinstance(params, dict):
        logger.info(f"Reqid: {reqid}, body type err, return code: 400005")
        raise StatusException(HTTPStatus.BODY_TYPE_ERR)

    # Action and ImageData
    Action = params.get("Action", None)
    ImageData = params.get("ImageData", None)

    if Action is None or ImageData is None:
        logger.info(f"Reqid: {reqid}, Action or ImageData missing, return code: 400006")
        raise StatusException(HTTPStatus.MUST_PRAM_ERR)
    if not isinstance(Action, str) or not isinstance(ImageData, str):
        logger.info(
            f"Reqid: {reqid}, Action or ImageData type err, return code: 400008"
        )
        raise StatusException(HTTPStatus.PRAM_TYPE_ERR)
    if not Action or not ImageData:
        logger.info(f"Reqid: {reqid}, Action or ImageData empty, return code: 400009")
        raise StatusException(HTTPStatus.IMAGE_DATA_AND_ACTION_EMPTY_ERR)
    if Action != action_configs:  # ACTION
        logger.info(f"Reqid: {reqid}, Action value err, return code: 400010")
        raise StatusException(HTTPStatus.ACTION_VALUE_ERR)

    # base 解码
    try:
        img_byte = base64.urlsafe_b64decode(ImageData)
    except Exception as e:
        logger.info(
            f"Reqid: {reqid}, decode image base64 err: {e}, return code: 400011"
        )
        raise StatusException(HTTPStatus.IMAGE_DATA_BASE64_ERR)

    # 图片格式
    if not FileFormatValidation.validate(img_byte, params_configs["FILE_FORMAT"]):
        logger.info(f"Reqid: {reqid}, image format err, return code: 400012")
        raise StatusException(HTTPStatus.IMAGE_TYPE_ERR)

    # 图片大小
    if len(img_byte) > params_configs["IMAGE_SIZE"]:
        logger.info(f"Reqid: {reqid}, image size err, return code: 400013")
        raise StatusException(HTTPStatus.IMAGE_SIZE_ERR)

    # 图片解码
    try:
        img_arr = np.frombuffer(img_byte, np.uint8)
        img_mat = cv2.cvtColor(cv2.imdecode(
            img_arr, cv2.IMREAD_COLOR), cv2.COLOR_BGR2RGB)
        height, width = img_mat.shape[:2]
        # img_mat = Image.open(BytesIO(img_byte)).convert("RGB")
        # width, height = img_mat.size
    except Exception as e:
        logger.info(f"Reqid: {reqid}, decode image err: {e}, return code: 410001")
        raise StatusException(HTTPStatus.IMAGE_DECODE_ERR)

    # 图片长宽
    if not (
        params_configs["MIN_LEN"] <= height <= params_configs["MAX_LEN"]
        and params_configs["MIN_LEN"] <= width <= params_configs["MAX_LEN"]
    ):
        logger.info(f"Reqid: {reqid}, image shape err, return code: 410002")
        raise StatusException(HTTPStatus.IMAGE_SHAPE_ERR)

    return img_arr, img_mat


def run(reqid, params):
    time_0 = time.time()
    img_arr, img_mat = check_params(reqid, params)
    client = RubbishDetectionClient(process_configs, reqid)

    time_1 = time.time()
    results, save_rule = client.run(img_arr, img_mat)
    time_2 = time.time()
    
    logger.info(
        f"Reqid: {reqid}, check params: {(time_1 - time_0) * 1000} ms, infer time: {(time_2 - time_1) * 1000} ms"
    )

    return results, save_rule


if __name__ == "__main__":
    logging.basicConfig(
        level=logging.DEBUG,
        format="%(asctime)s %(levelname)s %(filename)s:%(lineno)d - %(message)s",
    )

    client = RubbishDetectionClient(process_configs)

    image_paths = glob.glob("../../test/*.jpg")

    for idx, image_path in enumerate(image_paths):
        with open(image_path, "rb") as file:
            img_byte = file.read()
        img_arr = np.frombuffer(img_byte, np.uint8)
        img_mat = Image.open(BytesIO(img_byte)).convert("RGB")

        results, _ = client.run(img_arr, img_mat)
        ic(idx, image_path, results)
