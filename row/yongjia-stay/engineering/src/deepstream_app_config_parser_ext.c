
#include "deepstream_config.h"
#include "deepstream_common.h"
#include "deepstream_config_file_parser.h"
#include "deepstream_app_config_parser_ext.h"

#include <dlfcn.h>

#define CONFIG_GROUP_PROBE "custom-probe"
#define CONFIG_GROUP_PROBE_ENABLE "enable"
#define CONFIG_GROUP_PROBE_TYPE "type"
#define CONFIG_GROUP_PROBE_CUSTOM_LIB "custom-lib"
#define CONFIG_GROUP_PROBE_CUSTOM_FUNC "custom-func"
#define CONFIG_GROUP_PROBE_WORKSPACE_ID "workspace-id"
#define CONFIG_GROUP_PROBE_ZONE_ID "zone-id"
#define CONFIG_GROUP_PROBE_BOX_ID "box-id"
#define CONFIG_GROUP_PROBE_TASK_ID "task-id"
#define CONFIG_GROUP_PROBE_ID "id"
#define CONFIG_GROUP_PROBE_MODEL_NAME "model-name"
#define CONFIG_GROUP_PROBE_MODEL_ID "model-id"
#define CONFIG_GROUP_PROBE_EVT_INFO "evt-info"

#define CONFIG_GROUP_PROBE_DETECT_DURATION "detect-duration"
#define CONFIG_GROUP_PROBE_DETECT_SENSITIVITY "detect-sensitivity"
#define CONFIG_GROUP_PROBE_ROI_SENSITIVITY "roi-sensitivity"
#define CONFIG_GROUP_PROBE_WARNING_INTERVAL "warning-interval"
#define CONFIG_GROUP_PROBE_WARNING_TIMES "warning-times"

#define CONFIG_GROUP_PROBE_DEBUG "debug"

#define CONFIG_GROUP_SRC_PROBE "custom-src-probe"
#define CONFIG_GROUP_SRC_PROBE_ENABLE "enable"
#define CONFIG_GROUP_SRC_PROBE_PROTO_LIB "proto-lib"
#define CONFIG_GROUP_SRC_PROBE_CONN_STR "conn-str"
#define CONFIG_GROUP_SRC_PROBE_TOPIC "topic"

GST_DEBUG_CATEGORY_EXTERN (APP_CFG_PARSER_CAT);

#define CHECK_ERROR(error) \
    if (error) { \
        GST_CAT_ERROR (APP_CFG_PARSER_CAT, "%s", error->message); \
        goto done; \
    }

gboolean
parse_probe (SACProbeConfig *config, GKeyFile *key_file, gchar *cfg_file_path)
{
    gboolean ret = FALSE;
    gchar **keys = NULL;
    gchar **key = NULL;
    GError *error = NULL;

    keys = g_key_file_get_keys (key_file, CONFIG_GROUP_PROBE, NULL, &error);
    CHECK_ERROR (error);

    for (key = keys; *key; key++) {
        if (!g_strcmp0 (*key, CONFIG_GROUP_PROBE_ENABLE)) {
            config->enable = g_key_file_get_integer (key_file, CONFIG_GROUP_PROBE,
                                            CONFIG_GROUP_PROBE_ENABLE, &error);
            CHECK_ERROR (error);
        // } else if (!g_strcmp0 (*key, CONFIG_GROUP_PROBE_TYPE)) {
        //     config->type = g_key_file_get_integer (key_file, CONFIG_GROUP_PROBE,
        //             CONFIG_GROUP_PROBE_TYPE, &error);
        //     CHECK_ERROR (error);
        } else if (!g_strcmp0 (*key, CONFIG_GROUP_PROBE_CUSTOM_LIB)) {
            config->custom_lib = get_absolute_file_path (cfg_file_path,
                    g_key_file_get_string (key_file, CONFIG_GROUP_PROBE,
                            CONFIG_GROUP_PROBE_CUSTOM_LIB, &error));
            CHECK_ERROR (error);
        } else if (!g_strcmp0 (*key, CONFIG_GROUP_PROBE_CUSTOM_FUNC)) {
            config->custom_func = g_key_file_get_string (key_file, CONFIG_GROUP_PROBE,
                    CONFIG_GROUP_PROBE_CUSTOM_FUNC, &error);
            CHECK_ERROR (error);
        // } else if (!g_strcmp0 (*key, CONFIG_GROUP_PROBE_WORKSPACE_ID)) {
        //     config->workspace_id = g_key_file_get_string (key_file, CONFIG_GROUP_PROBE,
        //             CONFIG_GROUP_PROBE_WORKSPACE_ID, &error);
        //     CHECK_ERROR (error);
        // } else if (!g_strcmp0 (*key, CONFIG_GROUP_PROBE_ZONE_ID)) {
        //     config->zone_id = g_key_file_get_string (key_file, CONFIG_GROUP_PROBE,
        //             CONFIG_GROUP_PROBE_ZONE_ID, &error);
        //     CHECK_ERROR (error);
        // } else if (!g_strcmp0 (*key, CONFIG_GROUP_PROBE_BOX_ID)) {
        //     config->box_id = g_key_file_get_string (key_file, CONFIG_GROUP_PROBE,
        //             CONFIG_GROUP_PROBE_BOX_ID, &error);
        //     CHECK_ERROR (error);
        } else if (!g_strcmp0 (*key, CONFIG_GROUP_PROBE_TASK_ID)) {
            config->task_id = g_key_file_get_string (key_file, CONFIG_GROUP_PROBE,
                    CONFIG_GROUP_PROBE_TASK_ID, &error);
            CHECK_ERROR (error);
        } else if (!g_strcmp0 (*key, CONFIG_GROUP_PROBE_ID)) {
            config->id = g_key_file_get_string (key_file, CONFIG_GROUP_PROBE,
                    CONFIG_GROUP_PROBE_ID, &error);
            CHECK_ERROR (error);
        } else if (!g_strcmp0 (*key, CONFIG_GROUP_PROBE_MODEL_NAME)) {
            config->model_name = g_key_file_get_string (key_file, CONFIG_GROUP_PROBE,
                    CONFIG_GROUP_PROBE_MODEL_NAME, &error);
            CHECK_ERROR (error);
        // } else if (!g_strcmp0 (*key, CONFIG_GROUP_PROBE_MODEL_ID)) {
        //     config->model_id = g_key_file_get_string (key_file, CONFIG_GROUP_PROBE,
        //             CONFIG_GROUP_PROBE_MODEL_ID, &error);
        //     CHECK_ERROR (error);
        // } else if (!g_strcmp0 (*key, CONFIG_GROUP_PROBE_EVT_INFO)) {
        //     config->evt_info = g_key_file_get_string (key_file, CONFIG_GROUP_PROBE,
        //             CONFIG_GROUP_PROBE_EVT_INFO, &error);
        //     CHECK_ERROR (error);
        } else if (!g_strcmp0 (*key, CONFIG_GROUP_PROBE_DETECT_DURATION)) {
            config->detect_duration = g_key_file_get_uint64 (key_file, CONFIG_GROUP_PROBE,
                    CONFIG_GROUP_PROBE_DETECT_DURATION, &error);
            CHECK_ERROR (error);
        } else if (!g_strcmp0 (*key, CONFIG_GROUP_PROBE_DETECT_SENSITIVITY)) {
            config->detect_sensitivity = g_key_file_get_double (key_file, CONFIG_GROUP_PROBE,
                    CONFIG_GROUP_PROBE_DETECT_SENSITIVITY, &error);
            CHECK_ERROR (error);
        } else if (!g_strcmp0 (*key, CONFIG_GROUP_PROBE_ROI_SENSITIVITY)) {
            config->roi_sensitivity = g_key_file_get_double (key_file, CONFIG_GROUP_PROBE,
                    CONFIG_GROUP_PROBE_ROI_SENSITIVITY, &error);
            CHECK_ERROR (error);
        } else if (!g_strcmp0 (*key, CONFIG_GROUP_PROBE_WARNING_INTERVAL)) {
            config->warning_interval = g_key_file_get_uint64 (key_file, CONFIG_GROUP_PROBE,
                    CONFIG_GROUP_PROBE_WARNING_INTERVAL, &error);
            CHECK_ERROR (error);
        } else if (!g_strcmp0 (*key, CONFIG_GROUP_PROBE_WARNING_TIMES)) {
            config->warning_times = g_key_file_get_integer (key_file, CONFIG_GROUP_PROBE,
                    CONFIG_GROUP_PROBE_WARNING_TIMES, &error);
            CHECK_ERROR (error);
        } else if (!g_strcmp0 (*key, CONFIG_GROUP_PROBE_DEBUG)) {
            config->debug = g_key_file_get_integer (key_file, CONFIG_GROUP_PROBE,
                    CONFIG_GROUP_PROBE_DEBUG, &error);
            CHECK_ERROR (error);
        } else {
            NVGSTDS_WARN_MSG_V ("Unknown key '%s' for group [%s]", *key,
                                CONFIG_GROUP_PROBE);
        }
    }

    ret = TRUE;
    done:
    if (error) {
        g_error_free (error);
    }
    if (keys) {
        g_strfreev (keys);
    }
    if (!ret) {
        NVGSTDS_ERR_MSG_V ("%s failed", __func__);
    }
    return ret;
}

gboolean
parse_src_probe(SRCProbeConfig *src_probe_config, GKeyFile *key_file, gchar *cfg_file_path)
{
    gboolean ret = FALSE;
    gchar **keys = NULL;
    gchar **key = NULL;
    GError *error = NULL;

    keys = g_key_file_get_keys (key_file, CONFIG_GROUP_SRC_PROBE, NULL, &error);
    CHECK_ERROR (error);

    for (key = keys; *key; key++) {
        if (!g_strcmp0 (*key, CONFIG_GROUP_SRC_PROBE_ENABLE)) {
            src_probe_config->enable = g_key_file_get_integer (key_file, CONFIG_GROUP_SRC_PROBE,
                                            CONFIG_GROUP_SRC_PROBE_ENABLE, &error);
            CHECK_ERROR (error);
        } else if (!g_strcmp0 (*key, CONFIG_GROUP_SRC_PROBE_PROTO_LIB)) {
            src_probe_config->proto_lib = g_key_file_get_string (key_file, CONFIG_GROUP_SRC_PROBE,
                    CONFIG_GROUP_SRC_PROBE_PROTO_LIB, &error);
            CHECK_ERROR (error);
        } else if (!g_strcmp0 (*key, CONFIG_GROUP_SRC_PROBE_CONN_STR)) {
            src_probe_config->conn_str = g_key_file_get_string (key_file, CONFIG_GROUP_SRC_PROBE,
                    CONFIG_GROUP_SRC_PROBE_CONN_STR, &error);
            CHECK_ERROR (error);
        } else if (!g_strcmp0 (*key, CONFIG_GROUP_SRC_PROBE_TOPIC)) {
            src_probe_config->topic = g_key_file_get_string (key_file, CONFIG_GROUP_SRC_PROBE,
                    CONFIG_GROUP_SRC_PROBE_TOPIC, &error);
            CHECK_ERROR (error);
        } else {
            NVGSTDS_WARN_MSG_V ("Unknown key '%s' for group [%s]", *key,
                                CONFIG_GROUP_SRC_PROBE);
        }
    }

    ret = TRUE;
    done:
    if (error) {
        g_error_free (error);
    }
    if (keys) {
        g_strfreev (keys);
    }
    if (!ret) {
        NVGSTDS_ERR_MSG_V ("%s failed", __func__);
    }
    return ret;
}

gboolean parse_config_file_probe(SACProbeConfig *config, SRCProbeConfig *src_probe_config, gchar *cfg_file_path){
    GKeyFile *cfg_file = g_key_file_new();
    GError *error = NULL;
    gboolean ret = FALSE;

    if (!APP_CFG_PARSER_CAT) {
        GST_DEBUG_CATEGORY_INIT (APP_CFG_PARSER_CAT, "NVDS_CFG_PARSER", 0, NULL);
    }

    if (!g_key_file_load_from_file (cfg_file, cfg_file_path, G_KEY_FILE_NONE,
                                    &error)) {
        GST_CAT_ERROR (APP_CFG_PARSER_CAT, "Failed to load uri file: %s",
                       error->message);
        goto done;
    }

    if (g_key_file_has_group(cfg_file, CONFIG_GROUP_PROBE)){
        if(!parse_probe(config, cfg_file, cfg_file_path)){
            GST_CAT_ERROR (APP_CFG_PARSER_CAT, "Failed to parse '%s' group",
                           CONFIG_GROUP_PROBE);
            goto done;
        }
    }

    if (g_key_file_has_group(cfg_file, CONFIG_GROUP_SRC_PROBE)){
        if(!parse_src_probe(src_probe_config, cfg_file, cfg_file_path)){
            GST_CAT_ERROR (APP_CFG_PARSER_CAT, "Failed to parse '%s' group",
                           CONFIG_GROUP_SRC_PROBE);
            goto done;
        }
    }

    ret = TRUE;
    done:
    if (cfg_file) {
        g_key_file_free (cfg_file);
    }
    if (error) {
        g_error_free (error);
    }
    if (!ret) {
        NVGSTDS_ERR_MSG_V ("%s failed", __func__);
    }
    return ret;
}

gboolean
create_probe_func(SACProbeHandle *handle, SACProbeConfig * config){
    gboolean ret = FALSE;

    if (!handle || !config) return ret;
    if (!config->custom_lib || !config->custom_func) return ret;
    // g_print("111\n");
    handle->lib_custom_handle = dlopen(config->custom_lib, RTLD_LAZY);
    // g_print("222\n");
    if (handle->lib_custom_handle){
        handle->lib_custom_func = (SACCustomProbeFunc) dlsym(handle->lib_custom_handle, config->custom_func);
        ret = TRUE;
    }

    return ret;
}

void destroy_probe_func(SACProbeHandle *handle){
    if (handle){
        if (handle->lib_custom_handle)
            dlclose(handle->lib_custom_handle);
        handle->lib_custom_handle = NULL;
        handle->lib_custom_func = NULL;
    }
}
