# -*- coding: utf-8 -*-
import base64
import json
import requests
import os
import yaml
from icecream import ic

with open("../config.yaml", "r", encoding="utf-8") as f:
    configs = yaml.load(f, Loader=yaml.FullLoader)

# 错误码验证测试脚本


class error_code_verify:
    def __init__(self):
        host = "localhost"
        port = os.environ.get("HTTP_PORT", configs["ports"]["BACKEND_PORT"])

        self.url = "http://{}:{}".format(host, port)

    def make_request(
        self,
        payload,
        path="/predict",
        method="POST",
        headers={"Content-Type": "application/json"},
    ):
        request = requests.Request(
            method, self.url + path, headers=headers, data=payload
        )
        prepped_request = request.prepare()

        with requests.Session() as sess:
            resp = sess.send(prepped_request)
            # print(resp.encoding)
        return resp.content


def b64_content(img_path):
    with open(img_path, "rb") as f:
        content = f.read()
    b64_content = base64.urlsafe_b64encode(content)
    # b64_content = base64.b64encode(content)
    return b64_content.decode()


# 测试案例
def case_test(request, case_list):
    for case in case_list:
        # code 错误码
        case_code = case.get("code")

        # 是否 json 化
        if not case["need_json"]:
            payload = case["payload"]
        else:
            payload = json.dumps(case["payload"])

        # 不存在 headers，正常调用
        if "headers" not in case:
            res = request.make_request(
                payload, path=case["path"], method=case["method"]
            )
        # 存在 headers，但本地环境变量 CONTENT_TYTE 为空，则跳过判断
        elif os.getenv("CONTENT_TYPE") == None:
            continue
        else:  # 存在 headers 且 环境变量 CONTENT_TYTE 不为空，则传入 headers 判断
            res = request.make_request(
                payload,
                path=case["path"],
                method=case["method"],
                headers=case["headers"],
            )

        res = json.loads(res)
        # print(res)

        if "code" not in res:
            ic(case_code, "失败 " + str(res))
            break
        elif res.get("code") == case["code"]:  # 错误码相同
            if "details" in case:  # 需要判断错误详情
                # 错误详情相同
                if res.get("details") == case["details"]:
                    print(case_code, res.get("code"), "成功 " + str(res))
                else:  # 错误详情不同
                    ic(case_code, res.get("code"), "失败 " + str(res))
                    break
            else:  # 不需要判断错误详情
                print(case_code, res.get("code"), "成功 " + str(res))
        else:  # 错误码不同
            ic(case_code, res.get("code"), "失败 " + str(res))
            break


if __name__ == "__main__":
    # 错误码样例
    error_case_list = [
        # ===== 400001 请求路径错误 =====
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                "ImageData": b64_content("test_images/0027.jpg"),
            },
            "need_json": True,
            "code": 400001,
            "details": "请求路径错误",
            "method": "POST",
            "path": "/predict123",  # error path 400001
        },
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                "ImageData": b64_content("test_images/0027.jpg"),
            },
            "need_json": True,
            "code": 400001,
            "details": "请求路径错误",
            "method": "POST",
            "path": "/predict/123",  # error path 400001
        },
        # 请求方法错误-400002，但是 400001 优先级更高
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                "ImageData": b64_content("test_images/0027.jpg"),
            },
            "need_json": True,
            "code": 400001,
            "details": "请求路径错误",
            "method": "GET",  # error GET method 400002
            "path": "/predict123",  # error path 400001
        },
        # 请求体内容为空-400003，但是 400001 优先级更高
        {
            "payload": "",  # empty body 400003
            "need_json": False,
            "code": 400001,
            "details": "请求路径错误",
            "method": "POST",
            "path": "/predict123",  # error path 400001
        },
        # ===== 400002 请求方法错误 =====
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                "ImageData": b64_content("test_images/0027.jpg"),
            },
            "need_json": True,
            "code": 400002,
            "details": "请求方法错误，请使用 POST 请求",
            "method": "GET",  # error GET method 400002
            "path": "/predict",
        },
        # 请求体内容为空-400003，但是 400002 优先级更高
        {
            "payload": "",  # empty body 400003
            "need_json": False,
            "code": 400002,
            "details": "请求方法错误，请使用 POST 请求",
            "method": "GET",  # 400002
            "path": "/predict",
        },
        # ===== 400003 请求体内容为空 =====
        {
            "payload": "",  # empty body 400003
            "need_json": False,
            "code": 400003,
            "details": "请求体请求数据为空，没有包含内容",
            "method": "POST",
            "path": "/predict",
        },
        # 请求体非json格式-400004，但是 400003 优先级更高
        {
            "payload": "",  # empty body 400003
            "need_json": False,
            "code": 400003,
            "details": "请求体请求数据为空，没有包含内容",
            "method": "POST",
            "path": "/predict",
            # content-type not json 400004
            "headers": {"content-type": "multipart/form-data"},
        },
        # ===== 400004 请求体非 json 格式 =====
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                "ImageData": "aa",
            },  # body not json 400004
            "need_json": False,
            "code": 400004,
            "details": "请求体内容需要符合 json 要求",
            "method": "POST",
            "path": "/predict",
        },
        {
            "payload": {
                "Action": "RoadBusinessDetect",
            },  # body not json 400004
            "need_json": False,
            "code": 400004,
            "details": "请求体内容需要符合 json 要求",
            "method": "POST",
            "path": "/predict",
        },
        {
            "payload": {
                "Action": "RoadBusinessDetect",
            },  # body not json 400004
            "need_json": False,
            "code": 400004,
            "details": "请求体内容需要符合 json 要求",
            "method": "POST",
            "path": "/predict",
        },
        # add content-type 判断
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                "ImageData": "aa",
            },
            "need_json": True,
            "code": 400004,
            "details": "请求体内容需要符合 json 要求",
            "method": "POST",
            "path": "/predict",
            # content-type not json 400004
            "headers": {"content-type": "multipart/form-data"},
        },
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                "ImageData": "aa",
            },
            "need_json": True,
            "code": 400004,
            "details": "请求体内容需要符合 json 要求",
            "method": "POST",
            "path": "/predict",
            # content-type not json 400004
            "headers": {"content-type": "application/x-www-form-urlencoded"},
        },
        # ===== 400005 请求体类型错误 =====
        {
            "payload": [
                {
                    "Action": "RoadBusinessDetect",
                    "ImageData": b64_content("test_images/0027.jpg"),
                }
            ],  # body type list 400005
            "need_json": True,
            "code": 400005,
            "details": "请求体需为字典，不能为其他类型",
            "method": "POST",
            "path": "/predict",
        },
        {
            "payload": [
                {
                    "Action": "RoadBusinessDetect",
                }
            ],  # body type list 400005
            "need_json": True,
            "code": 400005,
            "details": "请求体需为字典，不能为其他类型",
            "method": "POST",
            "path": "/predict",
        },
        {
            "payload": [
                {
                    "ImageData": b64_content("test_images/0027.jpg"),
                }
            ],  # body type list 400005
            "need_json": True,
            "code": 400005,
            "details": "请求体需为字典，不能为其他类型",
            "method": "POST",
            "path": "/predict",
        },
        {
            "payload": "aaaaaa",  # body type str 400005
            "need_json": True,
            "code": 400005,
            "details": "请求体需为字典，不能为其他类型",
            "method": "POST",
            "path": "/predict",
        },
        {
            "payload": ["aaaaaa"],  # body type list 400005
            "need_json": True,
            "code": 400005,
            "details": "请求体需为字典，不能为其他类型",
            "method": "POST",
            "path": "/predict",
        },
        # ===== 400006 必传的参数未传 =====
        {
            "payload": {},  # miss Action and ImageData 400006
            "need_json": True,
            "code": 400006,
            "details": "必须的参数（Action、ImageData）未传",
            "method": "POST",
            "path": "/predict",
        },
        {
            "payload": {
                "Action": "RoadBusinessDetect",
            },  # miss ImageData 400006
            "need_json": True,
            "code": 400006,
            "details": "必须的参数（Action、ImageData）未传",
            "method": "POST",
            "path": "/predict",
        },
        {
            "payload": {
                "ImageData": b64_content("test_images/0027.jpg"),
            },  # miss Action 400006
            "need_json": True,
            "code": 400006,
            "details": "必须的参数（Action、ImageData）未传",
            "method": "POST",
            "path": "/predict",
        },
        {
            "payload": {
                "": b64_content("test_images/0027.jpg"),
            },  # miss Action and ImageData 400006
            "need_json": True,
            "code": 400006,
            "details": "必须的参数（Action、ImageData）未传",
            "method": "POST",
            "path": "/predict",
        },
        # 传递非法参数-400007，但是 400006 优先级更高
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                "Image": b64_content("test_images/0027.jpg"),
            },  # miss ImageData 400006
            "need_json": True,
            "code": 400006,
            "details": "必须的参数（Action、ImageData）未传",
            "method": "POST",
            "path": "/predict",
        },
        {
            "payload": {
                "Actionss": "RoadBusinessDetect",
                "ImageData": b64_content("test_images/0027.jpg"),
            },  # miss Action 400006
            "need_json": True,
            "code": 400006,
            "details": "必须的参数（Action、ImageData）未传",
            "method": "POST",
            "path": "/predict",
        },
        {
            "payload": {
                "Image": b64_content("test_images/0027.jpg"),
            },  # miss Action and ImageData 400006
            "need_json": True,
            "code": 400006,
            "details": "必须的参数（Action、ImageData）未传",
            "method": "POST",
            "path": "/predict",
        },
        {
            "payload": {
                "Actionss": "RoadBusinessDetect",
            },  # miss Action and ImageData 400006
            "need_json": True,
            "code": 400006,
            "details": "必须的参数（Action、ImageData）未传",
            "method": "POST",
            "path": "/predict",
        },
        # ===== 400007 传递非法参数 =====
        # 暂时不需要该错误码
        # ===== 400008 请求体的参数字段类型错误 =====
        {
            "payload": {
                "Action": ["RoadBusinessDetect"],  # Action illegal type 400008
                "ImageData": b64_content("test_images/0027.jpg"),
            },
            "need_json": True,
            "code": 400008,
            "details": "Action、ImageData 字段应该是 string 类型",
            "method": "POST",
            "path": "/predict",
        },
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                # ImageData illegal type 400008
                "ImageData": [b64_content("test_images/0027.jpg")],
            },
            "need_json": True,
            "code": 400008,
            "details": "Action、ImageData 字段应该是 string 类型",
            "method": "POST",
            "path": "/predict",
        },
        {
            "payload": {
                "Action": ["RoadBusinessDetect"],  # Action illegal type 400008
                # ImageData illegal type 400008
                "ImageData": [b64_content("test_images/0027.jpg")],
            },
            "need_json": True,
            "code": 400008,
            "details": "Action、ImageData 字段应该是 string 类型",
            "method": "POST",
            "path": "/predict",
        },
        # 请求体的参数字段值为空-400009，但是 400008 优先级更高
        {
            "payload": {
                "Action": ["RoadBusinessDetect"],  # Action illegal type 400008
                "ImageData": "",  # ImageData empty 400009
            },
            "need_json": True,
            "code": 400008,
            "details": "Action、ImageData 字段应该是 string 类型",
            "method": "POST",
            "path": "/predict",
        },
        # 请求体的参数字段值设置错误-400010，但是 400008 优先级更高
        {
            "payload": {
                "Action": "RoadBusinessDetect1234",  # Action value error 400010
                # ImageData illegal type 400008
                "ImageData": [b64_content("test_images/0027.jpg")],
            },
            "need_json": True,
            "code": 400008,
            "details": "Action、ImageData 字段应该是 string 类型",
            "method": "POST",
            "path": "/predict",
        },
        # base64数据处理异常-400011，但是 400008 优先级更高
        {
            "payload": {
                "Action": ["RoadBusinessDetect"],  # Action illegal type 400008
                "ImageData": "aaa",  # base64 error 400011
            },
            "need_json": True,
            "code": 400008,
            "details": "Action、ImageData 字段应该是 string 类型",
            "method": "POST",
            "path": "/predict",
        },
        # ===== 400009 请求体的参数字段值为空 =====
        {
            "payload": {
                "Action": "",  # Action empty 400009
                "ImageData": "",  # ImageData empty 400009
            },
            "need_json": True,
            "code": 400009,
            "details": "Action、ImageData 字段值为空字符",
            "method": "POST",
            "path": "/predict",
        },
        {
            "payload": {
                "Action": "",  # Action empty 400009
                "ImageData": b64_content("test_images/0027.jpg"),
            },
            "need_json": True,
            "code": 400009,
            "details": "Action、ImageData 字段值为空字符",
            "method": "POST",
            "path": "/predict",
        },
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                "ImageData": "",  # ImageData empty 400009
            },
            "need_json": True,
            "code": 400009,
            "details": "Action、ImageData 字段值为空字符",
            "method": "POST",
            "path": "/predict",
        },
        # 请求体的参数字段值设置错误-400010，但是 400009 优先级更高
        {
            "payload": {
                "Action": "RoadBusinessDetect123",  # error Action 400010
                "ImageData": "",  # ImageData empty 400009
            },
            "need_json": True,
            "code": 400009,
            "details": "Action、ImageData 字段值为空字符",
            "method": "POST",
            "path": "/predict",
        },
        # ===== 400010 请求体的参数字段值设置错误 =====
        {
            "payload": {
                "Action": "RoadBusinessDetect1234",  # error Action 400010
                "ImageData": b64_content("test_images/0027.jpg"),
            },
            "need_json": True,
            "code": 400010,
            "details": "Action 值设置错误",
            "method": "POST",
            "path": "/predict",
        },
        # base64 数据处理异常-400011，但是 400010 优先级更高
        {
            "payload": {
                "Action": "RoadBusinessDetect123",  # error Action 400010
                "ImageData": "aaa",  # base64 error 400011
            },
            "need_json": True,
            "code": 400010,
            "details": "Action 值设置错误",
            "method": "POST",
            "path": "/predict",
        },
        # ===== 400011 base64 数据处理异常 =====
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                "ImageData": "aaa",  # base64 error 400011
            },
            "need_json": True,
            "code": 400011,
            "details": "ImageData 字段的 base64 字符串转换字节码异常",
            "method": "POST",
            "path": "/predict",
        },
        # ===== 400012 文件格式不合法 =====
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                # format gif error
                "ImageData": b64_content("test_images/error_code_img/gif.gif"),
            },
            "need_json": True,
            "code": 400012,
            "details": "仅支持 jpeg/png/jpg/bmp 格式",
            "method": "POST",
            "path": "/predict",
        },
        # 文件大小不符合要求-400013，但是 400012 优先级更高
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                "ImageData": b64_content("test_images/error_code_img/img_7m.gif"),
                # gif 400012 / over 7M 400013
            },
            "need_json": True,
            "code": 400012,
            "details": "仅支持 jpeg/png/jpg/bmp 格式",
            "method": "POST",
            "path": "/predict",
        },
        # ===== 400013 文件大小不符合要求 =====
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                # over 7M 400013
                "ImageData": b64_content("test_images/error_code_img/img_7m.png"),
            },
            "need_json": True,
            "code": 400013,
            "details": "该文件大小不符合要求，图片要求小于 7M",
            "method": "POST",
            "path": "/predict",
        },
        # 文件大小不符合要求-410002，但是 400013 优先级更高
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                "ImageData": b64_content("test_images/error_code_img/img_9m_10000.jpg"),
                # over 7M 400013 / over size 410002
            },
            "need_json": True,
            "code": 400013,
            "details": "该文件大小不符合要求，图片要求小于 7M",
            "method": "POST",
            "path": "/predict",
        },
        # ===== 410001 图片解码错误 =====
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                "ImageData": b64_content("test_images/0027.jpg")[:300],
                # image decode error 400014
            },
            "need_json": True,
            "code": 410001,
            "details": "字节码解码为图片错误",
            "method": "POST",
            "path": "/predict",
        },
        # ===== 410002 图片尺寸不符合要求 =====
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                # less over 32 err
                "ImageData": b64_content("test_images/error_code_img/31_31.jpg"),
            },
            "need_json": True,
            "code": 410002,
            "details": "分辨率长宽尺寸应不高于 5000 不低于 32",
            "method": "POST",
            "path": "/predict",
        },
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                # over 5000 err
                "ImageData": b64_content("test_images/error_code_img/5001_5001.jpg"),
            },
            "need_json": True,
            "code": 410002,
            "details": "分辨率长宽尺寸应不高于 5000 不低于 32",
            "method": "POST",
            "path": "/predict",
        },
        # ===== 400008 请求体的参数字段类型错误 ScoreThresh =====
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                "ImageData": b64_content("test_images/0027.jpg"),
                "ScoreThresh": "1",
            },
            "need_json": True,
            "code": 400008,
            "details": "ScoreThresh 字段应该是 float 类型",
            "method": "POST",
            "path": "/predict",
        },
        # ===== 400010 请求体的参数字段值设置错误 ScoreThresh =====
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                "ImageData": b64_content("test_images/0027.jpg"),
                "ScoreThresh": 2,
            },
            "need_json": True,
            "code": 400010,
            "details": "ScoreThresh 字段不符合规范，请参考接口文档说明",
            "method": "POST",
            "path": "/predict",
        },
        # ===== 602001 Roi 不符合规范 =====
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                "ImageData": b64_content("test_images/0027.jpg"),
                "Roi": [[0, 0], [0, 5000], [5000, 0], [5000, 5000]],
            },
            "need_json": True,
            "code": 602001,
            "details": "Roi 不符合规范，应该为凸多边形",
            "method": "POST",
            "path": "/predict",
        },        
    ]

    good_case_list = [
        # 成功 code 0
        # 验证正常图片
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                "ImageData": b64_content("test_images/0027.jpg"),
            },
            "need_json": True,
            "code": 0,
            "method": "POST",
            "path": "/predict",
        },
        # 验证图片尺寸边缘条件32x32
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                "ImageData": b64_content("test_images/error_code_img/32_32.jpg"),
            },
            "need_json": True,
            "code": 0,
            "method": "POST",
            "path": "/predict",
        },
        # 验证图片尺寸边缘条件5000x5000
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                "ImageData": b64_content("test_images/error_code_img/img_5000.jpg"),
            },
            "need_json": True,
            "code": 0,
            "method": "POST",
            "path": "/predict",
        },
        # 验证图片大小，图片6.7M，符合小于 7M 要求
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                "ImageData": b64_content("test_images/error_code_img/img_6m.png"),
            },
            "need_json": True,
            "code": 0,
            "method": "POST",
            "path": "/predict",
        },
        # 验证阈值 ScoreThresh
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                "ImageData": b64_content("test_images/0027.jpg"),
                "ScoreThresh": 0,  # 阈值 0
            },
            "need_json": True,
            "code": 0,
            "method": "POST",
            "path": "/predict",
        },
        # ROI
        {
            "payload": {
                "Action": "RoadBusinessDetect",
                "ImageData": b64_content("test_images/0027.jpg"),
                "Roi": [[0, 0], [0, 500], [1000, 500], [1000, 0]],
            },
            "need_json": True,
            "code": 0,
            "method": "POST",
            "path": "/predict",
        },        
    ]

    verify_Request = error_code_verify()

    single_case = [
        {
            "payload": {
                "Action": ["RoadBusinessDetect"],  # Action type err
                "ImageData": b64_content("test_images/0027.jpg"),
            },
            "need_json": True,
            "code": 400008,
            "method": "POST",
            "path": "/predict",
        },
    ]

    case_test(verify_Request, error_case_list[:])
    case_test(verify_Request, good_case_list[:])
    # case_test(verify_Request, single_case)
