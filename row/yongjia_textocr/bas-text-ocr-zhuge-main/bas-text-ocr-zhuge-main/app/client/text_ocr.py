import logging
import math
import os
import sys

import cv2
import gevent
import gevent.ssl
import numpy as np
import tritonclient.http as httpclient
import yaml

from app.client.dbpost import DBPostProcess

logger = logging.getLogger(__name__)

CONFIG_YAML_PATH = "../../config.yaml"

with open(
        os.path.join(os.path.dirname(__file__), CONFIG_YAML_PATH), "r", encoding="utf-8"
) as f:
    configs = yaml.load(f, Loader=yaml.FullLoader)

port_configs = configs["ports"]  # 端口配置
models_configs = configs["models"]  # 模型参数配置
process_configs = configs["process"]  # 运行参数配置
params_configs = configs["params"]  # 系统参数配置

# 通用文字识别 端口
TRITON_URL = os.environ.get(
    "TRITON_URL", "localhost:{}".format(port_configs["TRITON_PORT"])
)

# dbnet 模型配置
text_dbnet_configs = models_configs["text_detect"]
# ppocr 模型配置
text_ppocr_configs = models_configs["text_ocr"]

# 字典地址
OCR_DICT_PATH = os.path.join(
    os.path.dirname(os.path.abspath(__file__)), process_configs["OCR_DICT_PATH"]
)


class ModelClient:
    def __init__(self, url, model_config):
        self.model_config = model_config
        try:
            if self.model_config["ssl"]:
                self.triton_client = httpclient.InferenceServerClient(
                    url=url,
                    verbose=self.model_config["verbose"],
                    ssl=True,
                    ssl_context_factory=gevent.ssl._create_unverified_context,
                    insecure=True,
                )
            else:
                self.triton_client = httpclient.InferenceServerClient(
                    url=url, verbose=self.model_config["verbose"]
                )
        except Exception as e:
            logger.error("channel creation failed: " + str(e))
            sys.exit(1)
        if self.model_config["http_headers"] is not None:
            self.headers = {
                l.split(":")[0]: l.split(":")[1]
                for l in self.model_config["http_headers"]
            }
        else:
            self.headers = None
        self.model_name = self.model_config["model_name"]
        self.request_compression_algorithm = self.model_config[
            "request_compression_algorithm"
        ]
        self.response_compression_algorithm = self.model_config[
            "response_compression_algorithm"
        ]

    def run(self, input_datas):
        inputs = [
            httpclient.InferInput(
                input_info["name"], input_datas[i].shape, input_info["dtype"]
            )
            for i, input_info in enumerate(self.model_config["input"])
        ]
        for i in range(len(inputs)):
            inputs[i].set_data_from_numpy(input_datas[i], binary_data=True)
        outputs = [
            httpclient.InferRequestedOutput(output_info["name"], binary_data=True)
            for output_info in self.model_config["output"]
        ]
        query_params = {"test_1": 1, "test_2": 2}
        results = self.triton_client.async_infer(
            self.model_name,
            inputs,
            outputs=outputs,
            query_params=query_params,
            headers=self.headers,
            request_compression_algorithm=self.request_compression_algorithm,
            response_compression_algorithm=self.response_compression_algorithm,
        )
        return results


# dbnet 模型客户端
det_client = ModelClient(TRITON_URL, text_dbnet_configs)
# crnn 模型客户端
ocr_client = ModelClient(TRITON_URL, text_ppocr_configs)


class BaseRecLabelDecode(object):
    """Convert between text-label and text-index"""

    def __init__(self, character_dict_path=None, use_space_char=False):
        self.beg_str = "sos"
        self.end_str = "eos"

        self.character_str = []
        if character_dict_path is None:
            self.character_str = "0123456789abcdefghijklmnopqrstuvwxyz"
            dict_character = list(self.character_str)
        else:
            with open(character_dict_path, "rb") as fin:
                lines = fin.readlines()
                for line in lines:
                    line = line.decode('utf-8').strip("\n").strip("\r\n")
                    self.character_str.append(line)
            if use_space_char:
                self.character_str.append(" ")
            dict_character = list(self.character_str)

        dict_character = self.add_special_char(dict_character)
        self.dict = {}
        for i, char in enumerate(dict_character):
            self.dict[char] = i
        self.character = dict_character

    def add_special_char(self, dict_character):
        return dict_character

    def decode(self, text_index, text_prob=None, is_remove_duplicate=False):
        """convert text-index into text-label."""
        result_list = []
        ignored_tokens = self.get_ignored_tokens()
        batch_size = len(text_index)
        for batch_idx in range(batch_size):
            char_list = []
            conf_list = []
            for idx in range(len(text_index[batch_idx])):
                if text_index[batch_idx][idx] in ignored_tokens:
                    continue
                if is_remove_duplicate:
                    # only for predict
                    if (
                            idx > 0
                            and text_index[batch_idx][idx - 1] == text_index[batch_idx][idx]
                    ):
                        continue
                char_list.append(self.character[int(text_index[batch_idx][idx])])
                if text_prob is not None:
                    conf_list.append(text_prob[batch_idx][idx])
                else:
                    conf_list.append(1)
            text = ''.join(char_list)
            result_list.append((text, np.mean(conf_list)))
        return result_list

    def get_ignored_tokens(self):
        return [0]  # for ctc blank


class CTCLabelDecode(BaseRecLabelDecode):
    """Convert between text-label and text-index"""

    def __init__(self, character_dict_path=None, use_space_char=False, **kwargs):
        super(CTCLabelDecode, self).__init__(character_dict_path, use_space_char)

    def __call__(self, preds, label=None, model_type=0, *args, **kwargs):
        # print(len(preds))
        # print(preds)
        if model_type != 2:
            if isinstance(preds, tuple):
                preds = preds[-1]

        if model_type == 0:
            preds_idx = preds.argmax(axis=2)
            preds_prob = preds.max(axis=2)
        elif model_type == 1:
            preds_idx = preds
            preds_prob = None
        elif model_type == 2:
            preds_idx = preds[0]
            preds_prob = preds[1]
        else:
            raise ValueError(
                "model_type should be in (0, 1, 2), but now is {}".format(model_type)
            )

        text = self.decode(preds_idx, preds_prob, is_remove_duplicate=True)
        if label is None:
            return text
        label = self.decode(label)
        return text, label

    def add_special_char(self, dict_character):
        dict_character = ['blank'] + dict_character
        return dict_character


class TextOcrClient:
    def __init__(self, reqid=None):
        self.reqid = reqid
        self.save_rule = params_configs["SAVE_RULE"]  # 默认保存规则 all

        # dbnet
        self.db_input_h = text_dbnet_configs["input_shape"][0]["input_h"]
        self.db_input_w = text_dbnet_configs["input_shape"][0]["input_w"]
        self.det_image_shape = [self.db_input_h, self.db_input_w, 3]
        self.mean = [0.485, 0.456, 0.406]
        self.std = [0.229, 0.224, 0.225]
        db_post_config = {
            "thresh": process_configs["DB_POST_PROC_THRESH"],
            "box_thresh": process_configs["DB_POST_PROC_BOX_THRESH"],
            "max_candidates": process_configs["DB_POST_PROC_MAX_CANDIDATES"],
            "unclip_ratio": process_configs["DB_POST_PROC_UNCLIP_RATIO"],
            "min_size": process_configs["DB_POST_PROC_MIN_SIZE"]
        }
        self.db_post_func = DBPostProcess(db_post_config)

        # ocr
        self.max_ocr_width = process_configs["OCR_MAX_WIDTH"]
        self.rec_img_h = text_ppocr_configs["input_shape"][0]["input_h"]

        self.ctc_decoder = CTCLabelDecode(
            OCR_DICT_PATH, use_space_char=process_configs["USE_SPACE_CHAR"]
        )
        self.rec_model_type = process_configs["REC_MODEL_TYPE"]
        self.batch = 64

    def adjust_box(self, box):
        # 对 x 排序
        x_sorted = box[np.argsort(box[:, 0]), :]
        # 获取最左侧和最右侧点
        # x 坐标
        left_most = x_sorted[:2, :]  # 左边的两个点
        right_most = x_sorted[2:, :]  # 右边的两个点
        # 根据y坐标对最左边的点排序
        left_most = left_most[np.argsort(left_most[:, 1]), :]
        (tl, bl) = left_most  # 左上，左下
        # 根据y坐标对最右边的点排序
        right_most = right_most[np.argsort(right_most[:, 1]), :]
        (tr, br) = right_most  # 右上，右下
        return np.array([tl, tr, br, bl], dtype="int")

    def resize_norm_img(self, img, points, h, w):
        imgW = self.max_ocr_width
        ratio = w / float(h)
        #
        rotflag = 0
        if h * 1.0 / w >= 1.5:
            rotflag = 1
            ratio = h / float(w)
        #
        if math.ceil(self.rec_img_h * ratio) > imgW:
            resized_w = imgW
        else:
            resized_w = int(math.ceil(self.rec_img_h * ratio))
        img_crop_width = resized_w
        img_crop_height = self.rec_img_h
        pts_std = np.float32(
            [
                [0, 0],
                [img_crop_width, 0],
                [img_crop_width, img_crop_height],
                [0, img_crop_height],
            ]
        )
        if rotflag == 1:
            pts_std = np.float32(
                [
                    [0, img_crop_height],
                    [0, 0],
                    [img_crop_width, 0],
                    [img_crop_width, img_crop_height],
                ]
            )
        M = cv2.getPerspectiveTransform(points, pts_std)
        dst_img = cv2.warpPerspective(
            img, M, (img_crop_width, img_crop_height), borderMode=cv2.BORDER_REPLICATE
        )
        dst_img_height, dst_img_width = dst_img.shape[0:2]
        pad_w = imgW - dst_img_width
        pad_h = self.rec_img_h - dst_img_height
        resized_image = cv2.copyMakeBorder(
            dst_img, 0, pad_h, 0, pad_w, cv2.BORDER_CONSTANT, value=[128, 128, 128]
        )
        resized_image = resized_image.astype('float32')
        resized_image = resized_image.transpose((2, 0, 1)) / 255
        resized_image -= 0.5
        resized_image /= 0.5
        return resized_image

    def get_bbox(self, image, predmap):
        outputs = []  # class_id, score, x1, y1, x2, y2, x3, y3, x4, y4
        crops_h = []
        crops_w = []
        points_boxes = []
        image_h, image_w, _ = image.shape
        dbnetboxes, dbnetscores = self.db_post_func(
            predmap, self.db_input_h, self.db_input_w, image_h, image_w
        )
        # self.max_wh_ratio = 0
        for i in range(len(dbnetboxes)):
            box = dbnetboxes[i]
            box = self.adjust_box(box)
            points = box.astype(np.float32)
            w = int(np.linalg.norm(points[0] - points[1]))
            h = int(np.linalg.norm(points[0] - points[3]))
            # wh_ratio = w * 1.0 / h
            # self.max_wh_ratio = max(self.max_wh_ratio, wh_ratio)
            outputs.append(
                [
                    0,
                    dbnetscores[i],
                    box[0, 0],
                    box[0, 1],
                    box[1, 0],
                    box[1, 1],
                    box[2, 0],
                    box[2, 1],
                    box[3, 0],
                    box[3, 1],
                ]
            )
            crops_h.append(h)
            crops_w.append(w)
            points_boxes.append(points)
        return outputs, crops_h, crops_w, points_boxes

    # ocr数据处理
    def process_ocr_data(self, img, crops_h, crops_w, points_boxes):
        # OCR输入图像预处理
        new_crops = []
        for i in range(len(points_boxes)):
            points = points_boxes[i]
            h = crops_h[i]
            w = crops_w[i]
            img_unpad = self.resize_norm_img(img, points, h, w)
            new_crops.append(img_unpad)
        return new_crops

    def get_cor_crop(self, image, dbmap):
        bboxs, crops_h, crops_w, points_boxes = self.get_bbox(image, dbmap)
        ocr_crops = self.process_ocr_data(image, crops_h, crops_w, points_boxes)
        if len(bboxs) == 0:
            bboxs.append([-1, 0, 0, 0, 0, 0, 0, 0, 0, 0])
        return bboxs, ocr_crops

    def _batch(self, iterable, n=1):
        l = len(iterable)
        for ndx in range(0, l, n):
            yield iterable[ndx: min(ndx + n, l)]

    def db_preprocess(self, img_np):
        db_input = np.zeros(self.det_image_shape, dtype=np.float32)
        h, w, c = self.det_image_shape
        img_h, img_w = img_np.shape[:2]

        if len(img_np.shape) == 2:
            img_np = cv2.cvtColor(img_np, cv2.COLOR_GRAY2BGR)

        ratio1 = h / img_h
        ratio2 = w / img_w
        ratio = min(ratio1, ratio2)
        new_h = int(ratio * img_h)
        new_w = int(ratio * img_w)
        resized = cv2.resize(img_np, (new_w, new_h), interpolation=cv2.INTER_LINEAR)

        typed = resized.astype(np.float32)

        typed = typed / 255.0
        scaled = (typed - self.mean) / self.std

        db_input[:new_h, :new_w, :] = scaled

        db_input = np.expand_dims(db_input.transpose(2, 0, 1), axis=0)

        return img_np, db_input

    # 返回值封装
    def update_final_result(self, res_texts, res_text_scores, res_text_boxes):
        # {"TextCount": 1,
        #  "TextBoxes": [[[69, 0], [182, 0], [182, 60], [69, 60]]],
        #  "TextScores": [0.5569],
        #  "Texts": ["荣耀"]}

        details = []
        name_num = 0
        for res_text, res_text_box in zip(res_texts, res_text_boxes):
            box = []
            for x, y in res_text_box:
                box.append(x)
                box.append(y)
            detail = {
                "name": str(name_num),
                "text": res_text,
                "box": box
            }
            details.append(detail)
            name_num += 1

        text_line = len(res_texts)
        final_result = {
            "text_line": text_line,
            "detail": details
        }

        return final_result, text_line

    def infer(self, img_bgr):
        # 检测 前处理
        img, db_input = self.db_preprocess(img_bgr)

        # 检测 模型推理
        model_infer = det_client.run([db_input])
        det_outputs = model_infer.get_result()
        dbnetmap = det_outputs.as_numpy('sigmoid_0.tmp_0')
        dbnetmap = dbnetmap[0]

        # 检测 后处理
        textboxes, ocr_crops = self.get_cor_crop(img, dbnetmap)

        # 没有检测到框有检测到，直接返回空字典
        if textboxes[0][0] == -1:
            logger.info(f"Reqid: {self.reqid}, no detect text")
            return self.update_final_result([], [], [])
        ocr_crops = np.stack(ocr_crops)

        # 拼成batchsize,输入ppocr推理
        output_idx = []
        output_prob = []
        for batch_ocr_crops in self._batch(ocr_crops, self.batch):
            outputs = ocr_client.run([batch_ocr_crops])
            batch_result = outputs.get_result()

            output_idx.append(batch_result.as_numpy("argmax_0.tmp_0"))
            output_prob.append(batch_result.as_numpy("max_0.tmp_0"))

        output_idx = np.vstack(output_idx)
        output_prob = np.vstack(output_prob)
        preds = [output_idx, output_prob]

        # ocr 后处理
        res_texts = []
        res_text_scores = []
        res_text_boxes = []
        textocr_res = self.ctc_decoder(preds, model_type=self.rec_model_type)

        for textbox, res in zip(textboxes, textocr_res):
            box = [float(o) for o in textbox[2:]]
            # box =textbox[2:]
            text, score = res
            if text == "":
                continue
            res_texts.append(text)
            res_text_scores.append(round(float(score), 4))
            # 浮点数
            res_text_boxes.append([
                [box[0], box[1]],
                [box[2], box[3]],
                [box[4], box[5]],
                [box[6], box[7]],
            ])

        return self.update_final_result(res_texts, res_text_scores, res_text_boxes)

    # 批量文本识别（多张图片）
    def batch_run(self, img_bgr_list):
        final_result = []
        try:
            for idx, img_bgr in enumerate(img_bgr_list):
                res, text_count = self.infer(img_bgr)
                final_result.append(res)
                logger.info(f"Reqid: {self.reqid}, 第{idx + 1}张图片, text_count:{text_count}")
        except Exception as e:
            logger.info(f"Reqid: {self.reqid}, ocr infer failed, {e}")
            raise e

        message = {
            "success": len(final_result), "fail": len(img_bgr_list) - len(final_result)
        }
        return final_result, message, self.save_rule
