Global:
  debug: false
  use_gpu: true
  epoch_num: 200
  log_smooth_window: 20
  print_batch_step: 100
  save_model_dir: ./output/rec_ppocr_v4_hgnet_normal_20240507
  save_epoch_step: 20
  eval_batch_step: [0, 6000]
  cal_metric_during_train: true
  pretrained_model: ./output/rec_ppocr_v4_hgnet_normal_20240227/best_accuracy
  checkpoints: 
  save_inference_dir:
  use_visualdl: false
  infer_img: doc/imgs_words/ch/word_1.jpg
  character_dict_path: ppocr/utils/new_ocr_dict.txt
  max_text_length: &max_text_length 25
  infer_mode: false
  use_space_char: true
  distributed: true
  save_res_path: ./output/rec/predicts_ppocrv4_normal_20240227.txt


Optimizer:
  name: Adam
  beta1: 0.9
  beta2: 0.999
  lr:
    name: Cosine
    learning_rate: 0.0001
    warmup_epoch: 3 #5
  regularizer:
    name: L2
    factor: 3.0e-05


Architecture:
  model_type: rec
  algorithm: SVTR_HGNet
  Transform:
  Backbone:
    name: PPHGNet_small
    freeze_params: true
  Head:
    name: MultiHead
    head_list:
      - CTCHead:
          Neck:
            name: svtr
            dims: 120
            depth: 2
            hidden_dims: 120
            kernel_size: [1, 3]
            use_guide: True
          Head:
            fc_decay: 0.00001
      - NRTRHead:
          nrtr_dim: 384
          max_text_length: *max_text_length

Loss:
  name: MultiLoss
  loss_config_list:
    - CTCLoss:
    - NRTRLoss:

PostProcess:  
  name: CTCLabelDecode

Metric:
  name: RecMetric
  main_indicator: acc

Train:
  dataset:
    name: MultiScaleDataSet
    ds_width: false
    data_dir: /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/rec/train/
    ext_op_transform_idx: 1
    label_file_list:
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/gt_train_lable_all.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/gt_train_fapiao_0719.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/label_verify_0807_train_X10.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/taxi_pp3_pp4server_same_train_0831_X20.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/ccf_taxi_data_train_0906_X20.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/taxi_other_0907_X20.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/taxi_20230913_pp3_pp4server_same_train_X20.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/taxi_data_20230913_other_d_X20.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/Downloader_crop_part_0810_img.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/Downloader_crop_part_0927_img.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/hy_generate_pdf_list.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/hy_generate_pdf_list_0105.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/hy_generate_pdf_list_20240111.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/hy_generate_pdf_list_symbol_20240111.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/hy_generate_pdf_list_20240111_randomfanti.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/hy_generate_pdf_list_20240111_randomsymbol.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/hy_generate_pdf_list_20240118_gongqing.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/hy_generate_pdf_list_20240125_kuohao.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/hy_generate_pdf_list_20240125_kuohao_add.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/hy_generate_pdf_list_20240129_kuohao_add.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/hy_generate_pdf_list_20240227_stamp.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/seal_1993_1868_20240507_X30.txt
    ratio_list: [1, 0.5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
    # ratio_list: [0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1]
    transforms:
    - DecodeImage:
        img_mode: BGR
        channel_first: false
    - RecConAug:
        prob: 0.5
        ext_data_num: 2
        image_shape: [48, 320, 3]
        max_text_length: *max_text_length
    - RecAug:
    - MultiLabelEncode:
        gtc_encode: NRTRLabelEncode
    - KeepKeys:
        keep_keys:
        - image
        - label_ctc
        - label_gtc
        - length
        - valid_ratio
  sampler:
    name: MultiScaleSampler
    scales: [[320, 32], [320, 48], [320, 64]]
    first_bs: &bs 256
    fix_bs: false
    divided_factor: [8, 16] # w, h
    is_training: True
  loader:
    shuffle: true
    batch_size_per_card: *bs
    drop_last: true
    num_workers: 8
Eval:
  dataset:
    name: SimpleDataSet
    data_dir: /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/rec/test/
    label_file_list:
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/gt_test_lable_all.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/gt_test_fapiao_0719.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/label_verify_0807_test.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/taxi_pp3_pp4server_same_test_0907.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/ccf_taxi_data_test_0905.txt
    - /data3/huangyun/PaddleOCR-release-2.7/normal_ocr_train_dataset/taxi_20230913_pp3_pp4server_same_test.txt
    ratio_list: [1, 1, 1, 1, 1, 1]
    transforms:
    - DecodeImage:
        img_mode: BGR
        channel_first: false
    - MultiLabelEncode:
        gtc_encode: NRTRLabelEncode
    - RecResizeImg:
        image_shape: [3, 48, 320]
    - KeepKeys:
        keep_keys:
        - image
        - label_ctc
        - label_gtc
        - length
        - valid_ratio
  loader:
    shuffle: false
    drop_last: false
    batch_size_per_card: 256
    num_workers: 4
