import glob
import os
import sys
import time
from io import BytesIO
import logging
import cv2
import gevent
import gevent.ssl
import numpy as np
import pybase64 as base64
import tritonclient.http as httpclient
from PIL import Image
import yaml
from teleexception import StatusException, HTTPStatus
import fleep
from scipy.special import expit as sigmoid
from shapely.geometry import Polygon

logger = logging.getLogger(__name__)

CONFIG_YAML_PATH = "../../config.yaml"
LOCK_PATH = f'/tmp/init_logger'

with open(
    os.path.join(os.path.dirname(__file__), CONFIG_YAML_PATH), "r", encoding="utf-8"
    ) as f:
    configs = yaml.load(f, Loader=yaml.FullLoader)
    if not os.path.exists(LOCK_PATH) or not os.path.isdir(LOCK_PATH):
        os.makedirs(LOCK_PATH, exist_ok=True)
        logger.info(f"Init, {configs}")

action_configs = configs["action"]      # ACTION
port_configs = configs["ports"]         # 端口配置
models_configs = configs["models"]      # 模型参数配置
process_configs = configs["process"]    # 运行参数配置
params_configs = configs["params"]      # 系统参数配置

# 人车非端口
TRAFFIC_TRITON_URL = os.environ.get(
    'TRAFFIC_TRITON_URL', 'localhost:{}'.format(port_configs["TRAFFIC_TRITON_PORT"])
    )
# 渣土车检测端口
SLAGCAR_TRITON_URL = os.environ.get(
    'SLAGCAR_TRITON_URL', 'localhost:{}'.format(port_configs["SLAGCAR_TRITON_PORT"])
    )

# 是否开启 ROI 区域
# enable 开启 roi，disable 关闭 roi，默认开启
ENABLE_ROI = os.environ.get("ENABLE_ROI", process_configs["ENABLE_ROI"])    

# 人车非检测 模型配置
traffic_configs = models_configs["traffic_plan"]
# 渣土车检测 模型配置
slagcar_plan_configs = models_configs["slagcar_plan"]

class DetectClient():
    def __init__(self, url, model_config):
        self.model_config = model_config
        try:
            if self.model_config["ssl"]:
                self.triton_client = httpclient.InferenceServerClient(
                    url=url,
                    verbose=self.model_config["verbose"],
                    ssl=True,
                    ssl_context_factory=gevent.ssl._create_unverified_context,
                    insecure=True)
            else:
                self.triton_client = httpclient.InferenceServerClient(
                    url=url, verbose=self.model_config["verbose"])
        except Exception as e:
            logger.info("channel creation failed: " + str(e))
            sys.exit(1)
        if self.model_config["http_headers"] is not None:
            self.headers = {
                l.split(':')[0]: l.split(':')[1] for l in self.model_config["http_headers"]
            }
        else:
            self.headers = None
        self.model_name = self.model_config["model_name"]
        self.request_compression_algorithm = self.model_config["request_compression_algorithm"]
        self.response_compression_algorithm = self.model_config["response_compression_algorithm"]

    def run(self, input_data):
        inputs = [httpclient.InferInput(input_info["name"], input_data.shape, input_info["dtype"])
                  for input_info in self.model_config["input"]]
        inputs[0].set_data_from_numpy(input_data, binary_data=True)

        outputs = [
            httpclient.InferRequestedOutput(output_info["name"], binary_data=True) for output_info in self.model_config["output"]
        ]
        query_params = {'test_1': 1, 'test_2': 2}
        results = self.triton_client.async_infer(
            self.model_name,
            inputs,
            outputs=outputs,
            query_params=query_params,
            headers=self.headers,
            request_compression_algorithm=self.request_compression_algorithm,
            response_compression_algorithm=self.response_compression_algorithm)
        return results


# 人车非检测 模型客户端
traffic_client = DetectClient(TRAFFIC_TRITON_URL, traffic_configs)
# 渣土车 模型客户端
slagcar_plan_client = DetectClient(SLAGCAR_TRITON_URL, slagcar_plan_configs)


class SlagcarDetectClient():
    def __init__(self, process_configs, reqid=None):
        self.reqid = reqid
        self.process_configs = process_configs
        self.save_rule = params_configs["SAVE_RULE"]  # 默认保存规则 all

    # opencv 预处理
    def letterbox(
        self,
        img,
        new_shape=(384, 640),
        color=(0, 0, 0),
        auto=False,
        scaleFill=False,
        scaleup=True,
        stride=32,
    ):
        # Resize and pad image while meeting stride-multiple constraints
        shape = img.shape[:2]  # current shape [height, width]
        if isinstance(new_shape, int):
            new_shape = (new_shape, new_shape)

        # Scale ratio (new / old)
        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
        # only scale down, do not scale up (for better test mAP)
        if not scaleup:
            r = min(r, 1.0)

        # Compute padding
        ratio = r, r  # width, height ratios
        new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
        dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # wh padding
        if auto:  # minimum rectangle
            dw, dh = np.mod(dw, stride), np.mod(dh, stride)  # wh padding
        elif scaleFill:  # stretch
            dw, dh = 0.0, 0.0
            new_unpad = (new_shape[1], new_shape[0])
            ratio = (
                new_shape[1] / shape[1],
                new_shape[0] / shape[0],
            )  # width, height ratios

        dw /= 2  # divide padding into 2 sides
        dh /= 2

        if shape[::-1] != new_unpad:  # resize
            img = cv2.resize(img, new_unpad, interpolation=cv2.INTER_LINEAR)
        top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
        left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
        img = cv2.copyMakeBorder(
            img, 0, bottom + top, 0, right + left, cv2.BORDER_CONSTANT, value=color
        )  # add border
        return img, ratio, (0, 0)
    
    # 人车非检测 前处理
    def _preprocess_traffic(self, input_image):
        img = self.letterbox(input_image)[0]

        # img = img[:, :, ::-1].transpose(2, 0, 1)  # BGR to RGB
        img = img.transpose(2, 0, 1)  # 已经是 RGB
        img = np.ascontiguousarray(img)

        img = img.astype(np.float32)
        # img /= 255.0  # 0 - 255 to 0.0 - 1.0

        if len(img) == 3:
            img = np.expand_dims(img, axis=0)

        return img

    # 计算两个多边形是否有交集
    @staticmethod
    def get_polygon_inter(points1, points2):
        """
        get intersection between polygon;
        :param points1: base points eg. [(344,362),(767,370),(731,698),(274,691)]
        :param points2: targets, eg. [235, 257, 873, 720]
        :return:
        """
        polygon = Polygon(points1)
        x1, y1, x2, y2 = points2
        target = Polygon([(x1, y1), (x2, y1), (x2, y2), (x1, y2)]).buffer(0.001)
        intersection = polygon.intersection(target)
        return intersection.area > 0

    # 人车非检测 后处理
    def _postprocess_traffic(self, traffic_detect_results, input_h, input_w):
        counts = traffic_detect_results.as_numpy("count")[0]
        boxes = traffic_detect_results.as_numpy("box")[0]
        scores = traffic_detect_results.as_numpy("score")[0]
        classes = traffic_detect_results.as_numpy("class")[0]

        num_dets = counts.squeeze()
        boxes = boxes[:num_dets]
        scores = scores[:num_dets]
        classes = classes[:num_dets]

        
        # logger.info(
        #     f"Reqid: {self.reqid}, num_dets is : {num_dets}; boxes is : {boxes}; classes is : {classes}; "
        # )

        ratio = (
            self.image_h / input_h
            if self.image_h / input_h > self.image_w / input_w
            else self.image_w / input_w
        )
        boxes *= ratio
        boxes = boxes.astype(np.int32)
        classes = classes.astype(np.int32)

        filter_boxes = []
        filter_scores = []
        filter_classes = []

        for i in range(len(classes)):
            # 类别过滤
            if classes[i] not in process_configs["KEEP_CLS_LIST"]:
                logger.info(
                    f"Reqid: {self.reqid}, traffic is filtered by class: {classes[i]}"
                )
                continue

            # 阈值过滤
            traffic_thresh = self.process_configs["TRAFFIC_THRESH"]
            if scores[i] < traffic_thresh:
                logger.info(
                    f"Reqid: {self.reqid}, class: {classes[i]}, score: {scores[i]:.2f}, box is filtered by thresh: {traffic_thresh}"
                )
                continue

        
            box = boxes[i]

            if ENABLE_ROI == "enable":  # 开启 ROI 判断
                # 计算是否满足ROI区域
                # [[0, 0], [0, 50], [50, 0], [50, 50]] 非凸多边形
                try:
                    state = self.get_polygon_inter(self.Roi, box)
                except Exception:
                    logger.info(f"Reqid: {self.reqid}, R0I not polygon, return code: 602001")
                    raise StatusException(HTTPStatus.ROI_POLYGON)
                if state is False:  # 无交集，过滤
                    logger.info(f"Reqid: {self.reqid}, R0I no intersection filter, box:{box}")
                    continue

            # 检测框比例过滤
            box_w = (boxes[i][2] - boxes[i][0]) / self.image_w
            box_h = (boxes[i][3] - boxes[i][1]) / self.image_h
            min_w = process_configs["BOX_THRESH_RATE_DICT"]["BOX_MIN_W_RATE"]
            max_w = process_configs["BOX_THRESH_RATE_DICT"]["BOX_MAX_W_RATE"]
            min_h = process_configs["BOX_THRESH_RATE_DICT"]["BOX_MIN_H_RATE"]
            max_h = process_configs["BOX_THRESH_RATE_DICT"]["BOX_MAX_H_RATE"]
            if not (min_w <= box_w <= max_w and min_h <= box_h <= max_h):
                logger.info(
                    f"Reqid: {self.reqid}, box is filtered by box rate, box_w: {box_w:.3f}, min_w: {min_w:.3f}, max_w: {max_w:.3f}, box_h: {box_h:.3f}, min_h: {min_h:.3f}, max_h: {max_h:.3f}"
                )
                continue

            filter_boxes.append(boxes[i])
            filter_scores.append(scores[i])
            filter_classes.append(classes[i])

        return filter_boxes, filter_scores, filter_classes

    def _batch(self, iterable, n=1):
        l = len(iterable)
        for ndx in range(0, l, n):
            yield iterable[ndx: min(ndx + n, l)]

    def letterbox_slagcar(self, img, new_shape,  color=(0, 0, 0)):
        shape = img.shape[:2]  # current shape [height, width]
        ratio = min(new_shape[1] / shape[0], new_shape[0] / shape[1])
        new_width = int(shape[1] * ratio)
        new_height = int(shape[0] * ratio)
        img = cv2.resize(img, (new_width, new_height))
        pad_x = (new_shape[0] - new_width) * 0.5
        pad_y = (new_shape[1] - new_height) * 0.5
        left, right = round(pad_x + 0.1), round(pad_x - 0.1)
        top, bottom = round(pad_y + 0.1), round(pad_y - 0.1)
        img = cv2.copyMakeBorder(img, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)
        return img

    # 渣土车结果 前处理
    def _preprocess_slagcar_plan(self, image, boxes, input_h, input_w):
        padded_images = []
        for box in boxes:
            left, top, right, bottom = box
            top = int(max(0, top))
            left = int(max(0, left))
            bottom = int(min(image.shape[0], bottom))
            right = int(min(image.shape[1], right))
            img = image[top:bottom, left:right]
            # img = image
            h, w, c = img.shape
            # Resize the image with long side while maintaining ratio
            # img = cv2.resize(img, (input_w, input_h), interpolation=cv2.INTER_LINEAR)
            # BGR -> RGB
            img = self.letterbox_slagcar(img, (input_w, input_h))
            # img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            img = img.astype(np.float32)
            # Normalize to [0,1]
            img -= np.array([127.5, 127.5, 127.5])
            img /= np.array([127.5, 127.5, 127.5])
            # HWC to CHW format:
            img = np.transpose(img, [2, 0, 1])
            # Convert the image to row-major order, also known as "C order":
            img = np.ascontiguousarray(img)
            padded_images.append(img)
        return np.stack(padded_images)

    def _get_cate_score(self, cates, res, filterIndexList):
        classIndex = np.argmax(res)
        # logger.info(f"res: {res}")
        # logger.info(f"classIndex: {classIndex}")
        if classIndex not in filterIndexList:
            return None, None
        cate_name = cates[classIndex]
        cate_score = self._softmax(res)
        # cate_score = np.argmax(res)
        return cate_name, cate_score[classIndex]
    
    def _softmax(self, x):
        return np.exp(x) / np.sum(np.exp(x), axis=0)

    # 渣土车结果 后处理
    def _postprocess_slagcar_plan(self, output):
        
        final_result = []
        BODY_CATES = self.process_configs["BODY_CATES"]
        for out in output:
            batch_size = out[0].shape[0]
            for idx in range(batch_size):
                # 得到车辆类别信息
                carType, carType_score = self._get_cate_score(BODY_CATES["carType"], out[0][idx], BODY_CATES["carFliter"])
                if not carType:
                    result = {"Name": '', "Score": 0.,}
                else:
                    result = {"Name": carType, "Score": round(float(carType_score), 4),}
                final_result.append(result)
        return final_result
    
    # 结果封装
    def update_final_result(self, final_result):
        attributes_list = []
        fix_boxes = []
        fix_scores = []

        boxes = final_result["box"]
        properties = final_result["property"]
        
        for det, _property in zip(boxes, properties):
            if not _property["Name"]:
                continue
            box = det[:4]
            x1, y1, x2, y2 = [int(v) if int(v) > 0 else 0 for v in box]
            new_box = [x1, y1, x2-x1, y2-y1]  # x, y, w, h
            fix_boxes.append(new_box)
            score = round(float(det[5]), 4)
            fix_scores.append(score)
            # cls = int(det[4])
            slagcar_cls = _property["Name"]
            attributes_list.append(slagcar_cls)

        final_result = {'DetectCount': len(fix_boxes),
                        'DetectBoxes': fix_boxes,
                        'DetectScores': fix_scores,
                        'DetectClses': attributes_list
                        }

        # 返回数量小于等于 SAVE_NUM，即不保存请求
        if len(fix_boxes) <= params_configs["SAVE_NUM"]:
            self.save_rule = "none"

        logger.info(
            f'Reqid: {self.reqid}, DetectCount:{len(fix_boxes)}, SaveRule:{self.save_rule}')

        return final_result, self.save_rule

    def run(self, img_arr, img_mat, traffic_thresh=process_configs["TRAFFIC_THRESH"], Roi=None):
        # 参数初始化
        self.image_h, self.image_w = img_mat.shape[:2]  # 图片长宽 opencv
        self.traffic_thresh = traffic_thresh  # 人车非阈值
        self.Roi = Roi
        logger.info(f"Reqid: {self.reqid}, traffic_thresh: {self.traffic_thresh}, Roi: {self.Roi}")
        final_result = {"box": [], "property": []}

        # 人车非检测 前处理
        input_data = self._preprocess_traffic(img_mat)
        # input_data = np.expand_dims(img_arr, axis=0)

        # 人车非检测 模型推理
        traffic_results = traffic_client.run(input_data)
        traffic_detect_results = traffic_results.get_result()


        # 人车非检测 后处理
        boxes, scores, classes = self._postprocess_traffic(
            traffic_detect_results, 
            traffic_configs["input_shape"][0]["input_h"], 
            traffic_configs["input_shape"][0]["input_w"]
            )
        
        # logger.info(f"{boxes}")
        
        # 没有检测车
        if len(boxes) == 0:
            logger.info(f"Reqid: {self.reqid}, no detect traffic")
            return self.update_final_result(final_result)
        
        # logger.info(f"boxes: {boxes}")

        # 渣土车检测 前处理
        padded_images = self._preprocess_slagcar_plan(img_mat, boxes,
                                               slagcar_plan_configs["input_shape"][0]["input_h"],
                                               slagcar_plan_configs["input_shape"][0]["input_w"]
                                               )
        
        
        output = []
        # 渣土车检测 模型推理
        for batch_paded_images in self._batch(padded_images, slagcar_plan_configs["max_batch_size"]):
            slagcar_plan_result = slagcar_plan_client.run(batch_paded_images)
            batch_result = slagcar_plan_result.get_result()
            output_tmp = []
            output_tmp.append(batch_result.as_numpy('output'))
            
            output.append(output_tmp)

        

        # 渣土车检测 后处理
        slagcar_plan_property = self._postprocess_slagcar_plan(output)

        # logger.info(f"slagcar_plan_property: {slagcar_plan_property}")

        for i in range(len(boxes)):
            box = boxes[i].tolist()
            box.append(classes[i])
            box.append(scores[i])
            final_result["box"].append(box)
        final_result["property"] = slagcar_plan_property

        return self.update_final_result(final_result)


class FileFormatValidation:
    @staticmethod
    def validate(file: bytes, mime_matches):
        if file is None or len(file) <= 128:
            return False

        info = fleep.get(file[:128])
        for mime in mime_matches:
            if info.mime_matches(mime):
                return True
        return False

    @staticmethod
    def convert_to_png(self):
        im = Image.open(BytesIO(self.file))
        byte_io = BytesIO()
        im.save(byte_io, 'PNG')
        self.cleaned_image = byte_io.getvalue()


def check_params(reqid, params):
    if not isinstance(params, dict):
        logger.info(f'Reqid: {reqid}, slagcar_plan type err, return code: 400005')
        raise StatusException(HTTPStatus.BODY_TYPE_ERR)
    Action = params.get('Action', None)
    ImageData = params.get('ImageData', None)
    if Action is None or ImageData is None:
        logger.info(
            f'Reqid: {reqid}, Action or ImageData missing, return code: 400006')
        raise StatusException(HTTPStatus.MUST_PRAM_ERR)
    if not isinstance(Action, str) or not isinstance(ImageData, str):
        logger.info(
            f'Reqid: {reqid}, Action or ImageData type err, return code: 400008')
        raise StatusException(HTTPStatus.PRAM_TYPE_ERR)
    if not Action or not ImageData:
        logger.info(
            f'Reqid: {reqid}, Action or ImageData empty, return code: 400009')
        raise StatusException(HTTPStatus.IMAGE_DATA_AND_ACTION_EMPTY_ERR)
    if Action != action_configs:  # ACTION
        logger.info(f'Reqid: {reqid}, Action value err, return code: 400010')
        raise StatusException(HTTPStatus.ACTION_VALUE_ERR)
    try:
        img_byte = base64.urlsafe_b64decode(ImageData)
    except Exception as e:
        logger.info(
            f'Reqid: {reqid}, decode image base64 err: {e}, return code: 400011')
        raise StatusException(HTTPStatus.IMAGE_DATA_BASE64_ERR)

    if not FileFormatValidation.validate(
            img_byte, params_configs['FILE_FORMAT']):
        logger.info(f'Reqid: {reqid}, image format err, return code: 400012')
        raise StatusException(HTTPStatus.IMAGE_TYPE_ERR)

    if len(img_byte) > params_configs['IMAGE_SIZE']:
        logger.info(f'Reqid: {reqid}, image size err, return code: 400013')
        raise StatusException(HTTPStatus.IMAGE_SIZE_ERR)

    try:
        img_arr = np.frombuffer(img_byte, np.uint8)
        img_mat = cv2.cvtColor(cv2.imdecode(
            img_arr, cv2.IMREAD_COLOR), cv2.COLOR_BGR2RGB)
        height, width = img_mat.shape[:2]
    except Exception as e:
        logger.info(
            f'Reqid: {reqid}, decode image err: {e}, return code: 410001')
        raise StatusException(HTTPStatus.IMAGE_DECODE_ERR)

    if not (params_configs['MIN_LEN'] <= height <= params_configs['MAX_LEN'] and
            params_configs['MIN_LEN'] <= width <= params_configs['MAX_LEN']):
        logger.info(f'Reqid: {reqid}, image shape err, return code: 410002')
        raise StatusException(HTTPStatus.IMAGE_SHAPE_ERR)


# Roi 区域判断
    if ENABLE_ROI == "enable":  # 开启 ROI 判断
        # 默认全图
        Roi = params.get(
            "Roi", [[0, 0], [0, height], [width, height], [width, 0]]
        )
        logger.info(f"Reqid: {reqid}, Roi: {Roi}")

        if not isinstance(Roi, list):
            # print(type(Roi))
            logger.info(f"Reqid: {reqid}, Roi type err, return code: 400008")
            raise StatusException(HTTPStatus.ROI_LIST_TYPE_ERR)
        for item in Roi:
            if len(item) == 2:
                if not isinstance(item[0], int) or not isinstance(item[1], int):
                    logger.info(
                        f"Reqid: {reqid}, Roi item type err, return code: 400008"
                    )
                    raise StatusException(HTTPStatus.ROI_ITEM_INT_TYPE_ERR)
            else:
                logger.info(f"Reqid: {reqid}, Roi item len err, return code: 400008")
                raise StatusException(HTTPStatus.ROI_ITEM_INT_TYPE_ERR)
        if not 3 < len(Roi) < 10:
            logger.info(
                f"Reqid: {reqid}, Roi lenghth value more than 3 but less than 10 , return code: 400019"
            )
            raise StatusException(HTTPStatus.ROI_LENGTH_ERR)
    else:
        # 默认全图
        Roi = [[0, 0], [0, height], [width, height], [width, 0]]
        logger.info(f"Reqid: {reqid}, Roi: {Roi}")

    # 业务字段 人车非分数阈值
    score_thresh = params.get("ScoreThresh", process_configs["TRAFFIC_THRESH"])
    if not (isinstance(score_thresh, int) or isinstance(score_thresh, float)):
        logger.info(
            f"Reqid: {reqid}, ScoreThresh data type is illegal, return code: 400008"
        )
        raise StatusException(HTTPStatus.SCORE_THRESH_FLOAT_TYPE_ERR)
    if not 0 <= score_thresh <= 1:
        logger.info(f"Reqid: {reqid}, ScoreThresh data is illegal, return code: 400010")
        raise StatusException(HTTPStatus.SCORE_THRESH_VALUE_ERR)

    return img_arr, img_mat, score_thresh, Roi



def run(reqid, params):
    time_0 = time.time()
    img_arr, img_mat, traffic_thresh, Roi  = check_params(
        reqid, params)
    client = SlagcarDetectClient(process_configs, reqid)
    time_1 = time.time()
    results, save_rule = client.run(img_arr, img_mat, traffic_thresh, Roi)
    time_2 = time.time()
    logger.info(
        f'Reqid: {reqid}, check params: {(time_1 - time_0) * 1000} ms, infer time: {(time_2 - time_1) * 1000} ms')

    return results, save_rule


if __name__ == "__main__":

    logging.basicConfig(
        level=logging.DEBUG, format='%(asctime)s %(levelname)s %(filename)s:%(lineno)d - %(message)s')

    client = SlagcarDetectClient(process_configs)

    image_paths = glob.glob("../../test/R-C.jpg")
    image_paths.sort()

    for image_path in image_paths:
        print(image_path)
        with open(image_path, 'rb') as file:
            img_arr = np.frombuffer(file.read(), np.uint8)
        img_mat = cv2.cvtColor(cv2.imdecode(
            img_arr, cv2.IMREAD_COLOR), cv2.COLOR_BGR2RGB)
        t1 = time.time()
        # final_result, _ = client.run(img_arr, img_mat)
        img_h, img_w = img_mat.shape[:2]
        final_result, _ = client.run(img_arr,img_mat, Roi=[[0, 0], [0, img_h], [img_w, img_h], [img_w, 0]])
        

        print(final_result)
        # final_result, _ = client.run(img_arr, img_mat, traffic_thresh=0)
        # print(final_result)
        t2 = time.time()
        print("cost %.2fms" % ((t2-t1)*1000))

 